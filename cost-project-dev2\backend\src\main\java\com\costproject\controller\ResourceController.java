package com.costproject.controller;

import com.costproject.entity.Resource;
import com.costproject.entity.ResourcePrice;
import com.costproject.service.ResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/resources")
@Api(tags = "资源管理")
public class ResourceController {
    
    private static final Logger log = LoggerFactory.getLogger(ResourceController.class);

    private final ResourceService resourceService;
    
    public ResourceController(ResourceService resourceService) {
        this.resourceService = resourceService;
    }

    @PostMapping
    @ApiOperation("创建资源")
    public ResponseEntity<Resource> createResource(
            @ApiParam(value = "资源信息", required = true)
            @Valid @RequestBody Resource resource) {
        return ResponseEntity.ok(resourceService.createResource(resource));
    }

    @PutMapping("/{id}")
    @ApiOperation("更新资源")
    public ResponseEntity<Resource> updateResource(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "资源信息", required = true)
            @Valid @RequestBody Resource resource) {
        return ResponseEntity.ok(resourceService.updateResource(id, resource));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除资源")
    public ResponseEntity<Void> deleteResource(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable Long id) {
        resourceService.deleteResource(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取资源详情")
    public ResponseEntity<Resource> getResource(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable Long id) {
        return ResponseEntity.ok(resourceService.getResource(id));
    }

    @GetMapping
    @ApiOperation("查询资源列表")
    public ResponseEntity<List<Resource>> searchResources(
            @ApiParam(value = "关键词")
            @RequestParam(required = false) String keyword,
            @ApiParam(value = "资源类型")
            @RequestParam(required = false) Resource.ResourceType type) {
        return ResponseEntity.ok(resourceService.searchResources(keyword, type));
    }

    @PostMapping("/{id}/price")
    @ApiOperation("更新资源价格")
    public ResponseEntity<Void> updatePrice(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "价格", required = true)
            @RequestParam BigDecimal price,
            @ApiParam(value = "生效时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime effectiveDate) {
        resourceService.updatePrice(id, price, effectiveDate);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/price-history")
    @ApiOperation("获取价格历史")
    public ResponseEntity<List<ResourcePrice>> getPriceHistory(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable Long id) {
        return ResponseEntity.ok(resourceService.getPriceHistory(id));
    }

    @PostMapping("/{resourceId}/suppliers/{supplierId}")
    @ApiOperation("添加供应商")
    public ResponseEntity<Void> addSupplier(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable Long resourceId,
            @ApiParam(value = "供应商ID", required = true)
            @PathVariable Long supplierId) {
        resourceService.addSupplier(resourceId, supplierId);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/cost-breakdown")
    @ApiOperation("更新成本拆分")
    public ResponseEntity<Void> updateCostBreakdown(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "成本拆分信息", required = true)
            @RequestBody Resource.CostBreakdown breakdown) {
        resourceService.updateCostBreakdown(id, breakdown);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/usage")
    @ApiOperation("更新资源使用量")
    public ResponseEntity<Void> updateUsage(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "使用量", required = true)
            @RequestParam BigDecimal quantity) {
        resourceService.updateUsage(id, quantity);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/stock")
    @ApiOperation("检查资源库存")
    public ResponseEntity<Map<String, Object>> checkStock(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "需求量", required = true)
            @RequestParam BigDecimal requiredQuantity) {
        return ResponseEntity.ok(resourceService.checkStock(id, requiredQuantity));
    }

    @GetMapping("/{id}/usage-report")
    @ApiOperation("生成资源使用报告")
    public ResponseEntity<Map<String, Object>> generateUsageReport(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable Long id) {
        return ResponseEntity.ok(resourceService.generateUsageReport(id));
    }

    @GetMapping("/alerts")
    @ApiOperation("生成资源预警")
    public ResponseEntity<List<Map<String, Object>>> generateAlerts() {
        return ResponseEntity.ok(resourceService.generateAlerts());
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleException(Exception e) {
        log.error("处理请求时发生错误", e);
        return ResponseEntity.badRequest().body(e.getMessage());
    }
}
