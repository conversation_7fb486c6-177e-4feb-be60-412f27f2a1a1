{"ast": null, "code": "export const COMMON_OPTIONS = {\n  autoResize: true,\n  behaviors: [{\n    key: 'zoom-canvas',\n    type: 'zoom-canvas'\n  }, {\n    key: 'drag-canvas',\n    type: 'drag-canvas'\n  }]\n};", "map": {"version": 3, "names": ["COMMON_OPTIONS", "autoResize", "behaviors", "key", "type"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/graphs/es/core/constants/options.js"], "sourcesContent": ["export const COMMON_OPTIONS = {\n    autoResize: true,\n    behaviors: [\n        {\n            key: 'zoom-canvas',\n            type: 'zoom-canvas',\n        },\n        {\n            key: 'drag-canvas',\n            type: 'drag-canvas',\n        },\n    ],\n};\n"], "mappings": "AAAA,OAAO,MAAMA,cAAc,GAAG;EAC1BC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,CACP;IACIC,GAAG,EAAE,aAAa;IAClBC,IAAI,EAAE;EACV,CAAC,EACD;IACID,GAAG,EAAE,aAAa;IAClBC,IAAI,EAAE;EACV,CAAC;AAET,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}