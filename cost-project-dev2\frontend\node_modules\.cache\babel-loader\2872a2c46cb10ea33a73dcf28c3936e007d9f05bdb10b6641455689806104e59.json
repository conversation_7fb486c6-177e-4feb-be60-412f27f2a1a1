{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CrownOutlinedSvg from \"@ant-design/icons-svg/es/asn/CrownOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CrownOutlined = function CrownOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CrownOutlinedSvg\n  }));\n};\n\n/**![crown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5OS42IDI3Ni41TDcwNSAzOTYuNCA1MTguNCAxNDcuNWE4LjA2IDguMDYgMCAwMC0xMi45IDBMMzE5IDM5Ni40IDEyNC4zIDI3Ni41Yy01LjctMy41LTEzLjEgMS4yLTEyLjIgNy45TDE4OC41IDg2NWMxLjEgNy45IDcuOSAxNCAxNiAxNGg2MTUuMWM4IDAgMTQuOS02IDE1LjktMTRsNzYuNC01ODAuNmMuOC02LjctNi41LTExLjQtMTIuMy03Ljl6bS0xMjYgNTM0LjFIMjUwLjNsLTUzLjgtNDA5LjQgMTM5LjggODYuMUw1MTIgMjUyLjlsMTc1LjcgMjM0LjQgMTM5LjgtODYuMS01My45IDQwOS40ek01MTIgNTA5Yy02Mi4xIDAtMTEyLjYgNTAuNS0xMTIuNiAxMTIuNlM0NDkuOSA3MzQuMiA1MTIgNzM0LjJzMTEyLjYtNTAuNSAxMTIuNi0xMTIuNlM1NzQuMSA1MDkgNTEyIDUwOXptMCAxNjAuOWMtMjYuNiAwLTQ4LjItMjEuNi00OC4yLTQ4LjMgMC0yNi42IDIxLjYtNDguMyA0OC4yLTQ4LjNzNDguMiAyMS42IDQ4LjIgNDguM2MwIDI2LjYtMjEuNiA0OC4zLTQ4LjIgNDguM3oiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CrownOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CrownOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "CrownOutlinedSvg", "AntdIcon", "CrownOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/CrownOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CrownOutlinedSvg from \"@ant-design/icons-svg/es/asn/CrownOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CrownOutlined = function CrownOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CrownOutlinedSvg\n  }));\n};\n\n/**![crown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5OS42IDI3Ni41TDcwNSAzOTYuNCA1MTguNCAxNDcuNWE4LjA2IDguMDYgMCAwMC0xMi45IDBMMzE5IDM5Ni40IDEyNC4zIDI3Ni41Yy01LjctMy41LTEzLjEgMS4yLTEyLjIgNy45TDE4OC41IDg2NWMxLjEgNy45IDcuOSAxNCAxNiAxNGg2MTUuMWM4IDAgMTQuOS02IDE1LjktMTRsNzYuNC01ODAuNmMuOC02LjctNi41LTExLjQtMTIuMy03Ljl6bS0xMjYgNTM0LjFIMjUwLjNsLTUzLjgtNDA5LjQgMTM5LjggODYuMUw1MTIgMjUyLjlsMTc1LjcgMjM0LjQgMTM5LjgtODYuMS01My45IDQwOS40ek01MTIgNTA5Yy02Mi4xIDAtMTEyLjYgNTAuNS0xMTIuNiAxMTIuNlM0NDkuOSA3MzQuMiA1MTIgNzM0LjJzMTEyLjYtNTAuNSAxMTIuNi0xMTIuNlM1NzQuMSA1MDkgNTEyIDUwOXptMCAxNjAuOWMtMjYuNiAwLTQ4LjItMjEuNi00OC4yLTQ4LjMgMC0yNi42IDIxLjYtNDguMyA0OC4yLTQ4LjNzNDguMiAyMS42IDQ4LjIgNDguM2MwIDI2LjYtMjEuNiA0OC4zLTQ4LjIgNDguM3oiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CrownOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CrownOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,4CAA4C;AACzE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,aAAa,CAAC;AAC1D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,eAAe;AACvC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}