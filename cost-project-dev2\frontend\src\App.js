import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import 'antd/dist/reset.css';

import MainLayout from './components/layout/MainLayout';
import HomePage from './pages/HomePage';
import ProjectList from './pages/project/ProjectList';
import ProjectDetail from './pages/project/ProjectDetail';
import CostAnalysis from './pages/cost/CostAnalysis';
import BillManagement from './pages/cost/BillManagement';
import QuotaManagement from './pages/cost/QuotaManagement';
import ResourceManagement from './pages/cost/ResourceManagement';
import CalendarPage from './pages/calendar/CalendarPage';
import GanttPage from './pages/gantt/GanttPage';
import StagewiseToolbar from './components/StagewiseToolbar';
import ErrorBoundary from './components/ErrorBoundary';
import { setupStagewise } from './utils/stagewise';

function App() {
  useEffect(() => {
    // 初始化Stagewise工具条
    setupStagewise();
  }, []);

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1890ff',
        },
      }}
    >
      <AntdApp>
        <Router>
          <ErrorBoundary>
            <MainLayout>
              <Routes>
                <Route path="/" element={<HomePage />} />
                <Route path="/projects" element={<ProjectList />} />
                <Route path="/projects/:id" element={<ProjectDetail />} />
                <Route path="/cost/analysis" element={<ErrorBoundary><CostAnalysis /></ErrorBoundary>} />
                <Route path="/cost/bills" element={<ErrorBoundary><BillManagement /></ErrorBoundary>} />
                <Route path="/cost/quotas" element={<ErrorBoundary><QuotaManagement /></ErrorBoundary>} />
                <Route path="/cost/resources" element={<ErrorBoundary><ResourceManagement /></ErrorBoundary>} />
                <Route path="/calendar" element={<CalendarPage />} />
                <Route path="/gantt" element={<GanttPage />} />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </MainLayout>
            <StagewiseToolbar />
          </ErrorBoundary>
        </Router>
      </AntdApp>
    </ConfigProvider>
  );
}

export default App;