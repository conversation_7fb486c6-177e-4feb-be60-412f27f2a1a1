{"ast": null, "code": "import EventEmitter from '@antv/event-emitter';\nimport { GraphEvent } from '../../constants';\nimport { HistoryEvent } from '../../constants/events/history';\nimport { idsOf } from '../../utils/id';\nimport { BasePlugin } from '../base-plugin';\nimport { parseCommand } from './util';\n/**\n * <zh/> 历史记录\n *\n * <en/> History\n * @remarks\n * <zh/> 历史记录用于记录图的数据变化，支持撤销和重做等操作。\n *\n * <en/> History is used to record data changes in the graph and supports operations such as undo and redo.\n */\nexport class History extends BasePlugin {\n  constructor(context, options) {\n    super(context, Object.assign({}, History.defaultOptions, options));\n    this.batchChanges = null;\n    this.batchAnimation = false;\n    this.undoStack = [];\n    this.redoStack = [];\n    this.freezed = false;\n    this.executeCommand = (cmd, revert = true) => {\n      var _a, _b, _c;\n      this.freezed = true;\n      (_b = (_a = this.options).executeCommand) === null || _b === void 0 ? void 0 : _b.call(_a, cmd);\n      const values = revert ? cmd.original : cmd.current;\n      this.context.graph.addData(values.add);\n      this.context.graph.updateData(values.update);\n      this.context.graph.removeData(idsOf(values.remove, false));\n      (_c = this.context.element) === null || _c === void 0 ? void 0 : _c.draw({\n        silence: true,\n        animation: cmd.animation\n      });\n      this.freezed = false;\n    };\n    this.addCommand = event => {\n      var _a;\n      if (this.freezed) return;\n      if (event.type === GraphEvent.AFTER_DRAW) {\n        const {\n          dataChanges = [],\n          animation = true\n        } = event.data;\n        if ((_a = this.context.batch) === null || _a === void 0 ? void 0 : _a.isBatching) {\n          if (!this.batchChanges) return;\n          this.batchChanges.push(dataChanges);\n          this.batchAnimation && (this.batchAnimation = animation);\n          return;\n        }\n        this.batchChanges = [dataChanges];\n        this.batchAnimation = animation;\n      }\n      this.undoStackPush(parseCommand(this.batchChanges.flat(), this.batchAnimation, this.context));\n      this.notify(HistoryEvent.ADD, this.undoStack[this.undoStack.length - 1]);\n    };\n    this.initBatchCommand = event => {\n      const {\n        initiate\n      } = event.data;\n      this.batchAnimation = false;\n      if (initiate) {\n        this.batchChanges = [];\n      } else {\n        const cmd = this.undoStack.pop();\n        if (!cmd) this.batchChanges = null;\n      }\n    };\n    this.emitter = new EventEmitter();\n    const {\n      graph\n    } = this.context;\n    graph.on(GraphEvent.AFTER_DRAW, this.addCommand);\n    graph.on(GraphEvent.BATCH_START, this.initBatchCommand);\n    graph.on(GraphEvent.BATCH_END, this.addCommand);\n  }\n  /**\n   * <zh/> 是否可以执行撤销操作\n   *\n   * <en/> Whether undo can be done\n   * @returns <zh/> 是否可以执行撤销操作 | <en/> Whether undo can be done\n   */\n  canUndo() {\n    return this.undoStack.length > 0;\n  }\n  /**\n   * <zh/> 是否可以执行重做操作\n   *\n   * <en/> Whether redo can be done\n   * @returns <zh/> 是否可以执行重做操作 | <en/> Whether redo can be done\n   */\n  canRedo() {\n    return this.redoStack.length > 0;\n  }\n  /**\n   * <zh/> 执行撤销\n   *\n   * <en/> Execute undo\n   * @returns <zh/> 返回当前实例 | <en/> Return the current instance\n   */\n  undo() {\n    var _a, _b, _c, _d;\n    const cmd = this.undoStack.pop();\n    if (cmd) {\n      this.executeCommand(cmd);\n      const before = (_b = (_a = this.options).beforeAddCommand) === null || _b === void 0 ? void 0 : _b.call(_a, cmd, false);\n      if (before === false) return;\n      this.redoStack.push(cmd);\n      (_d = (_c = this.options).afterAddCommand) === null || _d === void 0 ? void 0 : _d.call(_c, cmd, false);\n      this.notify(HistoryEvent.UNDO, cmd);\n    }\n    return this;\n  }\n  /**\n   * <zh/> 执行重做\n   *\n   * <en/> Execute redo\n   * @returns <zh/> 返回当前实例 | <en/> Return the current instance\n   */\n  redo() {\n    const cmd = this.redoStack.pop();\n    if (cmd) {\n      this.executeCommand(cmd, false);\n      this.undoStackPush(cmd);\n      this.notify(HistoryEvent.REDO, cmd);\n    }\n    return this;\n  }\n  /**\n   * <zh/> 执行撤销且不计入历史记录\n   *\n   * <en/> Execute undo and do not record in history\n   * @returns <zh/> 返回当前实例 | <en/> Return the current instance\n   */\n  undoAndCancel() {\n    const cmd = this.undoStack.pop();\n    if (cmd) {\n      this.executeCommand(cmd, false);\n      this.redoStack = [];\n      this.notify(HistoryEvent.CANCEL, cmd);\n    }\n    return this;\n  }\n  undoStackPush(cmd) {\n    var _a, _b, _c, _d;\n    const {\n      stackSize\n    } = this.options;\n    if (stackSize !== 0 && this.undoStack.length >= stackSize) {\n      this.undoStack.shift();\n    }\n    const before = (_b = (_a = this.options).beforeAddCommand) === null || _b === void 0 ? void 0 : _b.call(_a, cmd, true);\n    if (before === false) return;\n    this.undoStack.push(cmd);\n    (_d = (_c = this.options).afterAddCommand) === null || _d === void 0 ? void 0 : _d.call(_c, cmd, true);\n  }\n  /**\n   * <zh/> 清空历史记录\n   *\n   * <en/> Clear history\n   */\n  clear() {\n    this.undoStack = [];\n    this.redoStack = [];\n    this.batchChanges = null;\n    this.batchAnimation = false;\n    this.notify(HistoryEvent.CLEAR, null);\n  }\n  notify(event, cmd) {\n    this.emitter.emit(event, {\n      cmd\n    });\n    this.emitter.emit(HistoryEvent.CHANGE, {\n      cmd\n    });\n  }\n  /**\n   * <zh/> 监听历史记录事件\n   *\n   * <en/> Listen to history events\n   * @param event  - <zh/> 事件名称 | <en/> Event name\n   * @param handler - <zh/> 事件处理函数 | <en/> Event handler\n   */\n  on(event, handler) {\n    this.emitter.on(event, handler);\n  }\n  /**\n   * <zh/> 销毁\n   *\n   * <en/> Destroy\n   * @internal\n   */\n  destroy() {\n    const {\n      graph\n    } = this.context;\n    graph.off(GraphEvent.AFTER_DRAW, this.addCommand);\n    graph.off(GraphEvent.BATCH_START, this.initBatchCommand);\n    graph.off(GraphEvent.BATCH_END, this.addCommand);\n    this.emitter.off();\n    super.destroy();\n    this.undoStack = [];\n    this.redoStack = [];\n  }\n}\nHistory.defaultOptions = {\n  stackSize: 0\n};", "map": {"version": 3, "names": ["EventEmitter", "GraphEvent", "HistoryEvent", "idsOf", "BasePlugin", "parseCommand", "History", "constructor", "context", "options", "Object", "assign", "defaultOptions", "batchChanges", "batchAnimation", "undoStack", "redoStack", "freezed", "executeCommand", "cmd", "revert", "_b", "_a", "call", "values", "original", "current", "graph", "addData", "add", "updateData", "update", "removeData", "remove", "_c", "element", "draw", "silence", "animation", "addCommand", "event", "type", "AFTER_DRAW", "dataChanges", "data", "batch", "isBatching", "push", "undoStackPush", "flat", "notify", "ADD", "length", "initBatchCommand", "initiate", "pop", "emitter", "on", "BATCH_START", "BATCH_END", "canUndo", "canRedo", "undo", "before", "beforeAddCommand", "_d", "afterAddCommand", "UNDO", "redo", "REDO", "undoAndCancel", "CANCEL", "stackSize", "shift", "clear", "CLEAR", "emit", "CHANGE", "handler", "destroy", "off"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\g6\\src\\plugins\\history\\index.ts"], "sourcesContent": ["import EventEmitter from '@antv/event-emitter';\nimport { GraphEvent } from '../../constants';\nimport { HistoryEvent } from '../../constants/events/history';\nimport type { RuntimeContext } from '../../runtime/types';\nimport { <PERSON>Change, Loosen } from '../../types';\nimport type { Command } from '../../types/history';\nimport type { GraphLifeCycleEvent } from '../../utils/event';\nimport { idsOf } from '../../utils/id';\nimport type { BasePluginOptions } from '../base-plugin';\nimport { BasePlugin } from '../base-plugin';\nimport { parseCommand } from './util';\n\n/**\n * <zh/> 历史记录配置项\n *\n * <en/> History options\n */\nexport interface HistoryOptions extends BasePluginOptions {\n  /**\n   * <zh/>  最多记录该数据长度的历史记录\n   *\n   * <en/> The maximum number of history records\n   * @defaultValue 0(不做限制)\n   */\n  stackSize?: number;\n  /**\n   * <zh/> 当一个命令被添加到 Undo/Redo 队列前被调用，如果该方法返回 false，那么这个命令将不会被添加到队列中。revert 为 true 时表示撤销操作，为 false 时表示重做操作\n   *\n   * <en/> Called before a command is added to the Undo/Redo queue. If this method returns false, the command will not be added to the queue. revert is true for undo operations and false for redo operations\n   */\n  beforeAddCommand?: (cmd: Command, revert: boolean) => boolean | void;\n  /**\n   * <zh/> 当一个命令被添加到 Undo/Redo 队列后被调用。revert 为 true 时表示撤销操作，为 false 时表示重做操作\n   *\n   * <en/> Called after a command is added to the Undo/Redo queue. revert is true for undo operations and false for redo operations\n   */\n  afterAddCommand?: (cmd: Command, revert: boolean) => void;\n  /**\n   * <zh/> 执行命令时的回调函数\n   *\n   * <en/> Callback function when executing a command\n   */\n  executeCommand?: (cmd: Command) => void;\n}\n\n/**\n * <zh/> 历史记录\n *\n * <en/> History\n * @remarks\n * <zh/> 历史记录用于记录图的数据变化，支持撤销和重做等操作。\n *\n * <en/> History is used to record data changes in the graph and supports operations such as undo and redo.\n */\nexport class History extends BasePlugin<HistoryOptions> {\n  static defaultOptions: Partial<HistoryOptions> = {\n    stackSize: 0,\n  };\n  private emitter: EventEmitter;\n  private batchChanges: DataChange[][] | null = null;\n  private batchAnimation = false;\n  public undoStack: Command[] = [];\n  public redoStack: Command[] = [];\n  private freezed = false;\n\n  constructor(context: RuntimeContext, options: HistoryOptions) {\n    super(context, Object.assign({}, History.defaultOptions, options));\n\n    this.emitter = new EventEmitter();\n\n    const { graph } = this.context;\n    graph.on(GraphEvent.AFTER_DRAW, this.addCommand);\n    graph.on(GraphEvent.BATCH_START, this.initBatchCommand);\n    graph.on(GraphEvent.BATCH_END, this.addCommand);\n  }\n\n  /**\n   * <zh/> 是否可以执行撤销操作\n   *\n   * <en/> Whether undo can be done\n   * @returns <zh/> 是否可以执行撤销操作 | <en/> Whether undo can be done\n   */\n  public canUndo() {\n    return this.undoStack.length > 0;\n  }\n\n  /**\n   * <zh/> 是否可以执行重做操作\n   *\n   * <en/> Whether redo can be done\n   * @returns <zh/> 是否可以执行重做操作 | <en/> Whether redo can be done\n   */\n  public canRedo() {\n    return this.redoStack.length > 0;\n  }\n\n  /**\n   * <zh/> 执行撤销\n   *\n   * <en/> Execute undo\n   * @returns <zh/> 返回当前实例 | <en/> Return the current instance\n   */\n  public undo() {\n    const cmd = this.undoStack.pop();\n    if (cmd) {\n      this.executeCommand(cmd);\n\n      const before = this.options.beforeAddCommand?.(cmd, false);\n      if (before === false) return;\n\n      this.redoStack.push(cmd);\n      this.options.afterAddCommand?.(cmd, false);\n      this.notify(HistoryEvent.UNDO, cmd);\n    }\n    return this;\n  }\n\n  /**\n   * <zh/> 执行重做\n   *\n   * <en/> Execute redo\n   * @returns <zh/> 返回当前实例 | <en/> Return the current instance\n   */\n  public redo() {\n    const cmd = this.redoStack.pop();\n    if (cmd) {\n      this.executeCommand(cmd, false);\n      this.undoStackPush(cmd);\n      this.notify(HistoryEvent.REDO, cmd);\n    }\n    return this;\n  }\n\n  /**\n   * <zh/> 执行撤销且不计入历史记录\n   *\n   * <en/> Execute undo and do not record in history\n   * @returns <zh/> 返回当前实例 | <en/> Return the current instance\n   */\n  public undoAndCancel() {\n    const cmd = this.undoStack.pop();\n    if (cmd) {\n      this.executeCommand(cmd, false);\n      this.redoStack = [];\n      this.notify(HistoryEvent.CANCEL, cmd);\n    }\n    return this;\n  }\n\n  private executeCommand = (cmd: Command, revert = true) => {\n    this.freezed = true;\n\n    this.options.executeCommand?.(cmd);\n\n    const values = revert ? cmd.original : cmd.current;\n    this.context.graph.addData(values.add);\n    this.context.graph.updateData(values.update);\n    this.context.graph.removeData(idsOf(values.remove, false));\n    this.context.element?.draw({ silence: true, animation: cmd.animation });\n\n    this.freezed = false;\n  };\n\n  private addCommand = (event: GraphLifeCycleEvent) => {\n    if (this.freezed) return;\n\n    if (event.type === GraphEvent.AFTER_DRAW) {\n      const { dataChanges = [], animation = true } = (event as GraphLifeCycleEvent).data;\n\n      if (this.context.batch?.isBatching) {\n        if (!this.batchChanges) return;\n        this.batchChanges.push(dataChanges);\n        this.batchAnimation &&= animation;\n        return;\n      }\n      this.batchChanges = [dataChanges];\n      this.batchAnimation = animation;\n    }\n\n    this.undoStackPush(parseCommand(this.batchChanges!.flat(), this.batchAnimation, this.context));\n    this.notify(HistoryEvent.ADD, this.undoStack[this.undoStack.length - 1]);\n  };\n\n  private initBatchCommand = (event: GraphLifeCycleEvent) => {\n    const { initiate } = event.data;\n    this.batchAnimation = false;\n    if (initiate) {\n      this.batchChanges = [];\n    } else {\n      const cmd = this.undoStack.pop();\n      if (!cmd) this.batchChanges = null;\n    }\n  };\n\n  private undoStackPush(cmd: Command): void {\n    const { stackSize } = this.options;\n\n    if (stackSize !== 0 && this.undoStack.length >= stackSize) {\n      this.undoStack.shift();\n    }\n\n    const before = this.options.beforeAddCommand?.(cmd, true);\n    if (before === false) return;\n\n    this.undoStack.push(cmd);\n    this.options.afterAddCommand?.(cmd, true);\n  }\n\n  /**\n   * <zh/> 清空历史记录\n   *\n   * <en/> Clear history\n   */\n  public clear(): void {\n    this.undoStack = [];\n    this.redoStack = [];\n    this.batchChanges = null;\n    this.batchAnimation = false;\n    this.notify(HistoryEvent.CLEAR, null);\n  }\n\n  private notify(event: Loosen<HistoryEvent>, cmd: Command | null) {\n    this.emitter.emit(event, { cmd });\n    this.emitter.emit(HistoryEvent.CHANGE, { cmd });\n  }\n\n  /**\n   * <zh/> 监听历史记录事件\n   *\n   * <en/> Listen to history events\n   * @param event  - <zh/> 事件名称 | <en/> Event name\n   * @param handler - <zh/> 事件处理函数 | <en/> Event handler\n   */\n  public on(event: Loosen<HistoryEvent>, handler: (e: { cmd?: Command | null }) => void): void {\n    this.emitter.on(event, handler);\n  }\n\n  /**\n   * <zh/> 销毁\n   *\n   * <en/> Destroy\n   * @internal\n   */\n  public destroy(): void {\n    const { graph } = this.context;\n    graph.off(GraphEvent.AFTER_DRAW, this.addCommand);\n    graph.off(GraphEvent.BATCH_START, this.initBatchCommand);\n    graph.off(GraphEvent.BATCH_END, this.addCommand);\n\n    this.emitter.off();\n\n    super.destroy();\n    this.undoStack = [];\n    this.redoStack = [];\n  }\n}\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,qBAAqB;AAC9C,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,YAAY,QAAQ,gCAAgC;AAK7D,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,YAAY,QAAQ,QAAQ;AAmCrC;;;;;;;;;AASA,OAAM,MAAOC,OAAQ,SAAQF,UAA0B;EAWrDG,YAAYC,OAAuB,EAAEC,OAAuB;IAC1D,KAAK,CAACD,OAAO,EAAEE,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEL,OAAO,CAACM,cAAc,EAAEH,OAAO,CAAC,CAAC;IAP5D,KAAAI,YAAY,GAA0B,IAAI;IAC1C,KAAAC,cAAc,GAAG,KAAK;IACvB,KAAAC,SAAS,GAAc,EAAE;IACzB,KAAAC,SAAS,GAAc,EAAE;IACxB,KAAAC,OAAO,GAAG,KAAK;IAsFf,KAAAC,cAAc,GAAG,CAACC,GAAY,EAAEC,MAAM,GAAG,IAAI,KAAI;;MACvD,IAAI,CAACH,OAAO,GAAG,IAAI;MAEnB,CAAAI,EAAA,IAAAC,EAAA,OAAI,CAACb,OAAO,EAACS,cAAc,cAAAG,EAAA,uBAAAA,EAAA,CAAAE,IAAA,CAAAD,EAAA,EAAGH,GAAG,CAAC;MAElC,MAAMK,MAAM,GAAGJ,MAAM,GAAGD,GAAG,CAACM,QAAQ,GAAGN,GAAG,CAACO,OAAO;MAClD,IAAI,CAAClB,OAAO,CAACmB,KAAK,CAACC,OAAO,CAACJ,MAAM,CAACK,GAAG,CAAC;MACtC,IAAI,CAACrB,OAAO,CAACmB,KAAK,CAACG,UAAU,CAACN,MAAM,CAACO,MAAM,CAAC;MAC5C,IAAI,CAACvB,OAAO,CAACmB,KAAK,CAACK,UAAU,CAAC7B,KAAK,CAACqB,MAAM,CAACS,MAAM,EAAE,KAAK,CAAC,CAAC;MAC1D,CAAAC,EAAA,OAAI,CAAC1B,OAAO,CAAC2B,OAAO,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,IAAI,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEC,SAAS,EAAEnB,GAAG,CAACmB;MAAS,CAAE,CAAC;MAEvE,IAAI,CAACrB,OAAO,GAAG,KAAK;IACtB,CAAC;IAEO,KAAAsB,UAAU,GAAIC,KAA0B,IAAI;;MAClD,IAAI,IAAI,CAACvB,OAAO,EAAE;MAElB,IAAIuB,KAAK,CAACC,IAAI,KAAKxC,UAAU,CAACyC,UAAU,EAAE;QACxC,MAAM;UAAEC,WAAW,GAAG,EAAE;UAAEL,SAAS,GAAG;QAAI,CAAE,GAAIE,KAA6B,CAACI,IAAI;QAElF,IAAI,CAAAtB,EAAA,OAAI,CAACd,OAAO,CAACqC,KAAK,cAAAvB,EAAA,uBAAAA,EAAA,CAAEwB,UAAU,EAAE;UAClC,IAAI,CAAC,IAAI,CAACjC,YAAY,EAAE;UACxB,IAAI,CAACA,YAAY,CAACkC,IAAI,CAACJ,WAAW,CAAC;UACnC,IAAI,CAAC7B,cAAc,KAAnB,IAAI,CAACA,cAAc,GAAKwB,SAAS;UACjC;QACF;QACA,IAAI,CAACzB,YAAY,GAAG,CAAC8B,WAAW,CAAC;QACjC,IAAI,CAAC7B,cAAc,GAAGwB,SAAS;MACjC;MAEA,IAAI,CAACU,aAAa,CAAC3C,YAAY,CAAC,IAAI,CAACQ,YAAa,CAACoC,IAAI,EAAE,EAAE,IAAI,CAACnC,cAAc,EAAE,IAAI,CAACN,OAAO,CAAC,CAAC;MAC9F,IAAI,CAAC0C,MAAM,CAAChD,YAAY,CAACiD,GAAG,EAAE,IAAI,CAACpC,SAAS,CAAC,IAAI,CAACA,SAAS,CAACqC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC1E,CAAC;IAEO,KAAAC,gBAAgB,GAAIb,KAA0B,IAAI;MACxD,MAAM;QAAEc;MAAQ,CAAE,GAAGd,KAAK,CAACI,IAAI;MAC/B,IAAI,CAAC9B,cAAc,GAAG,KAAK;MAC3B,IAAIwC,QAAQ,EAAE;QACZ,IAAI,CAACzC,YAAY,GAAG,EAAE;MACxB,CAAC,MAAM;QACL,MAAMM,GAAG,GAAG,IAAI,CAACJ,SAAS,CAACwC,GAAG,EAAE;QAChC,IAAI,CAACpC,GAAG,EAAE,IAAI,CAACN,YAAY,GAAG,IAAI;MACpC;IACF,CAAC;IA5HC,IAAI,CAAC2C,OAAO,GAAG,IAAIxD,YAAY,EAAE;IAEjC,MAAM;MAAE2B;IAAK,CAAE,GAAG,IAAI,CAACnB,OAAO;IAC9BmB,KAAK,CAAC8B,EAAE,CAACxD,UAAU,CAACyC,UAAU,EAAE,IAAI,CAACH,UAAU,CAAC;IAChDZ,KAAK,CAAC8B,EAAE,CAACxD,UAAU,CAACyD,WAAW,EAAE,IAAI,CAACL,gBAAgB,CAAC;IACvD1B,KAAK,CAAC8B,EAAE,CAACxD,UAAU,CAAC0D,SAAS,EAAE,IAAI,CAACpB,UAAU,CAAC;EACjD;EAEA;;;;;;EAMOqB,OAAOA,CAAA;IACZ,OAAO,IAAI,CAAC7C,SAAS,CAACqC,MAAM,GAAG,CAAC;EAClC;EAEA;;;;;;EAMOS,OAAOA,CAAA;IACZ,OAAO,IAAI,CAAC7C,SAAS,CAACoC,MAAM,GAAG,CAAC;EAClC;EAEA;;;;;;EAMOU,IAAIA,CAAA;;IACT,MAAM3C,GAAG,GAAG,IAAI,CAACJ,SAAS,CAACwC,GAAG,EAAE;IAChC,IAAIpC,GAAG,EAAE;MACP,IAAI,CAACD,cAAc,CAACC,GAAG,CAAC;MAExB,MAAM4C,MAAM,GAAG,CAAA1C,EAAA,IAAAC,EAAA,OAAI,CAACb,OAAO,EAACuD,gBAAgB,cAAA3C,EAAA,uBAAAA,EAAA,CAAAE,IAAA,CAAAD,EAAA,EAAGH,GAAG,EAAE,KAAK,CAAC;MAC1D,IAAI4C,MAAM,KAAK,KAAK,EAAE;MAEtB,IAAI,CAAC/C,SAAS,CAAC+B,IAAI,CAAC5B,GAAG,CAAC;MACxB,CAAA8C,EAAA,IAAA/B,EAAA,OAAI,CAACzB,OAAO,EAACyD,eAAe,cAAAD,EAAA,uBAAAA,EAAA,CAAA1C,IAAA,CAAAW,EAAA,EAAGf,GAAG,EAAE,KAAK,CAAC;MAC1C,IAAI,CAAC+B,MAAM,CAAChD,YAAY,CAACiE,IAAI,EAAEhD,GAAG,CAAC;IACrC;IACA,OAAO,IAAI;EACb;EAEA;;;;;;EAMOiD,IAAIA,CAAA;IACT,MAAMjD,GAAG,GAAG,IAAI,CAACH,SAAS,CAACuC,GAAG,EAAE;IAChC,IAAIpC,GAAG,EAAE;MACP,IAAI,CAACD,cAAc,CAACC,GAAG,EAAE,KAAK,CAAC;MAC/B,IAAI,CAAC6B,aAAa,CAAC7B,GAAG,CAAC;MACvB,IAAI,CAAC+B,MAAM,CAAChD,YAAY,CAACmE,IAAI,EAAElD,GAAG,CAAC;IACrC;IACA,OAAO,IAAI;EACb;EAEA;;;;;;EAMOmD,aAAaA,CAAA;IAClB,MAAMnD,GAAG,GAAG,IAAI,CAACJ,SAAS,CAACwC,GAAG,EAAE;IAChC,IAAIpC,GAAG,EAAE;MACP,IAAI,CAACD,cAAc,CAACC,GAAG,EAAE,KAAK,CAAC;MAC/B,IAAI,CAACH,SAAS,GAAG,EAAE;MACnB,IAAI,CAACkC,MAAM,CAAChD,YAAY,CAACqE,MAAM,EAAEpD,GAAG,CAAC;IACvC;IACA,OAAO,IAAI;EACb;EA+CQ6B,aAAaA,CAAC7B,GAAY;;IAChC,MAAM;MAAEqD;IAAS,CAAE,GAAG,IAAI,CAAC/D,OAAO;IAElC,IAAI+D,SAAS,KAAK,CAAC,IAAI,IAAI,CAACzD,SAAS,CAACqC,MAAM,IAAIoB,SAAS,EAAE;MACzD,IAAI,CAACzD,SAAS,CAAC0D,KAAK,EAAE;IACxB;IAEA,MAAMV,MAAM,GAAG,CAAA1C,EAAA,IAAAC,EAAA,OAAI,CAACb,OAAO,EAACuD,gBAAgB,cAAA3C,EAAA,uBAAAA,EAAA,CAAAE,IAAA,CAAAD,EAAA,EAAGH,GAAG,EAAE,IAAI,CAAC;IACzD,IAAI4C,MAAM,KAAK,KAAK,EAAE;IAEtB,IAAI,CAAChD,SAAS,CAACgC,IAAI,CAAC5B,GAAG,CAAC;IACxB,CAAA8C,EAAA,IAAA/B,EAAA,OAAI,CAACzB,OAAO,EAACyD,eAAe,cAAAD,EAAA,uBAAAA,EAAA,CAAA1C,IAAA,CAAAW,EAAA,EAAGf,GAAG,EAAE,IAAI,CAAC;EAC3C;EAEA;;;;;EAKOuD,KAAKA,CAAA;IACV,IAAI,CAAC3D,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACH,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACoC,MAAM,CAAChD,YAAY,CAACyE,KAAK,EAAE,IAAI,CAAC;EACvC;EAEQzB,MAAMA,CAACV,KAA2B,EAAErB,GAAmB;IAC7D,IAAI,CAACqC,OAAO,CAACoB,IAAI,CAACpC,KAAK,EAAE;MAAErB;IAAG,CAAE,CAAC;IACjC,IAAI,CAACqC,OAAO,CAACoB,IAAI,CAAC1E,YAAY,CAAC2E,MAAM,EAAE;MAAE1D;IAAG,CAAE,CAAC;EACjD;EAEA;;;;;;;EAOOsC,EAAEA,CAACjB,KAA2B,EAAEsC,OAA8C;IACnF,IAAI,CAACtB,OAAO,CAACC,EAAE,CAACjB,KAAK,EAAEsC,OAAO,CAAC;EACjC;EAEA;;;;;;EAMOC,OAAOA,CAAA;IACZ,MAAM;MAAEpD;IAAK,CAAE,GAAG,IAAI,CAACnB,OAAO;IAC9BmB,KAAK,CAACqD,GAAG,CAAC/E,UAAU,CAACyC,UAAU,EAAE,IAAI,CAACH,UAAU,CAAC;IACjDZ,KAAK,CAACqD,GAAG,CAAC/E,UAAU,CAACyD,WAAW,EAAE,IAAI,CAACL,gBAAgB,CAAC;IACxD1B,KAAK,CAACqD,GAAG,CAAC/E,UAAU,CAAC0D,SAAS,EAAE,IAAI,CAACpB,UAAU,CAAC;IAEhD,IAAI,CAACiB,OAAO,CAACwB,GAAG,EAAE;IAElB,KAAK,CAACD,OAAO,EAAE;IACf,IAAI,CAAChE,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,SAAS,GAAG,EAAE;EACrB;;AAvMOV,OAAA,CAAAM,cAAc,GAA4B;EAC/C4D,SAAS,EAAE;CACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}