{"ast": null, "code": "var RADIAN = Math.PI / 180;\nvar toRadian = function (degree) {\n  return RADIAN * degree;\n};\nexport default toRadian;", "map": {"version": 3, "names": ["RADIAN", "Math", "PI", "toRadian", "degree"], "sources": ["lodash/to-radian.ts"], "sourcesContent": [null], "mappings": "AAAA,IAAMA,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;AAE5B,IAAMC,QAAQ,GAAG,SAAAA,CAAUC,MAAc;EACvC,OAAOJ,MAAM,GAAGI,MAAM;AACxB,CAAC;AAED,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}