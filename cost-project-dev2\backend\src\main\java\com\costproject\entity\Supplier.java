package com.costproject.entity;

import com.costproject.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Table(name = "suppliers")
@EqualsAndHashCode(callSuper = true)
public class Supplier extends BaseEntity {

    @Column(length = 50, nullable = false, unique = true)
    private String code; // 供应商编码

    @Column(nullable = false)
    private String name; // 供应商名称

    @Column
    private String contactPerson; // 联系人

    @Column
    private String phone; // 联系电话

    @Column
    private String email; // 邮箱

    @Column(columnDefinition = "TEXT")
    private String address; // 地址

    @Column
    private String businessScope; // 经营范围

    @Column
    private String qualification; // 资质

    @Column
    private String bankAccount; // 银行账号

    @Column
    private String taxNumber; // 税号

    @ManyToMany(mappedBy = "suppliers")
    private List<Resource> resources = new ArrayList<>();

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getQualification() {
        return qualification;
    }

    public void setQualification(String qualification) {
        this.qualification = qualification;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public List<Resource> getResources() {
        return resources;
    }

    public void setResources(List<Resource> resources) {
        this.resources = resources;
    }
}