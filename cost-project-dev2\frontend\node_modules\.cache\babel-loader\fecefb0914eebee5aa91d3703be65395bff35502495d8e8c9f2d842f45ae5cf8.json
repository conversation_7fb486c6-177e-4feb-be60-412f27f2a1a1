{"ast": null, "code": "export const addSubgraphConstraints = (g, cg, vs) => {\n  const prev = {};\n  let rootPrev;\n  vs === null || vs === void 0 ? void 0 : vs.forEach(v => {\n    let child = g.getParent(v);\n    let parent;\n    let prevChild;\n    while (child) {\n      parent = g.getParent(child.id);\n      if (parent) {\n        prevChild = prev[parent.id];\n        prev[parent.id] = child.id;\n      } else {\n        prevChild = rootPrev;\n        rootPrev = child.id;\n      }\n      if (prevChild && prevChild !== child.id) {\n        if (!cg.hasNode(prevChild)) {\n          cg.addNode({\n            id: prevChild,\n            data: {}\n          });\n        }\n        if (!cg.hasNode(child.id)) {\n          cg.addNode({\n            id: child.id,\n            data: {}\n          });\n        }\n        if (!cg.hasEdge(`e${prevChild}-${child.id}`)) {\n          cg.addEdge({\n            id: `e${prevChild}-${child.id}`,\n            source: prevChild,\n            target: child.id,\n            data: {}\n          });\n        }\n        return;\n      }\n      child = parent;\n    }\n  });\n};", "map": {"version": 3, "names": ["addSubgraphConstraints", "g", "cg", "vs", "prev", "rootPrev", "for<PERSON>ach", "v", "child", "getParent", "parent", "prev<PERSON><PERSON><PERSON>", "id", "hasNode", "addNode", "data", "hasEdge", "addEdge", "source", "target"], "sources": ["../../../src/antv-dagre/order/add-subgraph-constraints.ts"], "sourcesContent": [null], "mappings": "AAGA,OAAO,MAAMA,sBAAsB,GAAGA,CAACC,CAAQ,EAAEC,EAAS,EAAEC,EAAQ,KAAI;EACtE,MAAMC,IAAI,GAAmB,EAAE;EAC/B,IAAIC,QAAY;EAEhBF,EAAE,aAAFA,EAAE,uBAAFA,EAAE,CAAEG,OAAO,CAAEC,CAAC,IAAI;IAChB,IAAIC,KAAK,GAAGP,CAAC,CAACQ,SAAS,CAACF,CAAC,CAAC;IAC1B,IAAIG,MAAM;IACV,IAAIC,SAAa;IACjB,OAAOH,KAAK,EAAE;MACZE,MAAM,GAAGT,CAAC,CAACQ,SAAS,CAACD,KAAK,CAACI,EAAE,CAAC;MAC9B,IAAIF,MAAM,EAAE;QACVC,SAAS,GAAGP,IAAI,CAACM,MAAM,CAACE,EAAE,CAAC;QAC3BR,IAAI,CAACM,MAAM,CAACE,EAAE,CAAC,GAAGJ,KAAK,CAACI,EAAE;OAC3B,MAAM;QACLD,SAAS,GAAGN,QAAQ;QACpBA,QAAQ,GAAGG,KAAK,CAACI,EAAE;;MAErB,IAAID,SAAS,IAAIA,SAAS,KAAKH,KAAK,CAACI,EAAE,EAAE;QACvC,IAAI,CAACV,EAAE,CAACW,OAAO,CAACF,SAAS,CAAC,EAAE;UAC1BT,EAAE,CAACY,OAAO,CAAC;YACTF,EAAE,EAAED,SAAS;YACbI,IAAI,EAAE;WACP,CAAC;;QAEJ,IAAI,CAACb,EAAE,CAACW,OAAO,CAACL,KAAK,CAACI,EAAE,CAAC,EAAE;UACzBV,EAAE,CAACY,OAAO,CAAC;YACTF,EAAE,EAAEJ,KAAK,CAACI,EAAE;YACZG,IAAI,EAAE;WACP,CAAC;;QAEJ,IAAI,CAACb,EAAE,CAACc,OAAO,CAAC,IAAIL,SAAS,IAAIH,KAAK,CAACI,EAAE,EAAE,CAAC,EAAE;UAC5CV,EAAE,CAACe,OAAO,CAAC;YACTL,EAAE,EAAE,IAAID,SAAS,IAAIH,KAAK,CAACI,EAAE,EAAE;YAC/BM,MAAM,EAAEP,SAAS;YACjBQ,MAAM,EAAEX,KAAK,CAACI,EAAE;YAChBG,IAAI,EAAE;WACP,CAAC;;QAEJ;;MAEFP,KAAK,GAAGE,MAAM;;EAElB,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}