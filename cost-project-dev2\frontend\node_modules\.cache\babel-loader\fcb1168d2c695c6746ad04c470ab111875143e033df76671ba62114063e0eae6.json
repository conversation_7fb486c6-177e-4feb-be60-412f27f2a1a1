{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FundProjectionScreenOutlinedSvg from \"@ant-design/icons-svg/es/asn/FundProjectionScreenOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FundProjectionScreenOutlined = function FundProjectionScreenOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FundProjectionScreenOutlinedSvg\n  }));\n};\n\n/**![fund-projection-screen](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0zMTIuMSA1OTEuNWMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDEwMS44LTEwMS44IDg2LjEgODYuMmMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDIyNi4zLTIyNi41YzMuMS0zLjEgMy4xLTguMiAwLTExLjNsLTM2LjgtMzYuOGE4LjAzIDguMDMgMCAwMC0xMS4zIDBMNTE3IDQ4NS4zbC04Ni4xLTg2LjJhOC4wMyA4LjAzIDAgMDAtMTEuMyAwTDI3NS4zIDU0My40YTguMDMgOC4wMyAwIDAwMCAxMS4zbDM2LjggMzYuOHoiIC8+PHBhdGggZD0iTTkwNCAxNjBINTQ4Vjk2YzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY2NEgxMjBjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjUyMGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgzNTYuNHYzMkwzMTEuNiA4ODQuMWE3LjkyIDcuOTIgMCAwMC0yLjMgMTFsMzAuMyA0Ny4ydi4xYzIuNCAzLjcgNy40IDQuNyAxMS4xIDIuM0w1MTIgODM4LjlsMTYxLjMgMTA1LjhjMy43IDIuNCA4LjcgMS40IDExLjEtMi4zdi0uMWwzMC4zLTQ3LjJhOCA4IDAgMDAtMi4zLTExTDU0OCA3NzYuM1Y3NDRoMzU2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE5MmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDUxMkgxNjBWMjMyaDcwNHY0NDB6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FundProjectionScreenOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FundProjectionScreenOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "FundProjectionScreenOutlinedSvg", "AntdIcon", "FundProjectionScreenOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/antd/node_modules/@ant-design/icons/es/icons/FundProjectionScreenOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FundProjectionScreenOutlinedSvg from \"@ant-design/icons-svg/es/asn/FundProjectionScreenOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FundProjectionScreenOutlined = function FundProjectionScreenOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FundProjectionScreenOutlinedSvg\n  }));\n};\n\n/**![fund-projection-screen](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0zMTIuMSA1OTEuNWMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDEwMS44LTEwMS44IDg2LjEgODYuMmMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDIyNi4zLTIyNi41YzMuMS0zLjEgMy4xLTguMiAwLTExLjNsLTM2LjgtMzYuOGE4LjAzIDguMDMgMCAwMC0xMS4zIDBMNTE3IDQ4NS4zbC04Ni4xLTg2LjJhOC4wMyA4LjAzIDAgMDAtMTEuMyAwTDI3NS4zIDU0My40YTguMDMgOC4wMyAwIDAwMCAxMS4zbDM2LjggMzYuOHoiIC8+PHBhdGggZD0iTTkwNCAxNjBINTQ4Vjk2YzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY2NEgxMjBjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjUyMGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgzNTYuNHYzMkwzMTEuNiA4ODQuMWE3LjkyIDcuOTIgMCAwMC0yLjMgMTFsMzAuMyA0Ny4ydi4xYzIuNCAzLjcgNy40IDQuNyAxMS4xIDIuM0w1MTIgODM4LjlsMTYxLjMgMTA1LjhjMy43IDIuNCA4LjcgMS40IDExLjEtMi4zdi0uMWwzMC4zLTQ3LjJhOCA4IDAgMDAtMi4zLTExTDU0OCA3NzYuM1Y3NDRoMzU2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE5MmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDUxMkgxNjBWMjMyaDcwNHY0NDB6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FundProjectionScreenOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FundProjectionScreenOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,+BAA+B,MAAM,2DAA2D;AACvG,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,4BAA4B,GAAG,SAASA,4BAA4BA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnF,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,4BAA4B,CAAC;AACzE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,8BAA8B;AACtD;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}