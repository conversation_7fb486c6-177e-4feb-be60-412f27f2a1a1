{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\cost-project-dev2\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.js\";\nimport React from 'react';\nimport { Alert, Button } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    // 更新 state 使下一次渲染能够显示降级后的 UI\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    // 你同样可以将错误日志上报给服务器\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n  render() {\n    if (this.state.hasError) {\n      // 你可以自定义降级后的 UI 并渲染\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u7EC4\\u4EF6\\u6E32\\u67D3\\u9519\\u8BEF\",\n          description: \"\\u56FE\\u8868\\u7EC4\\u4EF6\\u9047\\u5230\\u4E86\\u4E00\\u4E2A\\u9519\\u8BEF\\uFF0C\\u8BF7\\u5237\\u65B0\\u9875\\u9762\\u91CD\\u8BD5\\u3002\",\n          type: \"error\",\n          showIcon: true,\n          action: /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            danger: true,\n            onClick: () => window.location.reload(),\n            children: \"\\u5237\\u65B0\\u9875\\u9762\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"details\", {\n          style: {\n            whiteSpace: 'pre-wrap',\n            marginTop: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            children: \"\\u9519\\u8BEF\\u8BE6\\u60C5 (\\u5F00\\u53D1\\u6A21\\u5F0F)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 15\n          }, this), this.state.error && this.state.error.toString(), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 15\n          }, this), this.state.errorInfo.componentStack]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "getDerivedStateFromError", "componentDidCatch", "console", "setState", "render", "style", "padding", "children", "message", "description", "type", "showIcon", "action", "size", "danger", "onClick", "window", "location", "reload", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "process", "env", "NODE_ENV", "whiteSpace", "marginTop", "toString", "componentStack"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/src/components/ErrorBoundary.js"], "sourcesContent": ["import React from 'react';\nimport { Alert, Button } from 'antd';\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    // 更新 state 使下一次渲染能够显示降级后的 UI\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // 你同样可以将错误日志上报给服务器\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n\n  render() {\n    if (this.state.hasError) {\n      // 你可以自定义降级后的 UI 并渲染\n      return (\n        <div style={{ padding: '20px' }}>\n          <Alert\n            message=\"组件渲染错误\"\n            description=\"图表组件遇到了一个错误，请刷新页面重试。\"\n            type=\"error\"\n            showIcon\n            action={\n              <Button \n                size=\"small\" \n                danger \n                onClick={() => window.location.reload()}\n              >\n                刷新页面\n              </Button>\n            }\n          />\n          {process.env.NODE_ENV === 'development' && (\n            <details style={{ whiteSpace: 'pre-wrap', marginTop: '10px' }}>\n              <summary>错误详情 (开发模式)</summary>\n              {this.state.error && this.state.error.toString()}\n              <br />\n              {this.state.errorInfo.componentStack}\n            </details>\n          )}\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,aAAa,SAASL,KAAK,CAACM,SAAS,CAAC;EAC1CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC;EAChE;EAEA,OAAOC,wBAAwBA,CAACF,KAAK,EAAE;IACrC;IACA,OAAO;MAAED,QAAQ,EAAE;IAAK,CAAC;EAC3B;EAEAI,iBAAiBA,CAACH,KAAK,EAAEC,SAAS,EAAE;IAClC;IACAG,OAAO,CAACJ,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEC,SAAS,CAAC;IACjE,IAAI,CAACI,QAAQ,CAAC;MACZL,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EAEAK,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACR,KAAK,CAACC,QAAQ,EAAE;MACvB;MACA,oBACEN,OAAA;QAAKc,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAC,QAAA,gBAC9BhB,OAAA,CAACH,KAAK;UACJoB,OAAO,EAAC,sCAAQ;UAChBC,WAAW,EAAC,0HAAsB;UAClCC,IAAI,EAAC,OAAO;UACZC,QAAQ;UACRC,MAAM,eACJrB,OAAA,CAACF,MAAM;YACLwB,IAAI,EAAC,OAAO;YACZC,MAAM;YACNC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YAAAX,QAAA,EACzC;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EACDC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrClC,OAAA;UAASc,KAAK,EAAE;YAAEqB,UAAU,EAAE,UAAU;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAApB,QAAA,gBAC5DhB,OAAA;YAAAgB,QAAA,EAAS;UAAW;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,EAC7B,IAAI,CAAC1B,KAAK,CAACE,KAAK,IAAI,IAAI,CAACF,KAAK,CAACE,KAAK,CAAC8B,QAAQ,CAAC,CAAC,eAChDrC,OAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACL,IAAI,CAAC1B,KAAK,CAACG,SAAS,CAAC8B,cAAc;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEV;IAEA,OAAO,IAAI,CAAC3B,KAAK,CAACY,QAAQ;EAC5B;AACF;AAEA,eAAef,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}