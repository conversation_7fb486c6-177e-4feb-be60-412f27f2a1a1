package com.costproject.controller;

import com.costproject.service.ProjectImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/project-import")
@Api(tags = "项目导入管理")
public class ProjectImportController {
    
    private static final Logger log = LoggerFactory.getLogger(ProjectImportController.class);

    private final ProjectImportService projectImportService;
    
    public ProjectImportController(ProjectImportService projectImportService) {
        this.projectImportService = projectImportService;
    }

    @PostMapping("/preview")
    @ApiOperation("预览项目文件")
    public ResponseEntity<?> previewProjectFile(
            @ApiParam(value = "项目文件", required = true)
            @RequestParam("file") MultipartFile file,
            @ApiParam(value = "文件格式", required = true)
            @RequestParam("format") String format) {
        try {
            Map<String, Object> previewData = projectImportService.previewProjectFile(file, format);
            return ResponseEntity.ok(previewData);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("预览项目文件失败", e);
            return ResponseEntity.internalServerError().body(Map.of("error", "预览项目文件失败: " + e.getMessage()));
        }
    }

    @PostMapping("/import")
    @ApiOperation("导入项目文件")
    public ResponseEntity<?> importProjectFile(
            @ApiParam(value = "项目文件", required = true)
            @RequestParam("file") MultipartFile file,
            @ApiParam(value = "文件格式", required = true)
            @RequestParam("format") String format) {
        try {
            Map<String, Object> importedData = projectImportService.importProjectFile(file, format);
            return ResponseEntity.ok(importedData);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("导入项目文件失败", e);
            return ResponseEntity.internalServerError().body(Map.of("error", "导入项目文件失败: " + e.getMessage()));
        }
    }

    @GetMapping("/formats")
    @ApiOperation("获取支持的文件格式")
    public ResponseEntity<?> getSupportedFormats() {
        Map<String, Object> formats = new HashMap<>();
        formats.put("formats", new String[]{
            "mpp", "mpx", "xml", "xer", "pmxml", "pp"
        });
        formats.put("descriptions", Map.of(
            "mpp", "Microsoft Project (MPP)",
            "mpx", "Microsoft Project Exchange (MPX)",
            "xml", "Microsoft Project XML (MSPDI)",
            "xer", "Primavera P6 (XER)",
            "pmxml", "Primavera P6 (PMXML)",
            "pp", "Asta Powerproject"
        ));
        return ResponseEntity.ok(formats);
    }

    @GetMapping("/validate")
    @ApiOperation("验证文件格式")
    public ResponseEntity<?> validateFile(
            @ApiParam(value = "文件名", required = true)
            @RequestParam("filename") String filename) {
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        boolean isSupported = switch (extension) {
            case "mpp", "mpx", "xml", "xer", "pmxml", "pp" -> true;
            default -> false;
        };
        return ResponseEntity.ok(Map.of(
            "supported", isSupported,
            "format", extension
        ));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<?> handleException(Exception e) {
        log.error("处理导入请求时发生错误", e);
        return ResponseEntity.internalServerError().body(Map.of(
            "error", "服务器内部错误",
            "message", e.getMessage()
        ));
    }
}
