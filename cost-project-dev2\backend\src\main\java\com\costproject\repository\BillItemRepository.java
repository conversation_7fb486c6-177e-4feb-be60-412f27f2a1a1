package com.costproject.repository;

import com.costproject.entity.BillItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BillItemRepository extends JpaRepository<BillItem, Long> {
    List<BillItem> findByNameContaining(String name);
    List<BillItem> findByChapter(String chapter);
    List<BillItem> findByNameContainingAndChapter(String name, String chapter);
    List<BillItem> findByCodeStartingWith(String codePrefix);
    boolean existsByCode(String code);
}