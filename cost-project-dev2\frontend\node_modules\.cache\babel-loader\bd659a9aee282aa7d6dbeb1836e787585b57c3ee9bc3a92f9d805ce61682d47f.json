{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\cost-project-dev2\\\\frontend\\\\src\\\\components\\\\StagewiseToolbar.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Button, Tooltip } from 'antd';\nimport { ToolOutlined, BugOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StagewiseToolbar = () => {\n  _s();\n  const [debugMode, setDebugMode] = useState(false);\n  useEffect(() => {\n    // 只在开发模式下显示\n    if (process.env.NODE_ENV === 'development') {\n      console.log('🎯 Stagewise工具条已加载');\n\n      // 检查调试模式状态\n      const isDebug = localStorage.getItem('debugMode') === 'true';\n      setDebugMode(isDebug);\n      if (isDebug) {\n        document.body.style.border = '3px solid #ff4d4f';\n      }\n    }\n  }, []);\n  const toggleDebugMode = () => {\n    const newDebugMode = !debugMode;\n    setDebugMode(newDebugMode);\n    localStorage.setItem('debugMode', newDebugMode.toString());\n    if (newDebugMode) {\n      document.body.style.border = '3px solid #ff4d4f';\n      console.log('🔧 调试模式已启用');\n    } else {\n      document.body.style.border = 'none';\n      console.log('✅ 调试模式已禁用');\n    }\n  };\n\n  // 只在开发模式下显示\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: '50%',\n      right: '20px',\n      transform: 'translateY(-50%)',\n      zIndex: 1000,\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '8px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n      title: \"Stagewise\\u5DE5\\u5177\\u6761\",\n      placement: \"left\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        shape: \"circle\",\n        icon: /*#__PURE__*/_jsxDEV(ToolOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 17\n        }, this),\n        style: {\n          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n      title: debugMode ? '关闭调试模式' : '开启调试模式',\n      placement: \"left\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: debugMode ? 'primary' : 'default',\n        shape: \"circle\",\n        icon: /*#__PURE__*/_jsxDEV(BugOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 17\n        }, this),\n        onClick: toggleDebugMode,\n        style: {\n          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n          backgroundColor: debugMode ? '#ff4d4f' : undefined,\n          borderColor: debugMode ? '#ff4d4f' : undefined\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(StagewiseToolbar, \"WBq1++UvW/H7yeMx0+LO31aLZGQ=\");\n_c = StagewiseToolbar;\nexport default StagewiseToolbar;\nvar _c;\n$RefreshReg$(_c, \"StagewiseToolbar\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ToolOutlined", "BugOutlined", "jsxDEV", "_jsxDEV", "StagewiseToolbar", "_s", "debugMode", "setDebugMode", "process", "env", "NODE_ENV", "console", "log", "isDebug", "localStorage", "getItem", "document", "body", "style", "border", "toggleDebugMode", "newDebugMode", "setItem", "toString", "position", "top", "right", "transform", "zIndex", "display", "flexDirection", "gap", "children", "title", "placement", "type", "shape", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "boxShadow", "onClick", "backgroundColor", "undefined", "borderColor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/src/components/StagewiseToolbar.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { But<PERSON>, Tooltip } from 'antd';\nimport { ToolOutlined, BugOutlined } from '@ant-design/icons';\n\nconst StagewiseToolbar = () => {\n  const [debugMode, setDebugMode] = useState(false);\n\n  useEffect(() => {\n    // 只在开发模式下显示\n    if (process.env.NODE_ENV === 'development') {\n      console.log('🎯 Stagewise工具条已加载');\n      \n      // 检查调试模式状态\n      const isDebug = localStorage.getItem('debugMode') === 'true';\n      setDebugMode(isDebug);\n      \n      if (isDebug) {\n        document.body.style.border = '3px solid #ff4d4f';\n      }\n    }\n  }, []);\n\n  const toggleDebugMode = () => {\n    const newDebugMode = !debugMode;\n    setDebugMode(newDebugMode);\n    localStorage.setItem('debugMode', newDebugMode.toString());\n    \n    if (newDebugMode) {\n      document.body.style.border = '3px solid #ff4d4f';\n      console.log('🔧 调试模式已启用');\n    } else {\n      document.body.style.border = 'none';\n      console.log('✅ 调试模式已禁用');\n    }\n  };\n\n  // 只在开发模式下显示\n  if (process.env.NODE_ENV !== 'development') {\n    return null;\n  }\n\n  return (\n    <div\n      style={{\n        position: 'fixed',\n        top: '50%',\n        right: '20px',\n        transform: 'translateY(-50%)',\n        zIndex: 1000,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px',\n      }}\n    >\n      <Tooltip title=\"Stagewise工具条\" placement=\"left\">\n        <Button\n          type=\"primary\"\n          shape=\"circle\"\n          icon={<ToolOutlined />}\n          style={{\n            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n          }}\n        />\n      </Tooltip>\n\n      <Tooltip title={debugMode ? '关闭调试模式' : '开启调试模式'} placement=\"left\">\n        <Button\n          type={debugMode ? 'primary' : 'default'}\n          shape=\"circle\"\n          icon={<BugOutlined />}\n          onClick={toggleDebugMode}\n          style={{\n            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n            backgroundColor: debugMode ? '#ff4d4f' : undefined,\n            borderColor: debugMode ? '#ff4d4f' : undefined,\n          }}\n        />\n      </Tooltip>\n    </div>\n  );\n};\n\nexport default StagewiseToolbar; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,SAASC,YAAY,EAAEC,WAAW,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAEjDD,SAAS,CAAC,MAAM;IACd;IACA,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC1CC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;;MAEjC;MACA,MAAMC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,KAAK,MAAM;MAC5DR,YAAY,CAACM,OAAO,CAAC;MAErB,IAAIA,OAAO,EAAE;QACXG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG,mBAAmB;MAClD;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,YAAY,GAAG,CAACf,SAAS;IAC/BC,YAAY,CAACc,YAAY,CAAC;IAC1BP,YAAY,CAACQ,OAAO,CAAC,WAAW,EAAED,YAAY,CAACE,QAAQ,CAAC,CAAC,CAAC;IAE1D,IAAIF,YAAY,EAAE;MAChBL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG,mBAAmB;MAChDR,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;IAC3B,CAAC,MAAM;MACLI,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;MACnCR,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1C,OAAO,IAAI;EACb;EAEA,oBACEP,OAAA;IACEe,KAAK,EAAE;MACLM,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,kBAAkB;MAC7BC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,GAAG,EAAE;IACP,CAAE;IAAAC,QAAA,gBAEF7B,OAAA,CAACJ,OAAO;MAACkC,KAAK,EAAC,6BAAc;MAACC,SAAS,EAAC,MAAM;MAAAF,QAAA,eAC5C7B,OAAA,CAACL,MAAM;QACLqC,IAAI,EAAC,SAAS;QACdC,KAAK,EAAC,QAAQ;QACdC,IAAI,eAAElC,OAAA,CAACH,YAAY;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBvB,KAAK,EAAE;UACLwB,SAAS,EAAE;QACb;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEVtC,OAAA,CAACJ,OAAO;MAACkC,KAAK,EAAE3B,SAAS,GAAG,QAAQ,GAAG,QAAS;MAAC4B,SAAS,EAAC,MAAM;MAAAF,QAAA,eAC/D7B,OAAA,CAACL,MAAM;QACLqC,IAAI,EAAE7B,SAAS,GAAG,SAAS,GAAG,SAAU;QACxC8B,KAAK,EAAC,QAAQ;QACdC,IAAI,eAAElC,OAAA,CAACF,WAAW;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBE,OAAO,EAAEvB,eAAgB;QACzBF,KAAK,EAAE;UACLwB,SAAS,EAAE,6BAA6B;UACxCE,eAAe,EAAEtC,SAAS,GAAG,SAAS,GAAGuC,SAAS;UAClDC,WAAW,EAAExC,SAAS,GAAG,SAAS,GAAGuC;QACvC;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACpC,EAAA,CA5EID,gBAAgB;AAAA2C,EAAA,GAAhB3C,gBAAgB;AA8EtB,eAAeA,gBAAgB;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}