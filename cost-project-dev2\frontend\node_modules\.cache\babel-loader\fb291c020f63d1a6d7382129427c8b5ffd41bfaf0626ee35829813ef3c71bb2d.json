{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport UploadOutlinedSvg from \"@ant-design/icons-svg/es/asn/UploadOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar UploadOutlined = function UploadOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: UploadOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(UploadOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'UploadOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_objectSpread", "React", "UploadOutlinedSvg", "AntdIcon", "UploadOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/UploadOutlined.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport UploadOutlinedSvg from \"@ant-design/icons-svg/es/asn/UploadOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar UploadOutlined = function UploadOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: UploadOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(UploadOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'UploadOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,cAAc,CAAC;AAC3D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,gBAAgB;AACxC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}