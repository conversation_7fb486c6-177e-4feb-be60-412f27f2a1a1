package com.costproject.entity;

import com.costproject.entity.base.BaseEntity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Entity
@Table(name = "quota_items")
@EqualsAndHashCode(callSuper = true)
public class QuotaItem extends BaseEntity {

    @Column(length = 50, nullable = false, unique = true)
    private String code; // 定额编号

    @Column(nullable = false)
    private String name; // 定额项名称

    @Column(columnDefinition = "TEXT")
    private String description; // 工作内容描述

    @Column(length = 20, nullable = false)
    private String unit; // 计量单位

    @Column(precision = 18, scale = 4)
    private BigDecimal unitPrice; // 定额单价（自动计算）

    // 资源消耗明细（JSON格式存储）
    @Column(columnDefinition = "TEXT")
    private String resourceDetails;

    // 与清单的关联关系
    @ManyToMany(mappedBy = "quotaItems")
    private List<BillItem> billItems = new ArrayList<>();

    // 人工费用组成
    @Column(precision = 18, scale = 4)
    private BigDecimal laborCost; // 人工费小计

    @Column(precision = 18, scale = 4)
    private BigDecimal basicSalary; // 基本工资
    
    @Column(precision = 18, scale = 4)
    private BigDecimal salaryAllowance; // 工资性补贴
    
    @Column(precision = 18, scale = 4)
    private BigDecimal auxiliaryWage; // 生产工人辅助工资
    
    @Column(precision = 18, scale = 4)
    private BigDecimal welfareFee; // 职工福利费
    
    @Column(precision = 18, scale = 4)
    private BigDecimal laborProtectionFee; // 劳动保护费

    // 材料费用组成
    @Column(precision = 18, scale = 4)
    private BigDecimal materialCost; // 材料费小计

    @Column(precision = 18, scale = 4)
    private BigDecimal materialPrice; // 材料原价
    
    @Column(precision = 18, scale = 4)
    private BigDecimal transportFee; // 材料运杂费
    
    @Column(precision = 18, scale = 4)
    private BigDecimal lossFee; // 运输损耗费
    
    @Column(precision = 18, scale = 4)
    private BigDecimal purchaseAndStorageFee; // 采购及保管费
    
    @Column(precision = 18, scale = 4)
    private BigDecimal testingFee; // 检验试验费

    // 机械费用组成
    @Column(precision = 18, scale = 4)
    private BigDecimal machineCost; // 机械费小计

    @Column(precision = 18, scale = 4)
    private BigDecimal depreciationFee; // 折旧费
    
    @Column(precision = 18, scale = 4)
    private BigDecimal majorRepairFee; // 大修理费
    
    @Column(precision = 18, scale = 4)
    private BigDecimal routineRepairFee; // 经常修理费
    
    @Column(precision = 18, scale = 4)
    private BigDecimal installationAndTransportFee; // 安拆费及场外运费
    
    @Column(precision = 18, scale = 4)
    private BigDecimal machineOperatorFee; // 机械操作人工费
    
    @Column(precision = 18, scale = 4)
    private BigDecimal fuelAndPowerFee; // 燃料动力费
    
    @Column(precision = 18, scale = 4)
    private BigDecimal roadFee; // 养路费及车船使用税

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getResourceDetails() {
        return resourceDetails;
    }

    public void setResourceDetails(String resourceDetails) {
        this.resourceDetails = resourceDetails;
    }

    public List<BillItem> getBillItems() {
        return billItems;
    }

    public void setBillItems(List<BillItem> billItems) {
        this.billItems = billItems;
    }

    // 人工费用相关的getter/setter
    public BigDecimal getLaborCost() {
        return laborCost;
    }

    public void setLaborCost(BigDecimal laborCost) {
        this.laborCost = laborCost;
    }

    public BigDecimal getBasicSalary() {
        return basicSalary;
    }

    public void setBasicSalary(BigDecimal basicSalary) {
        this.basicSalary = basicSalary;
        calculateUnitPrice();
    }

    public BigDecimal getSalaryAllowance() {
        return salaryAllowance;
    }

    public void setSalaryAllowance(BigDecimal salaryAllowance) {
        this.salaryAllowance = salaryAllowance;
        calculateUnitPrice();
    }

    public BigDecimal getAuxiliaryWage() {
        return auxiliaryWage;
    }

    public void setAuxiliaryWage(BigDecimal auxiliaryWage) {
        this.auxiliaryWage = auxiliaryWage;
        calculateUnitPrice();
    }

    public BigDecimal getWelfareFee() {
        return welfareFee;
    }

    public void setWelfareFee(BigDecimal welfareFee) {
        this.welfareFee = welfareFee;
        calculateUnitPrice();
    }

    public BigDecimal getLaborProtectionFee() {
        return laborProtectionFee;
    }

    public void setLaborProtectionFee(BigDecimal laborProtectionFee) {
        this.laborProtectionFee = laborProtectionFee;
        calculateUnitPrice();
    }

    // 材料费用相关的getter/setter
    public BigDecimal getMaterialCost() {
        return materialCost;
    }

    public void setMaterialCost(BigDecimal materialCost) {
        this.materialCost = materialCost;
    }

    public BigDecimal getMaterialPrice() {
        return materialPrice;
    }

    public void setMaterialPrice(BigDecimal materialPrice) {
        this.materialPrice = materialPrice;
        calculateUnitPrice();
    }

    public BigDecimal getTransportFee() {
        return transportFee;
    }

    public void setTransportFee(BigDecimal transportFee) {
        this.transportFee = transportFee;
        calculateUnitPrice();
    }

    public BigDecimal getLossFee() {
        return lossFee;
    }

    public void setLossFee(BigDecimal lossFee) {
        this.lossFee = lossFee;
        calculateUnitPrice();
    }

    public BigDecimal getPurchaseAndStorageFee() {
        return purchaseAndStorageFee;
    }

    public void setPurchaseAndStorageFee(BigDecimal purchaseAndStorageFee) {
        this.purchaseAndStorageFee = purchaseAndStorageFee;
        calculateUnitPrice();
    }

    public BigDecimal getTestingFee() {
        return testingFee;
    }

    public void setTestingFee(BigDecimal testingFee) {
        this.testingFee = testingFee;
        calculateUnitPrice();
    }

    // 机械费用相关的getter/setter
    public BigDecimal getMachineCost() {
        return machineCost;
    }

    public void setMachineCost(BigDecimal machineCost) {
        this.machineCost = machineCost;
    }

    public BigDecimal getDepreciationFee() {
        return depreciationFee;
    }

    public void setDepreciationFee(BigDecimal depreciationFee) {
        this.depreciationFee = depreciationFee;
        calculateUnitPrice();
    }

    public BigDecimal getMajorRepairFee() {
        return majorRepairFee;
    }

    public void setMajorRepairFee(BigDecimal majorRepairFee) {
        this.majorRepairFee = majorRepairFee;
        calculateUnitPrice();
    }

    public BigDecimal getRoutineRepairFee() {
        return routineRepairFee;
    }

    public void setRoutineRepairFee(BigDecimal routineRepairFee) {
        this.routineRepairFee = routineRepairFee;
        calculateUnitPrice();
    }

    public BigDecimal getInstallationAndTransportFee() {
        return installationAndTransportFee;
    }

    public void setInstallationAndTransportFee(BigDecimal installationAndTransportFee) {
        this.installationAndTransportFee = installationAndTransportFee;
        calculateUnitPrice();
    }

    public BigDecimal getMachineOperatorFee() {
        return machineOperatorFee;
    }

    public void setMachineOperatorFee(BigDecimal machineOperatorFee) {
        this.machineOperatorFee = machineOperatorFee;
        calculateUnitPrice();
    }

    public BigDecimal getFuelAndPowerFee() {
        return fuelAndPowerFee;
    }

    public void setFuelAndPowerFee(BigDecimal fuelAndPowerFee) {
        this.fuelAndPowerFee = fuelAndPowerFee;
        calculateUnitPrice();
    }

    public BigDecimal getRoadFee() {
        return roadFee;
    }

    public void setRoadFee(BigDecimal roadFee) {
        this.roadFee = roadFee;
        calculateUnitPrice();
    }

    /**
     * 计算定额单价
     */
    public void calculateUnitPrice() {
        this.unitPrice = BigDecimal.ZERO;

        // 计算人工费小计
        this.laborCost = BigDecimal.ZERO;
        if (basicSalary != null) this.laborCost = this.laborCost.add(basicSalary);
        if (salaryAllowance != null) this.laborCost = this.laborCost.add(salaryAllowance);
        if (auxiliaryWage != null) this.laborCost = this.laborCost.add(auxiliaryWage);
        if (welfareFee != null) this.laborCost = this.laborCost.add(welfareFee);
        if (laborProtectionFee != null) this.laborCost = this.laborCost.add(laborProtectionFee);

        // 计算材料费小计
        this.materialCost = BigDecimal.ZERO;
        if (materialPrice != null) this.materialCost = this.materialCost.add(materialPrice);
        if (transportFee != null) this.materialCost = this.materialCost.add(transportFee);
        if (lossFee != null) this.materialCost = this.materialCost.add(lossFee);
        if (purchaseAndStorageFee != null) this.materialCost = this.materialCost.add(purchaseAndStorageFee);
        if (testingFee != null) this.materialCost = this.materialCost.add(testingFee);

        // 计算机械费小计
        this.machineCost = BigDecimal.ZERO;
        if (depreciationFee != null) this.machineCost = this.machineCost.add(depreciationFee);
        if (majorRepairFee != null) this.machineCost = this.machineCost.add(majorRepairFee);
        if (routineRepairFee != null) this.machineCost = this.machineCost.add(routineRepairFee);
        if (installationAndTransportFee != null) this.machineCost = this.machineCost.add(installationAndTransportFee);
        if (machineOperatorFee != null) this.machineCost = this.machineCost.add(machineOperatorFee);
        if (fuelAndPowerFee != null) this.machineCost = this.machineCost.add(fuelAndPowerFee);
        if (roadFee != null) this.machineCost = this.machineCost.add(roadFee);

        // 计算总单价
        this.unitPrice = this.laborCost.add(this.materialCost).add(this.machineCost);
    }

    /**
     * 更新资源消耗明细
     * @param resources 资源消耗Map，key为资源ID，value为消耗数量
     */
    public void updateResourceDetails(Map<String, BigDecimal> resources) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            this.resourceDetails = mapper.writeValueAsString(resources);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert resource details to JSON", e);
        }
    }

    /**
     * 获取资源消耗明细
     * @return 资源消耗Map
     */
    public Map<String, BigDecimal> getResourceConsumption() {
        if (resourceDetails == null || resourceDetails.isEmpty()) {
            return new HashMap<>();
        }
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(resourceDetails, new TypeReference<Map<String, BigDecimal>>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to parse resource details from JSON", e);
        }
    }

    @PrePersist
    @PreUpdate
    private void prePersist() {
        calculateUnitPrice();
    }
}