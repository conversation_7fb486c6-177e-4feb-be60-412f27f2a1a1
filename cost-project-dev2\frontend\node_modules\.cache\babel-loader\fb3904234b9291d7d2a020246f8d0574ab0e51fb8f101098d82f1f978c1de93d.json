{"ast": null, "code": "import { binarytree } from \"d3-binarytree\";\nimport { quadtree } from \"d3-quadtree\";\nimport { octree } from \"d3-octree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\nimport { x, y, z } from \"./simulation.js\";\nexport default function () {\n  var nodes,\n    nDim,\n    node,\n    random,\n    alpha,\n    strength = constant(-30),\n    strengths,\n    distanceMin2 = 1,\n    distanceMax2 = Infinity,\n    theta2 = 0.81;\n  function force(_) {\n    var i,\n      n = nodes.length,\n      tree = (nDim === 1 ? binarytree(nodes, x) : nDim === 2 ? quadtree(nodes, x, y) : nDim === 3 ? octree(nodes, x, y, z) : null).visitAfter(accumulate);\n    for (alpha = _, i = 0; i < n; ++i) node = nodes[i], tree.visit(apply);\n  }\n  function initialize() {\n    if (!nodes) return;\n    var i,\n      n = nodes.length,\n      node;\n    strengths = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], strengths[node.index] = +strength(node, i, nodes);\n  }\n  function accumulate(treeNode) {\n    var strength = 0,\n      q,\n      c,\n      weight = 0,\n      x,\n      y,\n      z,\n      i;\n    var numChildren = treeNode.length;\n\n    // For internal nodes, accumulate forces from children.\n    if (numChildren) {\n      for (x = y = z = i = 0; i < numChildren; ++i) {\n        if ((q = treeNode[i]) && (c = Math.abs(q.value))) {\n          strength += q.value, weight += c, x += c * (q.x || 0), y += c * (q.y || 0), z += c * (q.z || 0);\n        }\n      }\n      strength *= Math.sqrt(4 / numChildren); // scale accumulated strength according to number of dimensions\n\n      treeNode.x = x / weight;\n      if (nDim > 1) {\n        treeNode.y = y / weight;\n      }\n      if (nDim > 2) {\n        treeNode.z = z / weight;\n      }\n    }\n\n    // For leaf nodes, accumulate forces from coincident nodes.\n    else {\n      q = treeNode;\n      q.x = q.data.x;\n      if (nDim > 1) {\n        q.y = q.data.y;\n      }\n      if (nDim > 2) {\n        q.z = q.data.z;\n      }\n      do strength += strengths[q.data.index]; while (q = q.next);\n    }\n    treeNode.value = strength;\n  }\n  function apply(treeNode, x1, arg1, arg2, arg3) {\n    if (!treeNode.value) return true;\n    var x2 = [arg1, arg2, arg3][nDim - 1];\n    var x = treeNode.x - node.x,\n      y = nDim > 1 ? treeNode.y - node.y : 0,\n      z = nDim > 2 ? treeNode.z - node.z : 0,\n      w = x2 - x1,\n      l = x * x + y * y + z * z;\n\n    // Apply the Barnes-Hut approximation if possible.\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (w * w / theta2 < l) {\n      if (l < distanceMax2) {\n        if (x === 0) x = jiggle(random), l += x * x;\n        if (nDim > 1 && y === 0) y = jiggle(random), l += y * y;\n        if (nDim > 2 && z === 0) z = jiggle(random), l += z * z;\n        if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n        node.vx += x * treeNode.value * alpha / l;\n        if (nDim > 1) {\n          node.vy += y * treeNode.value * alpha / l;\n        }\n        if (nDim > 2) {\n          node.vz += z * treeNode.value * alpha / l;\n        }\n      }\n      return true;\n    }\n\n    // Otherwise, process points directly.\n    else if (treeNode.length || l >= distanceMax2) return;\n\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (treeNode.data !== node || treeNode.next) {\n      if (x === 0) x = jiggle(random), l += x * x;\n      if (nDim > 1 && y === 0) y = jiggle(random), l += y * y;\n      if (nDim > 2 && z === 0) z = jiggle(random), l += z * z;\n      if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n    }\n    do if (treeNode.data !== node) {\n      w = strengths[treeNode.data.index] * alpha / l;\n      node.vx += x * w;\n      if (nDim > 1) {\n        node.vy += y * w;\n      }\n      if (nDim > 2) {\n        node.vz += z * w;\n      }\n    } while (treeNode = treeNode.next);\n  }\n  force.initialize = function (_nodes, ...args) {\n    nodes = _nodes;\n    random = args.find(arg => typeof arg === 'function') || Math.random;\n    nDim = args.find(arg => [1, 2, 3].includes(arg)) || 2;\n    initialize();\n  };\n  force.strength = function (_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n  force.distanceMin = function (_) {\n    return arguments.length ? (distanceMin2 = _ * _, force) : Math.sqrt(distanceMin2);\n  };\n  force.distanceMax = function (_) {\n    return arguments.length ? (distanceMax2 = _ * _, force) : Math.sqrt(distanceMax2);\n  };\n  force.theta = function (_) {\n    return arguments.length ? (theta2 = _ * _, force) : Math.sqrt(theta2);\n  };\n  return force;\n}", "map": {"version": 3, "names": ["binarytree", "quadtree", "octree", "constant", "jiggle", "x", "y", "z", "nodes", "nDim", "node", "random", "alpha", "strength", "strengths", "distanceMin2", "distanceMax2", "Infinity", "theta2", "force", "_", "i", "n", "length", "tree", "visitAfter", "accumulate", "visit", "apply", "initialize", "Array", "index", "treeNode", "q", "c", "weight", "numC<PERSON><PERSON>n", "Math", "abs", "value", "sqrt", "data", "next", "x1", "arg1", "arg2", "arg3", "x2", "w", "l", "vx", "vy", "vz", "_nodes", "args", "find", "arg", "includes", "arguments", "distanceMin", "distanceMax", "theta"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/d3-force-3d/src/manyBody.js"], "sourcesContent": ["import {binarytree} from \"d3-binarytree\";\nimport {quadtree} from \"d3-quadtree\";\nimport {octree} from \"d3-octree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\nimport {x, y, z} from \"./simulation.js\";\n\nexport default function() {\n  var nodes,\n      nDim,\n      node,\n      random,\n      alpha,\n      strength = constant(-30),\n      strengths,\n      distanceMin2 = 1,\n      distanceMax2 = Infinity,\n      theta2 = 0.81;\n\n  function force(_) {\n    var i,\n        n = nodes.length,\n        tree =\n            (nDim === 1 ? binarytree(nodes, x)\n            :(nDim === 2 ? quadtree(nodes, x, y)\n            :(nDim === 3 ? octree(nodes, x, y, z)\n            :null\n        ))).visitAfter(accumulate);\n\n    for (alpha = _, i = 0; i < n; ++i) node = nodes[i], tree.visit(apply);\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length, node;\n    strengths = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], strengths[node.index] = +strength(node, i, nodes);\n  }\n\n  function accumulate(treeNode) {\n    var strength = 0, q, c, weight = 0, x, y, z, i;\n    var numChildren = treeNode.length;\n\n    // For internal nodes, accumulate forces from children.\n    if (numChildren) {\n      for (x = y = z = i = 0; i < numChildren; ++i) {\n        if ((q = treeNode[i]) && (c = Math.abs(q.value))) {\n          strength += q.value, weight += c, x += c * (q.x || 0), y += c * (q.y || 0), z += c * (q.z || 0);\n        }\n      }\n      strength *= Math.sqrt(4 / numChildren); // scale accumulated strength according to number of dimensions\n\n      treeNode.x = x / weight;\n      if (nDim > 1) { treeNode.y = y / weight; }\n      if (nDim > 2) { treeNode.z = z / weight; }\n    }\n\n    // For leaf nodes, accumulate forces from coincident nodes.\n    else {\n      q = treeNode;\n      q.x = q.data.x;\n      if (nDim > 1) { q.y = q.data.y; }\n      if (nDim > 2) { q.z = q.data.z; }\n      do strength += strengths[q.data.index];\n      while (q = q.next);\n    }\n\n    treeNode.value = strength;\n  }\n\n  function apply(treeNode, x1, arg1, arg2, arg3) {\n    if (!treeNode.value) return true;\n    var x2 = [arg1, arg2, arg3][nDim-1];\n\n    var x = treeNode.x - node.x,\n        y = (nDim > 1 ? treeNode.y - node.y : 0),\n        z = (nDim > 2 ? treeNode.z - node.z : 0),\n        w = x2 - x1,\n        l = x * x + y * y + z * z;\n\n    // Apply the Barnes-Hut approximation if possible.\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (w * w / theta2 < l) {\n      if (l < distanceMax2) {\n        if (x === 0) x = jiggle(random), l += x * x;\n        if (nDim > 1 && y === 0) y = jiggle(random), l += y * y;\n        if (nDim > 2 && z === 0) z = jiggle(random), l += z * z;\n        if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n        node.vx += x * treeNode.value * alpha / l;\n        if (nDim > 1) { node.vy += y * treeNode.value * alpha / l; }\n        if (nDim > 2) { node.vz += z * treeNode.value * alpha / l; }\n      }\n      return true;\n    }\n\n    // Otherwise, process points directly.\n    else if (treeNode.length || l >= distanceMax2) return;\n\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (treeNode.data !== node || treeNode.next) {\n      if (x === 0) x = jiggle(random), l += x * x;\n      if (nDim > 1 && y === 0) y = jiggle(random), l += y * y;\n      if (nDim > 2 && z === 0) z = jiggle(random), l += z * z;\n      if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n    }\n\n    do if (treeNode.data !== node) {\n      w = strengths[treeNode.data.index] * alpha / l;\n      node.vx += x * w;\n      if (nDim > 1) { node.vy += y * w; }\n      if (nDim > 2) { node.vz += z * w; }\n    } while (treeNode = treeNode.next);\n  }\n\n  force.initialize = function(_nodes, ...args) {\n    nodes = _nodes;\n    random = args.find(arg => typeof arg === 'function') || Math.random;\n    nDim = args.find(arg => [1, 2, 3].includes(arg)) || 2;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.distanceMin = function(_) {\n    return arguments.length ? (distanceMin2 = _ * _, force) : Math.sqrt(distanceMin2);\n  };\n\n  force.distanceMax = function(_) {\n    return arguments.length ? (distanceMax2 = _ * _, force) : Math.sqrt(distanceMax2);\n  };\n\n  force.theta = function(_) {\n    return arguments.length ? (theta2 = _ * _, force) : Math.sqrt(theta2);\n  };\n\n  return force;\n}\n"], "mappings": "AAAA,SAAQA,UAAU,QAAO,eAAe;AACxC,SAAQC,QAAQ,QAAO,aAAa;AACpC,SAAQC,MAAM,QAAO,WAAW;AAChC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,MAAM,MAAM,aAAa;AAChC,SAAQC,CAAC,EAAEC,CAAC,EAAEC,CAAC,QAAO,iBAAiB;AAEvC,eAAe,YAAW;EACxB,IAAIC,KAAK;IACLC,IAAI;IACJC,IAAI;IACJC,MAAM;IACNC,KAAK;IACLC,QAAQ,GAAGV,QAAQ,CAAC,CAAC,EAAE,CAAC;IACxBW,SAAS;IACTC,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAGC,QAAQ;IACvBC,MAAM,GAAG,IAAI;EAEjB,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,IAAIC,CAAC;MACDC,CAAC,GAAGd,KAAK,CAACe,MAAM;MAChBC,IAAI,GACA,CAACf,IAAI,KAAK,CAAC,GAAGT,UAAU,CAACQ,KAAK,EAAEH,CAAC,CAAC,GAChCI,IAAI,KAAK,CAAC,GAAGR,QAAQ,CAACO,KAAK,EAAEH,CAAC,EAAEC,CAAC,CAAC,GAClCG,IAAI,KAAK,CAAC,GAAGP,MAAM,CAACM,KAAK,EAAEH,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GACpC,IACH,EAAEkB,UAAU,CAACC,UAAU,CAAC;IAE9B,KAAKd,KAAK,GAAGQ,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAEX,IAAI,GAAGF,KAAK,CAACa,CAAC,CAAC,EAAEG,IAAI,CAACG,KAAK,CAACC,KAAK,CAAC;EACvE;EAEA,SAASC,UAAUA,CAAA,EAAG;IACpB,IAAI,CAACrB,KAAK,EAAE;IACZ,IAAIa,CAAC;MAAEC,CAAC,GAAGd,KAAK,CAACe,MAAM;MAAEb,IAAI;IAC7BI,SAAS,GAAG,IAAIgB,KAAK,CAACR,CAAC,CAAC;IACxB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAEX,IAAI,GAAGF,KAAK,CAACa,CAAC,CAAC,EAAEP,SAAS,CAACJ,IAAI,CAACqB,KAAK,CAAC,GAAG,CAAClB,QAAQ,CAACH,IAAI,EAAEW,CAAC,EAAEb,KAAK,CAAC;EAC5F;EAEA,SAASkB,UAAUA,CAACM,QAAQ,EAAE;IAC5B,IAAInB,QAAQ,GAAG,CAAC;MAAEoB,CAAC;MAAEC,CAAC;MAAEC,MAAM,GAAG,CAAC;MAAE9B,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEc,CAAC;IAC9C,IAAIe,WAAW,GAAGJ,QAAQ,CAACT,MAAM;;IAEjC;IACA,IAAIa,WAAW,EAAE;MACf,KAAK/B,CAAC,GAAGC,CAAC,GAAGC,CAAC,GAAGc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,WAAW,EAAE,EAAEf,CAAC,EAAE;QAC5C,IAAI,CAACY,CAAC,GAAGD,QAAQ,CAACX,CAAC,CAAC,MAAMa,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACL,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE;UAChD1B,QAAQ,IAAIoB,CAAC,CAACM,KAAK,EAAEJ,MAAM,IAAID,CAAC,EAAE7B,CAAC,IAAI6B,CAAC,IAAID,CAAC,CAAC5B,CAAC,IAAI,CAAC,CAAC,EAAEC,CAAC,IAAI4B,CAAC,IAAID,CAAC,CAAC3B,CAAC,IAAI,CAAC,CAAC,EAAEC,CAAC,IAAI2B,CAAC,IAAID,CAAC,CAAC1B,CAAC,IAAI,CAAC,CAAC;QACjG;MACF;MACAM,QAAQ,IAAIwB,IAAI,CAACG,IAAI,CAAC,CAAC,GAAGJ,WAAW,CAAC,CAAC,CAAC;;MAExCJ,QAAQ,CAAC3B,CAAC,GAAGA,CAAC,GAAG8B,MAAM;MACvB,IAAI1B,IAAI,GAAG,CAAC,EAAE;QAAEuB,QAAQ,CAAC1B,CAAC,GAAGA,CAAC,GAAG6B,MAAM;MAAE;MACzC,IAAI1B,IAAI,GAAG,CAAC,EAAE;QAAEuB,QAAQ,CAACzB,CAAC,GAAGA,CAAC,GAAG4B,MAAM;MAAE;IAC3C;;IAEA;IAAA,KACK;MACHF,CAAC,GAAGD,QAAQ;MACZC,CAAC,CAAC5B,CAAC,GAAG4B,CAAC,CAACQ,IAAI,CAACpC,CAAC;MACd,IAAII,IAAI,GAAG,CAAC,EAAE;QAAEwB,CAAC,CAAC3B,CAAC,GAAG2B,CAAC,CAACQ,IAAI,CAACnC,CAAC;MAAE;MAChC,IAAIG,IAAI,GAAG,CAAC,EAAE;QAAEwB,CAAC,CAAC1B,CAAC,GAAG0B,CAAC,CAACQ,IAAI,CAAClC,CAAC;MAAE;MAChC,GAAGM,QAAQ,IAAIC,SAAS,CAACmB,CAAC,CAACQ,IAAI,CAACV,KAAK,CAAC,CAAC,QAChCE,CAAC,GAAGA,CAAC,CAACS,IAAI;IACnB;IAEAV,QAAQ,CAACO,KAAK,GAAG1B,QAAQ;EAC3B;EAEA,SAASe,KAAKA,CAACI,QAAQ,EAAEW,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;IAC7C,IAAI,CAACd,QAAQ,CAACO,KAAK,EAAE,OAAO,IAAI;IAChC,IAAIQ,EAAE,GAAG,CAACH,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC,CAACrC,IAAI,GAAC,CAAC,CAAC;IAEnC,IAAIJ,CAAC,GAAG2B,QAAQ,CAAC3B,CAAC,GAAGK,IAAI,CAACL,CAAC;MACvBC,CAAC,GAAIG,IAAI,GAAG,CAAC,GAAGuB,QAAQ,CAAC1B,CAAC,GAAGI,IAAI,CAACJ,CAAC,GAAG,CAAE;MACxCC,CAAC,GAAIE,IAAI,GAAG,CAAC,GAAGuB,QAAQ,CAACzB,CAAC,GAAGG,IAAI,CAACH,CAAC,GAAG,CAAE;MACxCyC,CAAC,GAAGD,EAAE,GAAGJ,EAAE;MACXM,CAAC,GAAG5C,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC;;IAE7B;IACA;IACA,IAAIyC,CAAC,GAAGA,CAAC,GAAG9B,MAAM,GAAG+B,CAAC,EAAE;MACtB,IAAIA,CAAC,GAAGjC,YAAY,EAAE;QACpB,IAAIX,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACO,MAAM,CAAC,EAAEsC,CAAC,IAAI5C,CAAC,GAAGA,CAAC;QAC3C,IAAII,IAAI,GAAG,CAAC,IAAIH,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACO,MAAM,CAAC,EAAEsC,CAAC,IAAI3C,CAAC,GAAGA,CAAC;QACvD,IAAIG,IAAI,GAAG,CAAC,IAAIF,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACO,MAAM,CAAC,EAAEsC,CAAC,IAAI1C,CAAC,GAAGA,CAAC;QACvD,IAAI0C,CAAC,GAAGlC,YAAY,EAAEkC,CAAC,GAAGZ,IAAI,CAACG,IAAI,CAACzB,YAAY,GAAGkC,CAAC,CAAC;QACrDvC,IAAI,CAACwC,EAAE,IAAI7C,CAAC,GAAG2B,QAAQ,CAACO,KAAK,GAAG3B,KAAK,GAAGqC,CAAC;QACzC,IAAIxC,IAAI,GAAG,CAAC,EAAE;UAAEC,IAAI,CAACyC,EAAE,IAAI7C,CAAC,GAAG0B,QAAQ,CAACO,KAAK,GAAG3B,KAAK,GAAGqC,CAAC;QAAE;QAC3D,IAAIxC,IAAI,GAAG,CAAC,EAAE;UAAEC,IAAI,CAAC0C,EAAE,IAAI7C,CAAC,GAAGyB,QAAQ,CAACO,KAAK,GAAG3B,KAAK,GAAGqC,CAAC;QAAE;MAC7D;MACA,OAAO,IAAI;IACb;;IAEA;IAAA,KACK,IAAIjB,QAAQ,CAACT,MAAM,IAAI0B,CAAC,IAAIjC,YAAY,EAAE;;IAE/C;IACA,IAAIgB,QAAQ,CAACS,IAAI,KAAK/B,IAAI,IAAIsB,QAAQ,CAACU,IAAI,EAAE;MAC3C,IAAIrC,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACO,MAAM,CAAC,EAAEsC,CAAC,IAAI5C,CAAC,GAAGA,CAAC;MAC3C,IAAII,IAAI,GAAG,CAAC,IAAIH,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACO,MAAM,CAAC,EAAEsC,CAAC,IAAI3C,CAAC,GAAGA,CAAC;MACvD,IAAIG,IAAI,GAAG,CAAC,IAAIF,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACO,MAAM,CAAC,EAAEsC,CAAC,IAAI1C,CAAC,GAAGA,CAAC;MACvD,IAAI0C,CAAC,GAAGlC,YAAY,EAAEkC,CAAC,GAAGZ,IAAI,CAACG,IAAI,CAACzB,YAAY,GAAGkC,CAAC,CAAC;IACvD;IAEA,GAAG,IAAIjB,QAAQ,CAACS,IAAI,KAAK/B,IAAI,EAAE;MAC7BsC,CAAC,GAAGlC,SAAS,CAACkB,QAAQ,CAACS,IAAI,CAACV,KAAK,CAAC,GAAGnB,KAAK,GAAGqC,CAAC;MAC9CvC,IAAI,CAACwC,EAAE,IAAI7C,CAAC,GAAG2C,CAAC;MAChB,IAAIvC,IAAI,GAAG,CAAC,EAAE;QAAEC,IAAI,CAACyC,EAAE,IAAI7C,CAAC,GAAG0C,CAAC;MAAE;MAClC,IAAIvC,IAAI,GAAG,CAAC,EAAE;QAAEC,IAAI,CAAC0C,EAAE,IAAI7C,CAAC,GAAGyC,CAAC;MAAE;IACpC,CAAC,QAAQhB,QAAQ,GAAGA,QAAQ,CAACU,IAAI;EACnC;EAEAvB,KAAK,CAACU,UAAU,GAAG,UAASwB,MAAM,EAAE,GAAGC,IAAI,EAAE;IAC3C9C,KAAK,GAAG6C,MAAM;IACd1C,MAAM,GAAG2C,IAAI,CAACC,IAAI,CAACC,GAAG,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IAAInB,IAAI,CAAC1B,MAAM;IACnEF,IAAI,GAAG6C,IAAI,CAACC,IAAI,CAACC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC,CAAC,IAAI,CAAC;IACrD3B,UAAU,CAAC,CAAC;EACd,CAAC;EAEDV,KAAK,CAACN,QAAQ,GAAG,UAASO,CAAC,EAAE;IAC3B,OAAOsC,SAAS,CAACnC,MAAM,IAAIV,QAAQ,GAAG,OAAOO,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGjB,QAAQ,CAAC,CAACiB,CAAC,CAAC,EAAES,UAAU,CAAC,CAAC,EAAEV,KAAK,IAAIN,QAAQ;EACnH,CAAC;EAEDM,KAAK,CAACwC,WAAW,GAAG,UAASvC,CAAC,EAAE;IAC9B,OAAOsC,SAAS,CAACnC,MAAM,IAAIR,YAAY,GAAGK,CAAC,GAAGA,CAAC,EAAED,KAAK,IAAIkB,IAAI,CAACG,IAAI,CAACzB,YAAY,CAAC;EACnF,CAAC;EAEDI,KAAK,CAACyC,WAAW,GAAG,UAASxC,CAAC,EAAE;IAC9B,OAAOsC,SAAS,CAACnC,MAAM,IAAIP,YAAY,GAAGI,CAAC,GAAGA,CAAC,EAAED,KAAK,IAAIkB,IAAI,CAACG,IAAI,CAACxB,YAAY,CAAC;EACnF,CAAC;EAEDG,KAAK,CAAC0C,KAAK,GAAG,UAASzC,CAAC,EAAE;IACxB,OAAOsC,SAAS,CAACnC,MAAM,IAAIL,MAAM,GAAGE,CAAC,GAAGA,CAAC,EAAED,KAAK,IAAIkB,IAAI,CAACG,IAAI,CAACtB,MAAM,CAAC;EACvE,CAAC;EAED,OAAOC,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}