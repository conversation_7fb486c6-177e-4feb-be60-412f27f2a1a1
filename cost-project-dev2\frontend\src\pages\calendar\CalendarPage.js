import React, { useState } from 'react';
import { Calendar, Card, Badge, Modal, Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

const CalendarPage = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);

  // 模拟事件数据
  const events = {
    '2024-01-15': [
      { type: 'success', content: '项目1启动会议' },
      { type: 'warning', content: '预算审核' },
    ],
    '2024-01-20': [
      { type: 'error', content: '项目截止日期' },
    ],
    '2024-01-25': [
      { type: 'processing', content: '成本评估会议' },
    ],
  };

  const getListData = (value) => {
    const dateStr = value.format('YYYY-MM-DD');
    return events[dateStr] || [];
  };

  const cellRender = (value, info) => {
    // 只处理日期类型的单元格
    if (info.type !== 'date') {
      return info.originNode;
    }

    const listData = getListData(value);
    return (
      <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
        {listData.map((item, index) => (
          <li key={index}>
            <Badge status={item.type} text={item.content} />
          </li>
        ))}
      </ul>
    );
  };

  const onSelect = (value) => {
    setSelectedDate(value);
    setModalVisible(true);
  };

  return (
    <div>
      <Card 
        title="项目日程管理" 
        extra={
          <Button type="primary" icon={<PlusOutlined />}>
            新建事件
          </Button>
        }
      >
        <Calendar
          cellRender={cellRender}
          onSelect={onSelect}
        />
      </Card>

      <Modal
        title={`${selectedDate?.format('YYYY-MM-DD')} 的事件`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setModalVisible(false)}>
            关闭
          </Button>,
          <Button key="add" type="primary">
            添加事件
          </Button>,
        ]}
      >
        {selectedDate && (
          <div>
            <p>日期：{selectedDate.format('YYYY年MM月DD日')}</p>
            <p>当日事件：</p>
            {getListData(selectedDate).length > 0 ? (
              <ul>
                {getListData(selectedDate).map((item, index) => (
                  <li key={index}>
                    <Badge status={item.type} text={item.content} />
                  </li>
                ))}
              </ul>
            ) : (
              <p>暂无事件</p>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CalendarPage; 