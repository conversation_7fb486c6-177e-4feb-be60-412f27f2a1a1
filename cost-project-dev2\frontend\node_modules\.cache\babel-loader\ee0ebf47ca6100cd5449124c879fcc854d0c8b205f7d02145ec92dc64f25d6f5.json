{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport BellTwoToneSvg from \"@ant-design/icons-svg/es/asn/BellTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BellTwoTone = function BellTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BellTwoToneSvg\n  }));\n};\n\n/**![bell](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAyMjBjLTU1LjYgMC0xMDcuOCAyMS42LTE0Ny4xIDYwLjlTMzA0IDM3Mi40IDMwNCA0Mjh2MzQwaDQxNlY0MjhjMC01NS42LTIxLjYtMTA3LjgtNjAuOS0xNDcuMVM1NjcuNiAyMjAgNTEyIDIyMHptMjgwIDIwOGMwLTE0MS4xLTEwNC4zLTI1Ny44LTI0MC0yNzcuMnYuMWMxMzUuNyAxOS40IDI0MCAxMzYgMjQwIDI3Ny4xek00NzIgMTUwLjl2LS4xQzMzNi4zIDE3MC4yIDIzMiAyODYuOSAyMzIgNDI4YzAtMTQxLjEgMTA0LjMtMjU3LjcgMjQwLTI3Ny4xeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODE2IDc2OGgtMjRWNDI4YzAtMTQxLjEtMTA0LjMtMjU3LjctMjQwLTI3Ny4xVjExMmMwLTIyLjEtMTcuOS00MC00MC00MHMtNDAgMTcuOS00MCA0MHYzOC45Yy0xMzUuNyAxOS40LTI0MCAxMzYtMjQwIDI3Ny4xdjM0MGgtMjRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjMyYzAgNC40IDMuNiA4IDggOGgyMTZjMCA2MS44IDUwLjIgMTEyIDExMiAxMTJzMTEyLTUwLjIgMTEyLTExMmgyMTZjNC40IDAgOC0zLjYgOC04di0zMmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNTEyIDg4OGMtMjYuNSAwLTQ4LTIxLjUtNDgtNDhoOTZjMCAyNi41LTIxLjUgNDgtNDggNDh6bTIwOC0xMjBIMzA0VjQyOGMwLTU1LjYgMjEuNi0xMDcuOCA2MC45LTE0Ny4xUzQ1Ni40IDIyMCA1MTIgMjIwYzU1LjYgMCAxMDcuOCAyMS42IDE0Ny4xIDYwLjlTNzIwIDM3Mi40IDcyMCA0Mjh2MzQweiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BellTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BellTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "BellTwoToneSvg", "AntdIcon", "BellTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/BellTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport BellTwoToneSvg from \"@ant-design/icons-svg/es/asn/BellTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BellTwoTone = function BellTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BellTwoToneSvg\n  }));\n};\n\n/**![bell](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAyMjBjLTU1LjYgMC0xMDcuOCAyMS42LTE0Ny4xIDYwLjlTMzA0IDM3Mi40IDMwNCA0Mjh2MzQwaDQxNlY0MjhjMC01NS42LTIxLjYtMTA3LjgtNjAuOS0xNDcuMVM1NjcuNiAyMjAgNTEyIDIyMHptMjgwIDIwOGMwLTE0MS4xLTEwNC4zLTI1Ny44LTI0MC0yNzcuMnYuMWMxMzUuNyAxOS40IDI0MCAxMzYgMjQwIDI3Ny4xek00NzIgMTUwLjl2LS4xQzMzNi4zIDE3MC4yIDIzMiAyODYuOSAyMzIgNDI4YzAtMTQxLjEgMTA0LjMtMjU3LjcgMjQwLTI3Ny4xeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODE2IDc2OGgtMjRWNDI4YzAtMTQxLjEtMTA0LjMtMjU3LjctMjQwLTI3Ny4xVjExMmMwLTIyLjEtMTcuOS00MC00MC00MHMtNDAgMTcuOS00MCA0MHYzOC45Yy0xMzUuNyAxOS40LTI0MCAxMzYtMjQwIDI3Ny4xdjM0MGgtMjRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjMyYzAgNC40IDMuNiA4IDggOGgyMTZjMCA2MS44IDUwLjIgMTEyIDExMiAxMTJzMTEyLTUwLjIgMTEyLTExMmgyMTZjNC40IDAgOC0zLjYgOC04di0zMmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNTEyIDg4OGMtMjYuNSAwLTQ4LTIxLjUtNDgtNDhoOTZjMCAyNi41LTIxLjUgNDgtNDggNDh6bTIwOC0xMjBIMzA0VjQyOGMwLTU1LjYgMjEuNi0xMDcuOCA2MC45LTE0Ny4xUzQ1Ni40IDIyMCA1MTIgMjIwYzU1LjYgMCAxMDcuOCAyMS42IDE0Ny4xIDYwLjlTNzIwIDM3Mi40IDcyMCA0Mjh2MzQweiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BellTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BellTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}