package com.costproject.service;

import com.costproject.entity.QuotaItem;
import com.costproject.entity.Resource;
import com.costproject.repository.QuotaItemRepository;
import com.costproject.repository.ResourceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.*;

@Service
public class QuotaService {

    private static final Logger log = LoggerFactory.getLogger(QuotaService.class);

    private final QuotaItemRepository quotaItemRepository;
    private final ResourceRepository resourceRepository;
    
    public QuotaService(QuotaItemRepository quotaItemRepository,
                       ResourceRepository resourceRepository) {
        this.quotaItemRepository = quotaItemRepository;
        this.resourceRepository = resourceRepository;
    }

    /**
     * 创建定额项
     */
    @Transactional
    public QuotaItem createQuotaItem(QuotaItem quotaItem) {
        validateQuotaItem(quotaItem);
        calculateCosts(quotaItem);
        return quotaItemRepository.save(quotaItem);
    }

    /**
     * 更新定额项
     */
    @Transactional
    public QuotaItem updateQuotaItem(Long id, QuotaItem quotaItem) {
        QuotaItem existing = getQuotaItem(id);
        updateQuotaItemFields(existing, quotaItem);
        calculateCosts(existing);
        return quotaItemRepository.save(existing);
    }

    /**
     * 删除定额项
     */
    @Transactional
    public void deleteQuotaItem(Long id) {
        QuotaItem quotaItem = getQuotaItem(id);
        quotaItemRepository.delete(quotaItem);
    }

    /**
     * 获取定额项
     */
    public QuotaItem getQuotaItem(Long id) {
        return quotaItemRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("定额项不存在: " + id));
    }

    /**
     * 查询定额列表
     */
    public List<QuotaItem> searchQuotaItems(String keyword) {
        if (keyword != null) {
            return quotaItemRepository.findByNameContainingOrCodeContaining(keyword, keyword);
        }
        return quotaItemRepository.findAll();
    }

    /**
     * 从Excel导入定额库
     */
    @Transactional
    public List<QuotaItem> importFromExcel(MultipartFile file) throws IOException {
        List<QuotaItem> importedItems = new ArrayList<>();
        // TODO: 实现Excel导入逻辑
        return importedItems;
    }

    /**
     * 从Markdown文件导入定额库
     */
    @Transactional
    public List<QuotaItem> importFromMarkdown(MultipartFile file) throws IOException {
        List<QuotaItem> importedItems = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            String line;
            QuotaItem currentItem = null;
            
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("## ")) { // 新定额项
                    if (currentItem != null) {
                        importedItems.add(createQuotaItem(currentItem));
                    }
                    currentItem = new QuotaItem();
                    currentItem.setName(line.substring(3).trim());
                } else if (line.startsWith("- code: ")) {
                    currentItem.setCode(line.substring(7).trim());
                } else if (line.startsWith("- unit: ")) {
                    currentItem.setUnit(line.substring(7).trim());
                } else if (line.startsWith("- description: ")) {
                    currentItem.setDescription(line.substring(14).trim());
                }
                // TODO: 解析更多字段
            }
            
            if (currentItem != null) {
                importedItems.add(createQuotaItem(currentItem));
            }
        }
        
        return importedItems;
    }

    /**
     * 计算定额成本
     */
    public void calculateCosts(QuotaItem quotaItem) {
        // 计算人工费
        BigDecimal laborCost = quotaItem.getBasicSalary()
            .add(quotaItem.getSalaryAllowance())
            .add(quotaItem.getAuxiliaryWage())
            .add(quotaItem.getWelfareFee())
            .add(quotaItem.getLaborProtectionFee());
        quotaItem.setLaborCost(laborCost);

        // 计算材料费
        BigDecimal materialCost = quotaItem.getMaterialPrice()
            .add(quotaItem.getTransportFee())
            .add(quotaItem.getLossFee())
            .add(quotaItem.getPurchaseAndStorageFee())
            .add(quotaItem.getTestingFee());
        quotaItem.setMaterialCost(materialCost);

        // 计算机械费
        BigDecimal machineCost = quotaItem.getDepreciationFee()
            .add(quotaItem.getMajorRepairFee())
            .add(quotaItem.getRoutineRepairFee())
            .add(quotaItem.getInstallationAndTransportFee())
            .add(quotaItem.getMachineOperatorFee())
            .add(quotaItem.getFuelAndPowerFee())
            .add(quotaItem.getRoadFee());
        quotaItem.setMachineCost(machineCost);

        // 计算综合单价
        quotaItem.setUnitPrice(laborCost.add(materialCost).add(machineCost));
    }

    /**
     * 统计资源消耗
     */
    public Map<String, BigDecimal> calculateResourceConsumption(List<Long> quotaItemIds) {
        Map<String, BigDecimal> consumption = new HashMap<>();
        
        List<QuotaItem> quotaItems = quotaItemRepository.findAllById(quotaItemIds);
        for (QuotaItem item : quotaItems) {
            Map<String, BigDecimal> itemConsumption = item.getResourceConsumption();
            if (itemConsumption != null) {
                itemConsumption.forEach((resourceId, quantity) -> 
                    consumption.merge(resourceId, quantity, BigDecimal::add));
            }
        }
        
        return consumption;
    }

    /**
     * 生成资源需求计划
     */
    public Map<String, Object> generateResourcePlan(List<Long> quotaItemIds) {
        Map<String, Object> plan = new HashMap<>();
        
        // 按资源类型统计
        Map<Resource.ResourceType, BigDecimal> typeConsumption = new HashMap<>();
        Map<String, BigDecimal> consumption = calculateResourceConsumption(quotaItemIds);
        
        consumption.forEach((resourceId, quantity) -> {
            Resource resource = resourceRepository.findById(Long.valueOf(resourceId))
                .orElseThrow(() -> new RuntimeException("资源不存在: " + resourceId));
            
            typeConsumption.merge(resource.getType(), quantity, BigDecimal::add);
        });
        
        plan.put("typeConsumption", typeConsumption);
        plan.put("detailedConsumption", consumption);
        
        return plan;
    }

    // 私有辅助方法

    private void validateQuotaItem(QuotaItem quotaItem) {
        if (quotaItem.getCode() == null || quotaItem.getCode().isEmpty()) {
            throw new IllegalArgumentException("定额编号不能为空");
        }
        if (quotaItem.getName() == null || quotaItem.getName().isEmpty()) {
            throw new IllegalArgumentException("定额名称不能为空");
        }
        // TODO: 添加更多验证规则
    }

    private void updateQuotaItemFields(QuotaItem existing, QuotaItem updated) {
        existing.setName(updated.getName());
        existing.setDescription(updated.getDescription());
        existing.setUnit(updated.getUnit());
        
        // 更新人工费用组成
        existing.setBasicSalary(updated.getBasicSalary());
        existing.setSalaryAllowance(updated.getSalaryAllowance());
        existing.setAuxiliaryWage(updated.getAuxiliaryWage());
        existing.setWelfareFee(updated.getWelfareFee());
        existing.setLaborProtectionFee(updated.getLaborProtectionFee());
        
        // 更新材料费用组成
        existing.setMaterialPrice(updated.getMaterialPrice());
        existing.setTransportFee(updated.getTransportFee());
        existing.setLossFee(updated.getLossFee());
        existing.setPurchaseAndStorageFee(updated.getPurchaseAndStorageFee());
        existing.setTestingFee(updated.getTestingFee());
        
        // 更新机械费用组成
        existing.setDepreciationFee(updated.getDepreciationFee());
        existing.setMajorRepairFee(updated.getMajorRepairFee());
        existing.setRoutineRepairFee(updated.getRoutineRepairFee());
        existing.setInstallationAndTransportFee(updated.getInstallationAndTransportFee());
        existing.setMachineOperatorFee(updated.getMachineOperatorFee());
        existing.setFuelAndPowerFee(updated.getFuelAndPowerFee());
        existing.setRoadFee(updated.getRoadFee());
        
        // 不更新code，这是标识字段
    }
}
