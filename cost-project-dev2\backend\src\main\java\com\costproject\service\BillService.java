package com.costproject.service;

import com.costproject.entity.BillItem;
import com.costproject.entity.QuotaItem;
import com.costproject.entity.Task;
import com.costproject.repository.BillItemRepository;
import com.costproject.repository.QuotaItemRepository;
import com.costproject.repository.TaskRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BillService {

    private static final Logger log = LoggerFactory.getLogger(BillService.class);

    private final BillItemRepository billItemRepository;
    private final QuotaItemRepository quotaItemRepository;
    private final TaskRepository taskRepository;
    
    public BillService(BillItemRepository billItemRepository, 
                      QuotaItemRepository quotaItemRepository,
                      TaskRepository taskRepository) {
        this.billItemRepository = billItemRepository;
        this.quotaItemRepository = quotaItemRepository;
        this.taskRepository = taskRepository;
    }

    /**
     * 创建清单项
     */
    @Transactional
    public BillItem createBillItem(BillItem billItem) {
        validateBillItem(billItem);
        return billItemRepository.save(billItem);
    }

    /**
     * 更新清单项
     */
    @Transactional
    public BillItem updateBillItem(Long id, BillItem billItem) {
        BillItem existing = getBillItem(id);
        updateBillItemFields(existing, billItem);
        return billItemRepository.save(existing);
    }

    /**
     * 删除清单项
     */
    @Transactional
    public void deleteBillItem(Long id) {
        BillItem billItem = getBillItem(id);
        // 解除与任务的关联
        for (Task task : billItem.getTasks()) {
            task.getBillItems().remove(billItem);
            taskRepository.save(task);
        }
        billItemRepository.delete(billItem);
    }

    /**
     * 获取清单项
     */
    public BillItem getBillItem(Long id) {
        return billItemRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("清单项不存在: " + id));
    }

    /**
     * 查询清单列表
     */
    public List<BillItem> searchBillItems(String keyword, String chapter) {
        if (keyword != null && chapter != null) {
            return billItemRepository.findByNameContainingAndChapter(keyword, chapter);
        } else if (keyword != null) {
            return billItemRepository.findByNameContaining(keyword);
        } else if (chapter != null) {
            return billItemRepository.findByChapter(chapter);
        }
        return billItemRepository.findAll();
    }

    /**
     * 关联定额项
     */
    @Transactional
    public void linkQuotaItem(Long billItemId, Long quotaItemId) {
        BillItem billItem = getBillItem(billItemId);
        QuotaItem quotaItem = quotaItemRepository.findById(quotaItemId)
            .orElseThrow(() -> new RuntimeException("定额项不存在: " + quotaItemId));
        
        billItem.addQuotaItem(quotaItem);
        billItemRepository.save(billItem);
    }

    /**
     * 解除定额关联
     */
    @Transactional
    public void unlinkQuotaItem(Long billItemId, Long quotaItemId) {
        BillItem billItem = getBillItem(billItemId);
        QuotaItem quotaItem = quotaItemRepository.findById(quotaItemId)
            .orElseThrow(() -> new RuntimeException("定额项不存在: " + quotaItemId));
        
        billItem.removeQuotaItem(quotaItem);
        billItemRepository.save(billItem);
    }

    /**
     * 关联任务
     */
    @Transactional
    public void linkTask(Long billItemId, Long taskId) {
        BillItem billItem = getBillItem(billItemId);
        Task task = taskRepository.findById(taskId)
            .orElseThrow(() -> new RuntimeException("任务不存在: " + taskId));
        
        if (!task.getBillItems().contains(billItem)) {
            task.getBillItems().add(billItem);
            billItem.getTasks().add(task);
            billItemRepository.save(billItem);
        }
        taskRepository.save(task);
    }

    /**
     * 从Excel导入清单
     */
    @Transactional
    public List<BillItem> importFromExcel(MultipartFile file) throws IOException {
        List<BillItem> importedItems = new ArrayList<>();
        
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            
            // 跳过表头
            Iterator<Row> rows = sheet.iterator();
            if (rows.hasNext()) {
                rows.next(); // 跳过表头行
            }
            
            // 处理数据行
            while (rows.hasNext()) {
                Row row = rows.next();
                BillItem billItem = parseBillItemFromExcel(row);
                if (billItem != null) {
                    importedItems.add(createBillItem(billItem));
                }
            }
        }
        
        return importedItems;
    }

    /**
     * 导出清单到Excel
     */
    public Workbook exportToExcel(List<BillItem> billItems) {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("清单");
        
        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"编码", "章节", "名称", "描述", "单位", "工程量", "综合单价", "合价"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }
        
        // 填充数据
        int rowNum = 1;
        for (BillItem item : billItems) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(item.getCode());
            row.createCell(1).setCellValue(item.getChapter());
            row.createCell(2).setCellValue(item.getName());
            row.createCell(3).setCellValue(item.getDescription());
            row.createCell(4).setCellValue(item.getUnit());
            row.createCell(5).setCellValue(item.getQuantity().doubleValue());
            row.createCell(6).setCellValue(item.getUnitPrice().doubleValue());
            row.createCell(7).setCellValue(item.getTotalPrice().doubleValue());
        }
        
        return workbook;
    }

    /**
     * 计算清单合价
     */
    public Map<String, BigDecimal> calculateTotalCost(List<Long> billItemIds) {
        List<BillItem> billItems = billItemRepository.findAllById(billItemIds);
        
        BigDecimal totalCost = BigDecimal.ZERO;
        Map<String, BigDecimal> costBreakdown = new HashMap<>();
        
        for (BillItem item : billItems) {
            BigDecimal itemTotal = item.getTotalPrice();
            totalCost = totalCost.add(itemTotal);
            
            // TODO: 按资源类型分类汇总成本
        }
        
        costBreakdown.put("total", totalCost);
        return costBreakdown;
    }

    // 私有辅助方法

    private void validateBillItem(BillItem billItem) {
        if (billItem.getCode() == null || billItem.getCode().length() != 12) {
            throw new IllegalArgumentException("清单编码必须为12位");
        }
        if (billItem.getQuantity() != null && billItem.getQuantity().compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("工程量不能为负数");
        }
    }

    private void updateBillItemFields(BillItem existing, BillItem updated) {
        existing.setName(updated.getName());
        existing.setDescription(updated.getDescription());
        existing.setUnit(updated.getUnit());
        existing.setQuantity(updated.getQuantity());
        existing.setUnitPrice(updated.getUnitPrice());
        // 不更新code和chapter，这些是标识字段
    }

    private BillItem parseBillItemFromExcel(Row row) {
        try {
            BillItem item = new BillItem();
            item.setCode(getStringCellValue(row.getCell(0)));
            item.setChapter(getStringCellValue(row.getCell(1)));
            item.setName(getStringCellValue(row.getCell(2)));
            item.setDescription(getStringCellValue(row.getCell(3)));
            item.setUnit(getStringCellValue(row.getCell(4)));
            item.setQuantity(new BigDecimal(getStringCellValue(row.getCell(5))));
            item.setUnitPrice(new BigDecimal(getStringCellValue(row.getCell(6))));
            return item;
        } catch (Exception e) {
            log.error("解析Excel行失败: " + e.getMessage());
            return null;
        }
    }

    private String getStringCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return "";
        }
    }
}