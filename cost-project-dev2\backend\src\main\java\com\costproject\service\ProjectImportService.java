package com.costproject.service;

import lombok.extern.slf4j.Slf4j;
import net.sf.mpxj.*;
import net.sf.mpxj.reader.*;
import net.sf.mpxj.mpp.MPPReader;
import net.sf.mpxj.mpx.MPXReader;
import net.sf.mpxj.mspdi.MSPDIReader;
import net.sf.mpxj.primavera.PrimaveraXERFileReader;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

@Slf4j
@Service
public class ProjectImportService {

    /**
     * 预览项目文件
     * @param file 上传的文件
     * @param format 文件格式
     * @return 项目预览数据
     */
    public Map<String, Object> previewProjectFile(MultipartFile file, String format) throws Exception {
        ProjectFile project = readProjectFile(file, format);
        return extractPreviewData(project);
    }

    /**
     * 导入项目文件
     * @param file 上传的文件
     * @param format 文件格式
     * @return 导入的项目数据
     */
    public Map<String, Object> importProjectFile(MultipartFile file, String format) throws Exception {
        ProjectFile project = readProjectFile(file, format);
        return importProjectData(project);
    }

    /**
     * 读取项目文件
     * @param file 上传的文件
     * @param format 文件格式
     * @return ProjectFile对象
     */
    private ProjectFile readProjectFile(MultipartFile file, String format) throws Exception {
        // 创建临时文件
        File tempFile = File.createTempFile("project_", "." + format);
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(file.getBytes());
        }

        // 根据文件格式选择对应的读取器
        ProjectReader reader = switch (format.toLowerCase()) {
            case "mpp" -> new MPPReader();
            case "mpx" -> new MPXReader();
            case "xml" -> new MSPDIReader();
            case "xer" -> new PrimaveraXERFileReader();
            case "pmxml" -> new PrimaveraXERFileReader();
            default -> throw new IllegalArgumentException("Unsupported file format: " + format);
        };

        // 读取项目文件
        ProjectFile project = reader.read(tempFile);
        tempFile.delete();
        return project;
    }

    /**
     * 提取预览数据
     * @param project ProjectFile对象
     * @return 预览数据
     */
    private Map<String, Object> extractPreviewData(ProjectFile project) {
        Map<String, Object> previewData = new HashMap<>();
        List<Map<String, Object>> tasks = new ArrayList<>();

        // 提取任务信息
        for (Task task : project.getTasks()) {
            if (task != null && task.getName() != null) {
                Map<String, Object> taskData = new HashMap<>();
                taskData.put("id", task.getID());
                taskData.put("name", task.getName());
                taskData.put("startDate", task.getStart());
                taskData.put("endDate", task.getFinish());
                taskData.put("duration", task.getDuration());
                taskData.put("progress", task.getPercentageComplete());
                taskData.put("wbs", task.getWBS());
                taskData.put("level", task.getOutlineLevel());
                tasks.add(taskData);
            }
        }

        // 提取项目基本信息
        previewData.put("projectName", project.getProjectProperties().getName());
        previewData.put("startDate", project.getProjectProperties().getStartDate());
        previewData.put("finishDate", project.getProjectProperties().getFinishDate());
        previewData.put("tasks", tasks);
        previewData.put("taskCount", tasks.size());

        return previewData;
    }

    /**
     * 导入项目数据
     * @param project ProjectFile对象
     * @return 导入的数据
     */
    private Map<String, Object> importProjectData(ProjectFile project) {
        Map<String, Object> importedData = new HashMap<>();
        List<Map<String, Object>> tasks = new ArrayList<>();
        List<Map<String, Object>> resources = new ArrayList<>();
        List<Map<String, Object>> assignments = new ArrayList<>();

        // 导入任务
        for (Task task : project.getTasks()) {
            if (task != null && task.getName() != null) {
                Map<String, Object> taskData = new HashMap<>();
                taskData.put("id", task.getID());
                taskData.put("name", task.getName());
                taskData.put("startDate", task.getStart());
                taskData.put("endDate", task.getFinish());
                taskData.put("duration", task.getDuration());
                taskData.put("progress", task.getPercentageComplete());
                taskData.put("wbs", task.getWBS());
                taskData.put("level", task.getOutlineLevel());
                taskData.put("predecessors", extractPredecessors(task));
                taskData.put("successors", extractSuccessors(task));
                tasks.add(taskData);
            }
        }

        // 导入资源
        for (Resource resource : project.getResources()) {
            if (resource != null && resource.getName() != null) {
                Map<String, Object> resourceData = new HashMap<>();
                resourceData.put("id", resource.getID());
                resourceData.put("name", resource.getName());
                resourceData.put("type", resource.getType());
                resourceData.put("cost", resource.getStandardRate());
                resources.add(resourceData);
            }
        }

        // 导入资源分配
        for (ResourceAssignment assignment : project.getResourceAssignments()) {
            if (assignment != null && assignment.getResource() != null && assignment.getTask() != null) {
                Map<String, Object> assignmentData = new HashMap<>();
                assignmentData.put("taskId", assignment.getTask().getID());
                assignmentData.put("resourceId", assignment.getResource().getID());
                assignmentData.put("units", assignment.getUnits());
                assignmentData.put("work", assignment.getWork());
                assignments.add(assignmentData);
            }
        }

        // 设置导入数据
        importedData.put("projectName", project.getProjectProperties().getName());
        importedData.put("startDate", project.getProjectProperties().getStartDate());
        importedData.put("finishDate", project.getProjectProperties().getFinishDate());
        importedData.put("tasks", tasks);
        importedData.put("resources", resources);
        importedData.put("assignments", assignments);

        return importedData;
    }

    /**
     * 提取前置任务
     * @param task 任务
     * @return 前置任务列表
     */
    private List<Map<String, Object>> extractPredecessors(Task task) {
        List<Map<String, Object>> predecessors = new ArrayList<>();
        for (Relation relation : task.getPredecessors()) {
            Map<String, Object> predecessor = new HashMap<>();
            predecessor.put("id", relation.getTargetTask().getID());
            predecessor.put("type", relation.getType());
            predecessor.put("lag", relation.getLag());
            predecessors.add(predecessor);
        }
        return predecessors;
    }

    /**
     * 提取后续任务
     * @param task 任务
     * @return 后续任务列表
     */
    private List<Map<String, Object>> extractSuccessors(Task task) {
        List<Map<String, Object>> successors = new ArrayList<>();
        for (Relation relation : task.getSuccessors()) {
            Map<String, Object> successor = new HashMap<>();
            successor.put("id", relation.getTargetTask().getID());
            successor.put("type", relation.getType());
            successor.put("lag", relation.getLag());
            successors.add(successor);
        }
        return successors;
    }
}
