{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { Rect } from '@antv/g';\nimport { isFunction } from '@antv/util';\nimport { COMBO_KEY, CanvasEvent, ComboEvent, CommonEvent } from '../constants';\nimport { getBBoxSize, getCombinedBBox } from '../utils/bbox';\nimport { idOf } from '../utils/id';\nimport { subStyleProps } from '../utils/prefix';\nimport { divide, subtract } from '../utils/vector';\nimport { BaseBehavior } from './base-behavior';\n/**\n * <zh/> 拖拽元素交互\n *\n * <en/> Drag element behavior\n */\nexport class DragElement extends BaseBehavior {\n  constructor(context, options) {\n    super(context, Object.assign({}, DragElement.defaultOptions, options));\n    this.enable = false;\n    this.enableElements = ['node', 'combo'];\n    this.target = [];\n    this.shadowOrigin = [0, 0];\n    this.hiddenEdges = [];\n    this.isDragging = false;\n    /**\n     * <zh/> 拖拽放下的回调\n     *\n     * <en/> Callback when dragging is released\n     * @param event - <zh/> 拖拽事件对象 | <en/> drag event object\n     */\n    this.onDrop = event => __awaiter(this, void 0, void 0, function* () {\n      var _a;\n      if (this.options.dropEffect !== 'link') return;\n      const {\n        model,\n        element\n      } = this.context;\n      const modifiedParentId = event.target.id;\n      this.target.forEach(id => {\n        const originalParent = model.getParentData(id, COMBO_KEY);\n        // 如果是在原父 combo 内部拖拽，需要刷新 combo 数据\n        // If it is a drag and drop within the original parent combo, you need to refresh the combo data\n        if (originalParent && idOf(originalParent) === modifiedParentId) {\n          model.refreshComboData(modifiedParentId);\n        }\n        model.setParent(id, modifiedParentId, COMBO_KEY);\n      });\n      yield (_a = element === null || element === void 0 ? void 0 : element.draw({\n        animation: true\n      })) === null || _a === void 0 ? void 0 : _a.finished;\n    });\n    this.setCursor = event => {\n      if (this.isDragging) return;\n      const {\n        type\n      } = event;\n      const {\n        canvas\n      } = this.context;\n      const {\n        cursor\n      } = this.options;\n      if (type === CommonEvent.POINTER_ENTER) canvas.setCursor((cursor === null || cursor === void 0 ? void 0 : cursor.grab) || 'grab');else canvas.setCursor((cursor === null || cursor === void 0 ? void 0 : cursor.default) || 'default');\n    };\n    this.onDragStart = this.onDragStart.bind(this);\n    this.onDrag = this.onDrag.bind(this);\n    this.onDragEnd = this.onDragEnd.bind(this);\n    this.onDrop = this.onDrop.bind(this);\n    this.bindEvents();\n  }\n  /**\n   * <zh/> 更新元素拖拽配置\n   *\n   * <en/> Update the element dragging configuration\n   * @param options - <zh/> 配置项 | <en/> options\n   * @internal\n   */\n  update(options) {\n    this.unbindEvents();\n    super.update(options);\n    this.bindEvents();\n  }\n  bindEvents() {\n    const {\n      graph,\n      canvas\n    } = this.context;\n    // @ts-expect-error internal property\n    const $canvas = canvas.getLayer().getContextService().$canvas;\n    if ($canvas) {\n      $canvas.addEventListener('blur', this.onDragEnd);\n      $canvas.addEventListener('contextmenu', this.onDragEnd);\n    }\n    this.enableElements.forEach(type => {\n      graph.on(`${type}:${CommonEvent.DRAG_START}`, this.onDragStart);\n      graph.on(`${type}:${CommonEvent.DRAG}`, this.onDrag);\n      graph.on(`${type}:${CommonEvent.DRAG_END}`, this.onDragEnd);\n      graph.on(`${type}:${CommonEvent.POINTER_ENTER}`, this.setCursor);\n      graph.on(`${type}:${CommonEvent.POINTER_LEAVE}`, this.setCursor);\n    });\n    if (['link'].includes(this.options.dropEffect)) {\n      graph.on(ComboEvent.DROP, this.onDrop);\n      graph.on(CanvasEvent.DROP, this.onDrop);\n    }\n  }\n  /**\n   * <zh/> 获取当前选中的节点 id 集合\n   *\n   * <en/> Get the id collection of the currently selected node\n   * @param currTarget - <zh/> 当前拖拽目标元素 id 集合 | <en/> The id collection of the current drag target element\n   * @returns <zh/> 当前选中的节点 id 集合 | <en/> The id collection of the currently selected node\n   * @internal\n   */\n  getSelectedNodeIDs(currTarget) {\n    return Array.from(new Set(this.context.graph.getElementDataByState('node', this.options.state).map(node => node.id).concat(currTarget)));\n  }\n  /**\n   * Get the delta of the drag\n   * @param event - drag event object\n   * @returns delta\n   * @internal\n   */\n  getDelta(event) {\n    const zoom = this.context.graph.getZoom();\n    return divide([event.dx, event.dy], zoom);\n  }\n  /**\n   * <zh/> 拖拽开始时的回调\n   *\n   * <en/> Callback when dragging starts\n   * @param event - <zh/> 拖拽事件对象 | <en/> drag event object\n   * @internal\n   */\n  onDragStart(event) {\n    var _a;\n    this.enable = this.validate(event);\n    if (!this.enable) return;\n    const {\n      batch,\n      canvas,\n      graph\n    } = this.context;\n    canvas.setCursor(((_a = this.options.cursor) === null || _a === void 0 ? void 0 : _a.grabbing) || 'grabbing');\n    this.isDragging = true;\n    batch.startBatch();\n    // 如果当前节点是选中状态，则查询出画布中所有选中的节点，否则只拖拽当前节点\n    // If the current node is selected, query all selected nodes in the canvas, otherwise only drag the current node\n    const id = event.target.id;\n    const states = graph.getElementState(id);\n    if (states.includes(this.options.state)) this.target = this.getSelectedNodeIDs([id]);else this.target = [id];\n    this.hideEdge();\n    this.context.graph.frontElement(this.target);\n    if (this.options.shadow) this.createShadow(this.target);\n  }\n  /**\n   * <zh/> 拖拽过程中的回调\n   *\n   * <en/> Callback when dragging\n   * @param event - <zh/> 拖拽事件对象 | <en/> drag event object\n   * @internal\n   */\n  onDrag(event) {\n    if (!this.enable) return;\n    const delta = this.getDelta(event);\n    if (this.options.shadow) this.moveShadow(delta);else this.moveElement(this.target, delta);\n  }\n  /**\n   * <zh/> 元素拖拽结束的回调\n   *\n   * <en/> Callback when dragging ends\n   * @internal\n   */\n  onDragEnd() {\n    var _a, _b, _c;\n    if (!this.enable) return; // It can be called multiple times\n    this.enable = false;\n    if (this.options.shadow) {\n      if (!this.shadow) return;\n      this.shadow.style.visibility = 'hidden';\n      const {\n        x = 0,\n        y = 0\n      } = this.shadow.attributes;\n      const [dx, dy] = subtract([+x, +y], this.shadowOrigin);\n      this.moveElement(this.target, [dx, dy]);\n    }\n    this.showEdges();\n    (_b = (_a = this.options).onFinish) === null || _b === void 0 ? void 0 : _b.call(_a, this.target);\n    const {\n      batch,\n      canvas\n    } = this.context;\n    batch.endBatch();\n    canvas.setCursor(((_c = this.options.cursor) === null || _c === void 0 ? void 0 : _c.grab) || 'grab');\n    this.isDragging = false;\n    this.target = [];\n  }\n  /**\n   * <zh/> 验证元素是否允许拖拽\n   *\n   * <en/> Verify if the element is allowed to be dragged\n   * @param event - <zh/> 拖拽事件对象 | <en/> drag event object\n   * @returns <zh/> 是否允许拖拽 | <en/> Whether to allow dragging\n   * @internal\n   */\n  validate(event) {\n    if (this.destroyed) return false;\n    const {\n      enable\n    } = this.options;\n    if (isFunction(enable)) return enable(event);\n    return !!enable;\n  }\n  /**\n   * <zh/> 移动元素\n   *\n   * <en/> Move the element\n   * @param ids - <zh/> 元素 id 集合 | <en/> element id collection\n   * @param offset <zh/> 偏移量 | <en/> offset\n   * @internal\n   */\n  moveElement(ids, offset) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const {\n        graph,\n        model\n      } = this.context;\n      const {\n        dropEffect\n      } = this.options;\n      if (dropEffect === 'move') ids.forEach(id => model.refreshComboData(id));\n      graph.translateElementBy(Object.fromEntries(ids.map(id => [id, offset])), false);\n    });\n  }\n  moveShadow(offset) {\n    if (!this.shadow) return;\n    const {\n      x = 0,\n      y = 0\n    } = this.shadow.attributes;\n    const [dx, dy] = offset;\n    this.shadow.attr({\n      x: +x + dx,\n      y: +y + dy\n    });\n  }\n  createShadow(target) {\n    const shadowStyle = subStyleProps(this.options, 'shadow');\n    const bbox = getCombinedBBox(target.map(id => this.context.element.getElement(id).getBounds()));\n    const [x, y] = bbox.min;\n    this.shadowOrigin = [x, y];\n    const [width, height] = getBBoxSize(bbox);\n    const positionStyle = {\n      width,\n      height,\n      x,\n      y\n    };\n    if (this.shadow) {\n      this.shadow.attr(Object.assign(Object.assign(Object.assign({}, shadowStyle), positionStyle), {\n        visibility: 'visible'\n      }));\n    } else {\n      this.shadow = new Rect({\n        style: Object.assign(Object.assign(Object.assign({\n          // @ts-ignore $layer is not in the type definition\n          $layer: 'transient'\n        }, shadowStyle), positionStyle), {\n          pointerEvents: 'none'\n        })\n      });\n      this.context.canvas.appendChild(this.shadow);\n    }\n  }\n  showEdges() {\n    if (this.options.shadow || this.hiddenEdges.length === 0) return;\n    this.context.graph.showElement(this.hiddenEdges);\n    this.hiddenEdges = [];\n  }\n  /**\n   * Hide the edge\n   * @internal\n   */\n  hideEdge() {\n    const {\n      hideEdge,\n      shadow\n    } = this.options;\n    if (hideEdge === 'none' || shadow) return;\n    const {\n      graph\n    } = this.context;\n    if (hideEdge === 'all') this.hiddenEdges = graph.getEdgeData().map(idOf);else {\n      this.hiddenEdges = Array.from(new Set(this.target.map(id => graph.getRelatedEdgesData(id, hideEdge).map(idOf)).flat()));\n    }\n    graph.hideElement(this.hiddenEdges);\n  }\n  unbindEvents() {\n    const {\n      graph,\n      canvas\n    } = this.context;\n    // @ts-expect-error internal property\n    const $canvas = canvas.getLayer().getContextService().$canvas;\n    if ($canvas) {\n      $canvas.removeEventListener('blur', this.onDragEnd);\n      $canvas.removeEventListener('contextmenu', this.onDragEnd);\n    }\n    this.enableElements.forEach(type => {\n      graph.off(`${type}:${CommonEvent.DRAG_START}`, this.onDragStart);\n      graph.off(`${type}:${CommonEvent.DRAG}`, this.onDrag);\n      graph.off(`${type}:${CommonEvent.DRAG_END}`, this.onDragEnd);\n      graph.off(`${type}:${CommonEvent.POINTER_ENTER}`, this.setCursor);\n      graph.off(`${type}:${CommonEvent.POINTER_LEAVE}`, this.setCursor);\n    });\n    graph.off(`combo:${CommonEvent.DROP}`, this.onDrop);\n    graph.off(`canvas:${CommonEvent.DROP}`, this.onDrop);\n  }\n  destroy() {\n    var _a;\n    this.unbindEvents();\n    (_a = this.shadow) === null || _a === void 0 ? void 0 : _a.destroy();\n    super.destroy();\n  }\n}\nDragElement.defaultOptions = {\n  animation: true,\n  enable: event => ['node', 'combo'].includes(event.targetType),\n  dropEffect: 'move',\n  state: 'selected',\n  hideEdge: 'none',\n  shadow: false,\n  shadowZIndex: 100,\n  shadowFill: '#F3F9FF',\n  shadowFillOpacity: 0.5,\n  shadowStroke: '#1890FF',\n  shadowStrokeOpacity: 0.9,\n  shadowLineDash: [5, 5],\n  cursor: {\n    default: 'default',\n    grab: 'grab',\n    grabbing: 'grabbing'\n  }\n};", "map": {"version": 3, "names": ["Rect", "isFunction", "COMBO_KEY", "CanvasEvent", "ComboEvent", "CommonEvent", "getBBoxSize", "getCombinedBBox", "idOf", "subStyleProps", "divide", "subtract", "BaseBehavior", "DragElement", "constructor", "context", "options", "Object", "assign", "defaultOptions", "enable", "enableElements", "target", "<PERSON><PERSON><PERSON><PERSON>", "hidden<PERSON><PERSON>", "isDragging", "onDrop", "event", "__awaiter", "dropEffect", "model", "element", "modifiedParentId", "id", "for<PERSON>ach", "originalParent", "getParentData", "refreshComboData", "setParent", "_a", "draw", "animation", "finished", "setCursor", "type", "canvas", "cursor", "POINTER_ENTER", "grab", "default", "onDragStart", "bind", "onDrag", "onDragEnd", "bindEvents", "update", "unbindEvents", "graph", "$canvas", "<PERSON><PERSON><PERSON><PERSON>", "getContextService", "addEventListener", "on", "DRAG_START", "DRAG", "DRAG_END", "POINTER_LEAVE", "includes", "DROP", "getSelectedNodeIDs", "currTarget", "Array", "from", "Set", "getElementDataByState", "state", "map", "node", "concat", "<PERSON><PERSON><PERSON><PERSON>", "zoom", "getZoom", "dx", "dy", "validate", "batch", "grabbing", "startBatch", "states", "getElementState", "hideEdge", "frontElement", "shadow", "createShadow", "delta", "moveShadow", "moveElement", "style", "visibility", "x", "y", "attributes", "showEdges", "_b", "onFinish", "call", "endBatch", "_c", "destroyed", "ids", "offset", "translateElementBy", "fromEntries", "attr", "shadowStyle", "bbox", "getElement", "getBounds", "min", "width", "height", "positionStyle", "$layer", "pointerEvents", "append<PERSON><PERSON><PERSON>", "length", "showElement", "getEdgeData", "getRelatedEdgesData", "flat", "hideElement", "removeEventListener", "off", "destroy", "targetType", "shadowZIndex", "shadowFill", "shadowFillOpacity", "shadowStroke", "shadowStrokeOpacity", "shadowLineDash"], "sources": ["../../src/behaviors/drag-element.ts"], "sourcesContent": [null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAASA,IAAI,QAAQ,SAAS;AAC9B,SAASC,UAAU,QAAQ,YAAY;AACvC,SAASC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,WAAW,QAAQ,cAAc;AAG9E,SAASC,WAAW,EAAEC,eAAe,QAAQ,eAAe;AAC5D,SAASC,IAAI,QAAQ,aAAa;AAClC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,MAAM,EAAEC,QAAQ,QAAQ,iBAAiB;AAElD,SAASC,YAAY,QAAQ,iBAAiB;AA0G9C;;;;;AAKA,OAAM,MAAOC,WAAY,SAAQD,YAAgC;EAmC/DE,YAAYC,OAAuB,EAAEC,OAA2B;IAC9D,KAAK,CAACD,OAAO,EAAEE,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEL,WAAW,CAACM,cAAc,EAAEH,OAAO,CAAC,CAAC;IAf9D,KAAAI,MAAM,GAAY,KAAK;IAEzB,KAAAC,cAAc,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;IAEhC,KAAAC,MAAM,GAAS,EAAE;IAInB,KAAAC,YAAY,GAAU,CAAC,CAAC,EAAE,CAAC,CAAC;IAE5B,KAAAC,WAAW,GAAS,EAAE;IAEtB,KAAAC,UAAU,GAAY,KAAK;IAkJnC;;;;;;IAMQ,KAAAC,MAAM,GAAUC,KAAwB,IAAIC,SAAA;;MAClD,IAAI,IAAI,CAACZ,OAAO,CAACa,UAAU,KAAK,MAAM,EAAE;MACxC,MAAM;QAAEC,KAAK;QAAEC;MAAO,CAAE,GAAG,IAAI,CAAChB,OAAO;MACvC,MAAMiB,gBAAgB,GAAGL,KAAK,CAACL,MAAM,CAACW,EAAE;MACxC,IAAI,CAACX,MAAM,CAACY,OAAO,CAAED,EAAE,IAAI;QACzB,MAAME,cAAc,GAAGL,KAAK,CAACM,aAAa,CAACH,EAAE,EAAE/B,SAAS,CAAC;QACzD;QACA;QACA,IAAIiC,cAAc,IAAI3B,IAAI,CAAC2B,cAAc,CAAC,KAAKH,gBAAgB,EAAE;UAC/DF,KAAK,CAACO,gBAAgB,CAACL,gBAAgB,CAAC;QAC1C;QACAF,KAAK,CAACQ,SAAS,CAACL,EAAE,EAAED,gBAAgB,EAAE9B,SAAS,CAAC;MAClD,CAAC,CAAC;MACF,MAAM,CAAAqC,EAAA,GAAAR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAES,IAAI,CAAC;QAAEC,SAAS,EAAE;MAAI,CAAE,CAAC,cAAAF,EAAA,uBAAAA,EAAA,CAAEG,QAAQ;IACpD,CAAC;IAEO,KAAAC,SAAS,GAAIhB,KAAoB,IAAI;MAC3C,IAAI,IAAI,CAACF,UAAU,EAAE;MACrB,MAAM;QAAEmB;MAAI,CAAE,GAAGjB,KAAK;MACtB,MAAM;QAAEkB;MAAM,CAAE,GAAG,IAAI,CAAC9B,OAAO;MAC/B,MAAM;QAAE+B;MAAM,CAAE,GAAG,IAAI,CAAC9B,OAAO;MAE/B,IAAI4B,IAAI,KAAKvC,WAAW,CAAC0C,aAAa,EAAEF,MAAM,CAACF,SAAS,CAAC,CAAAG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,IAAI,KAAI,MAAM,CAAC,CAAC,KAC5EH,MAAM,CAACF,SAAS,CAAC,CAAAG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,OAAO,KAAI,SAAS,CAAC;IACrD,CAAC;IA5KC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACD,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAACE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACF,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACzB,MAAM,GAAG,IAAI,CAACA,MAAM,CAACyB,IAAI,CAAC,IAAI,CAAC;IAEpC,IAAI,CAACG,UAAU,EAAE;EACnB;EACA;;;;;;;EAOOC,MAAMA,CAACvC,OAAoC;IAChD,IAAI,CAACwC,YAAY,EAAE;IACnB,KAAK,CAACD,MAAM,CAACvC,OAAO,CAAC;IACrB,IAAI,CAACsC,UAAU,EAAE;EACnB;EAEQA,UAAUA,CAAA;IAChB,MAAM;MAAEG,KAAK;MAAEZ;IAAM,CAAE,GAAG,IAAI,CAAC9B,OAAO;IAEtC;IACA,MAAM2C,OAAO,GAAsBb,MAAM,CAACc,QAAQ,EAAE,CAACC,iBAAiB,EAAE,CAACF,OAAO;IAChF,IAAIA,OAAO,EAAE;MACXA,OAAO,CAACG,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACR,SAAS,CAAC;MAChDK,OAAO,CAACG,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACR,SAAS,CAAC;IACzD;IAEA,IAAI,CAAChC,cAAc,CAACa,OAAO,CAAEU,IAAI,IAAI;MACnCa,KAAK,CAACK,EAAE,CAAC,GAAGlB,IAAI,IAAIvC,WAAW,CAAC0D,UAAU,EAAE,EAAE,IAAI,CAACb,WAAW,CAAC;MAC/DO,KAAK,CAACK,EAAE,CAAC,GAAGlB,IAAI,IAAIvC,WAAW,CAAC2D,IAAI,EAAE,EAAE,IAAI,CAACZ,MAAM,CAAC;MACpDK,KAAK,CAACK,EAAE,CAAC,GAAGlB,IAAI,IAAIvC,WAAW,CAAC4D,QAAQ,EAAE,EAAE,IAAI,CAACZ,SAAS,CAAC;MAC3DI,KAAK,CAACK,EAAE,CAAC,GAAGlB,IAAI,IAAIvC,WAAW,CAAC0C,aAAa,EAAE,EAAE,IAAI,CAACJ,SAAS,CAAC;MAChEc,KAAK,CAACK,EAAE,CAAC,GAAGlB,IAAI,IAAIvC,WAAW,CAAC6D,aAAa,EAAE,EAAE,IAAI,CAACvB,SAAS,CAAC;IAClE,CAAC,CAAC;IAEF,IAAI,CAAC,MAAM,CAAC,CAACwB,QAAQ,CAAC,IAAI,CAACnD,OAAO,CAACa,UAAU,CAAC,EAAE;MAC9C4B,KAAK,CAACK,EAAE,CAAC1D,UAAU,CAACgE,IAAI,EAAE,IAAI,CAAC1C,MAAM,CAAC;MACtC+B,KAAK,CAACK,EAAE,CAAC3D,WAAW,CAACiE,IAAI,EAAE,IAAI,CAAC1C,MAAM,CAAC;IACzC;EACF;EAEA;;;;;;;;EAQU2C,kBAAkBA,CAACC,UAAgB;IAC3C,OAAOC,KAAK,CAACC,IAAI,CACf,IAAIC,GAAG,CACL,IAAI,CAAC1D,OAAO,CAAC0C,KAAK,CACfiB,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC1D,OAAO,CAAC2D,KAAK,CAAC,CACjDC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAAC5C,EAAE,CAAC,CACtB6C,MAAM,CAACR,UAAU,CAAC,CACtB,CACF;EACH;EAEA;;;;;;EAMUS,QAAQA,CAACpD,KAAwB;IACzC,MAAMqD,IAAI,GAAG,IAAI,CAACjE,OAAO,CAAC0C,KAAK,CAACwB,OAAO,EAAE;IACzC,OAAOvE,MAAM,CAAC,CAACiB,KAAK,CAACuD,EAAE,EAAEvD,KAAK,CAACwD,EAAE,CAAC,EAAEH,IAAI,CAAC;EAC3C;EAEA;;;;;;;EAOU9B,WAAWA,CAACvB,KAAwB;;IAC5C,IAAI,CAACP,MAAM,GAAG,IAAI,CAACgE,QAAQ,CAACzD,KAAK,CAAC;IAClC,IAAI,CAAC,IAAI,CAACP,MAAM,EAAE;IAElB,MAAM;MAAEiE,KAAK;MAAExC,MAAM;MAAEY;IAAK,CAAE,GAAG,IAAI,CAAC1C,OAAO;IAC7C8B,MAAM,CAACF,SAAS,CAAC,EAAAJ,EAAA,OAAI,CAACvB,OAAQ,CAAC8B,MAAM,cAAAP,EAAA,uBAAAA,EAAA,CAAE+C,QAAQ,KAAI,UAAU,CAAC;IAC9D,IAAI,CAAC7D,UAAU,GAAG,IAAI;IACtB4D,KAAM,CAACE,UAAU,EAAE;IAEnB;IACA;IACA,MAAMtD,EAAE,GAAGN,KAAK,CAACL,MAAM,CAACW,EAAE;IAC1B,MAAMuD,MAAM,GAAG/B,KAAK,CAACgC,eAAe,CAACxD,EAAE,CAAC;IACxC,IAAIuD,MAAM,CAACrB,QAAQ,CAAC,IAAI,CAACnD,OAAO,CAAC2D,KAAK,CAAC,EAAE,IAAI,CAACrD,MAAM,GAAG,IAAI,CAAC+C,kBAAkB,CAAC,CAACpC,EAAE,CAAC,CAAC,CAAC,KAChF,IAAI,CAACX,MAAM,GAAG,CAACW,EAAE,CAAC;IAEvB,IAAI,CAACyD,QAAQ,EAAE;IACf,IAAI,CAAC3E,OAAO,CAAC0C,KAAK,CAACkC,YAAY,CAAC,IAAI,CAACrE,MAAM,CAAC;IAC5C,IAAI,IAAI,CAACN,OAAO,CAAC4E,MAAM,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAACvE,MAAM,CAAC;EACzD;EAEA;;;;;;;EAOU8B,MAAMA,CAACzB,KAAwB;IACvC,IAAI,CAAC,IAAI,CAACP,MAAM,EAAE;IAClB,MAAM0E,KAAK,GAAG,IAAI,CAACf,QAAQ,CAACpD,KAAK,CAAC;IAElC,IAAI,IAAI,CAACX,OAAO,CAAC4E,MAAM,EAAE,IAAI,CAACG,UAAU,CAACD,KAAK,CAAC,CAAC,KAC3C,IAAI,CAACE,WAAW,CAAC,IAAI,CAAC1E,MAAM,EAAEwE,KAAK,CAAC;EAC3C;EAEA;;;;;;EAMUzC,SAASA,CAAA;;IACjB,IAAI,CAAC,IAAI,CAACjC,MAAM,EAAE,OAAO,CAAC;IAC1B,IAAI,CAACA,MAAM,GAAG,KAAK;IACnB,IAAI,IAAI,CAACJ,OAAO,CAAC4E,MAAM,EAAE;MACvB,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE;MAClB,IAAI,CAACA,MAAM,CAACK,KAAK,CAACC,UAAU,GAAG,QAAQ;MACvC,MAAM;QAAEC,CAAC,GAAG,CAAC;QAAEC,CAAC,GAAG;MAAC,CAAE,GAAG,IAAI,CAACR,MAAM,CAACS,UAAU;MAC/C,MAAM,CAACnB,EAAE,EAAEC,EAAE,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAACwF,CAAC,EAAE,CAACC,CAAC,CAAC,EAAE,IAAI,CAAC7E,YAAY,CAAC;MACtD,IAAI,CAACyE,WAAW,CAAC,IAAI,CAAC1E,MAAM,EAAE,CAAC4D,EAAE,EAAEC,EAAE,CAAC,CAAC;IACzC;IACA,IAAI,CAACmB,SAAS,EAAE;IAChB,CAAAC,EAAA,IAAAhE,EAAA,OAAI,CAACvB,OAAO,EAACwF,QAAQ,cAAAD,EAAA,uBAAAA,EAAA,CAAAE,IAAA,CAAAlE,EAAA,EAAG,IAAI,CAACjB,MAAM,CAAC;IACpC,MAAM;MAAE+D,KAAK;MAAExC;IAAM,CAAE,GAAG,IAAI,CAAC9B,OAAO;IACtCsE,KAAM,CAACqB,QAAQ,EAAE;IACjB7D,MAAM,CAACF,SAAS,CAAC,EAAAgE,EAAA,OAAI,CAAC3F,OAAQ,CAAC8B,MAAM,cAAA6D,EAAA,uBAAAA,EAAA,CAAE3D,IAAI,KAAI,MAAM,CAAC;IACtD,IAAI,CAACvB,UAAU,GAAG,KAAK;IACvB,IAAI,CAACH,MAAM,GAAG,EAAE;EAClB;EAkCA;;;;;;;;EAQU8D,QAAQA,CAACzD,KAAwB;IACzC,IAAI,IAAI,CAACiF,SAAS,EAAE,OAAO,KAAK;IAChC,MAAM;MAAExF;IAAM,CAAE,GAAG,IAAI,CAACJ,OAAO;IAC/B,IAAIf,UAAU,CAACmB,MAAM,CAAC,EAAE,OAAOA,MAAM,CAACO,KAAK,CAAC;IAC5C,OAAO,CAAC,CAACP,MAAM;EACjB;EAEA;;;;;;;;EAQgB4E,WAAWA,CAACa,GAAS,EAAEC,MAAa;;MAClD,MAAM;QAAErD,KAAK;QAAE3B;MAAK,CAAE,GAAG,IAAI,CAACf,OAAO;MACrC,MAAM;QAAEc;MAAU,CAAE,GAAG,IAAI,CAACb,OAAO;MAEnC,IAAIa,UAAU,KAAK,MAAM,EAAEgF,GAAG,CAAC3E,OAAO,CAAED,EAAE,IAAKH,KAAK,CAACO,gBAAgB,CAACJ,EAAE,CAAC,CAAC;MAC1EwB,KAAK,CAACsD,kBAAkB,CAAC9F,MAAM,CAAC+F,WAAW,CAACH,GAAG,CAACjC,GAAG,CAAE3C,EAAE,IAAK,CAACA,EAAE,EAAE6E,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;IACpF,CAAC;;EAEOf,UAAUA,CAACe,MAAa;IAC9B,IAAI,CAAC,IAAI,CAAClB,MAAM,EAAE;IAClB,MAAM;MAAEO,CAAC,GAAG,CAAC;MAAEC,CAAC,GAAG;IAAC,CAAE,GAAG,IAAI,CAACR,MAAM,CAACS,UAAU;IAC/C,MAAM,CAACnB,EAAE,EAAEC,EAAE,CAAC,GAAG2B,MAAM;IACvB,IAAI,CAAClB,MAAM,CAACqB,IAAI,CAAC;MAAEd,CAAC,EAAE,CAACA,CAAC,GAAGjB,EAAE;MAAEkB,CAAC,EAAE,CAACA,CAAC,GAAGjB;IAAE,CAAE,CAAC;EAC9C;EAEQU,YAAYA,CAACvE,MAAY;IAC/B,MAAM4F,WAAW,GAAGzG,aAAa,CAAC,IAAI,CAACO,OAAO,EAAE,QAAQ,CAAC;IAEzD,MAAMmG,IAAI,GAAG5G,eAAe,CAACe,MAAM,CAACsD,GAAG,CAAE3C,EAAE,IAAK,IAAI,CAAClB,OAAO,CAACgB,OAAQ,CAACqF,UAAU,CAACnF,EAAE,CAAE,CAACoF,SAAS,EAAE,CAAC,CAAC;IACnG,MAAM,CAAClB,CAAC,EAAEC,CAAC,CAAC,GAAGe,IAAI,CAACG,GAAG;IACvB,IAAI,CAAC/F,YAAY,GAAG,CAAC4E,CAAC,EAAEC,CAAC,CAAC;IAC1B,MAAM,CAACmB,KAAK,EAAEC,MAAM,CAAC,GAAGlH,WAAW,CAAC6G,IAAI,CAAC;IACzC,MAAMM,aAAa,GAAG;MAAEF,KAAK;MAAEC,MAAM;MAAErB,CAAC;MAAEC;IAAC,CAAE;IAE7C,IAAI,IAAI,CAACR,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACqB,IAAI,CAAAhG,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACXgG,WAAW,GACXO,aAAa;QAChBvB,UAAU,EAAE;MAAS,GACrB;IACJ,CAAC,MAAM;MACL,IAAI,CAACN,MAAM,GAAG,IAAI5F,IAAI,CAAC;QACrBiG,KAAK,EAAAhF,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA;UACH;UACAwG,MAAM,EAAE;QAAW,GAChBR,WAAW,GACXO,aAAa;UAChBE,aAAa,EAAE;QAAM;OAExB,CAAC;MACF,IAAI,CAAC5G,OAAO,CAAC8B,MAAM,CAAC+E,WAAW,CAAC,IAAI,CAAChC,MAAM,CAAC;IAC9C;EACF;EAEQU,SAASA,CAAA;IACf,IAAI,IAAI,CAACtF,OAAO,CAAC4E,MAAM,IAAI,IAAI,CAACpE,WAAW,CAACqG,MAAM,KAAK,CAAC,EAAE;IAC1D,IAAI,CAAC9G,OAAO,CAAC0C,KAAK,CAACqE,WAAW,CAAC,IAAI,CAACtG,WAAW,CAAC;IAChD,IAAI,CAACA,WAAW,GAAG,EAAE;EACvB;EAEA;;;;EAIUkE,QAAQA,CAAA;IAChB,MAAM;MAAEA,QAAQ;MAAEE;IAAM,CAAE,GAAG,IAAI,CAAC5E,OAAO;IACzC,IAAI0E,QAAQ,KAAK,MAAM,IAAIE,MAAM,EAAE;IACnC,MAAM;MAAEnC;IAAK,CAAE,GAAG,IAAI,CAAC1C,OAAO;IAC9B,IAAI2E,QAAQ,KAAK,KAAK,EAAE,IAAI,CAAClE,WAAW,GAAGiC,KAAK,CAACsE,WAAW,EAAE,CAACnD,GAAG,CAACpE,IAAI,CAAC,CAAC,KACpE;MACH,IAAI,CAACgB,WAAW,GAAG+C,KAAK,CAACC,IAAI,CAC3B,IAAIC,GAAG,CAAC,IAAI,CAACnD,MAAM,CAACsD,GAAG,CAAE3C,EAAE,IAAKwB,KAAK,CAACuE,mBAAmB,CAAC/F,EAAE,EAAEyD,QAAQ,CAAC,CAACd,GAAG,CAACpE,IAAI,CAAC,CAAC,CAACyH,IAAI,EAAE,CAAC,CAC3F;IACH;IACAxE,KAAK,CAACyE,WAAW,CAAC,IAAI,CAAC1G,WAAW,CAAC;EACrC;EAEQgC,YAAYA,CAAA;IAClB,MAAM;MAAEC,KAAK;MAAEZ;IAAM,CAAE,GAAG,IAAI,CAAC9B,OAAO;IAEtC;IACA,MAAM2C,OAAO,GAAsBb,MAAM,CAACc,QAAQ,EAAE,CAACC,iBAAiB,EAAE,CAACF,OAAO;IAChF,IAAIA,OAAO,EAAE;MACXA,OAAO,CAACyE,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC9E,SAAS,CAAC;MACnDK,OAAO,CAACyE,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC9E,SAAS,CAAC;IAC5D;IAEA,IAAI,CAAChC,cAAc,CAACa,OAAO,CAAEU,IAAI,IAAI;MACnCa,KAAK,CAAC2E,GAAG,CAAC,GAAGxF,IAAI,IAAIvC,WAAW,CAAC0D,UAAU,EAAE,EAAE,IAAI,CAACb,WAAW,CAAC;MAChEO,KAAK,CAAC2E,GAAG,CAAC,GAAGxF,IAAI,IAAIvC,WAAW,CAAC2D,IAAI,EAAE,EAAE,IAAI,CAACZ,MAAM,CAAC;MACrDK,KAAK,CAAC2E,GAAG,CAAC,GAAGxF,IAAI,IAAIvC,WAAW,CAAC4D,QAAQ,EAAE,EAAE,IAAI,CAACZ,SAAS,CAAC;MAC5DI,KAAK,CAAC2E,GAAG,CAAC,GAAGxF,IAAI,IAAIvC,WAAW,CAAC0C,aAAa,EAAE,EAAE,IAAI,CAACJ,SAAS,CAAC;MACjEc,KAAK,CAAC2E,GAAG,CAAC,GAAGxF,IAAI,IAAIvC,WAAW,CAAC6D,aAAa,EAAE,EAAE,IAAI,CAACvB,SAAS,CAAC;IACnE,CAAC,CAAC;IAEFc,KAAK,CAAC2E,GAAG,CAAC,SAAS/H,WAAW,CAAC+D,IAAI,EAAE,EAAE,IAAI,CAAC1C,MAAM,CAAC;IACnD+B,KAAK,CAAC2E,GAAG,CAAC,UAAU/H,WAAW,CAAC+D,IAAI,EAAE,EAAE,IAAI,CAAC1C,MAAM,CAAC;EACtD;EAEO2G,OAAOA,CAAA;;IACZ,IAAI,CAAC7E,YAAY,EAAE;IACnB,CAAAjB,EAAA,OAAI,CAACqD,MAAM,cAAArD,EAAA,uBAAAA,EAAA,CAAE8F,OAAO,EAAE;IACtB,KAAK,CAACA,OAAO,EAAE;EACjB;;AAtUOxH,WAAA,CAAAM,cAAc,GAAgC;EACnDsB,SAAS,EAAE,IAAI;EACfrB,MAAM,EAAGO,KAAK,IAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAACwC,QAAQ,CAACxC,KAAK,CAAC2G,UAAU,CAAC;EAC/DzG,UAAU,EAAE,MAAM;EAClB8C,KAAK,EAAE,UAAU;EACjBe,QAAQ,EAAE,MAAM;EAChBE,MAAM,EAAE,KAAK;EACb2C,YAAY,EAAE,GAAG;EACjBC,UAAU,EAAE,SAAS;EACrBC,iBAAiB,EAAE,GAAG;EACtBC,YAAY,EAAE,SAAS;EACvBC,mBAAmB,EAAE,GAAG;EACxBC,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACtB9F,MAAM,EAAE;IACNG,OAAO,EAAE,SAAS;IAClBD,IAAI,EAAE,MAAM;IACZsC,QAAQ,EAAE;;CAEb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}