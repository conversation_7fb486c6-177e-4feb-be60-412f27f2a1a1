{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { devUseWarning } from '../../_util/warning';\nfunction filter(items) {\n  return items.filter(item => item);\n}\nexport default function useLegacyItems(items, children) {\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Tabs');\n    warning.deprecated(!children, 'Tabs.TabPane', 'items');\n  }\n  if (items) {\n    return items.map(item => {\n      var _a;\n      const mergedDestroyOnHidden = (_a = item.destroyOnHidden) !== null && _a !== void 0 ? _a : item.destroyInactiveTabPane;\n      return Object.assign(Object.assign({}, item), {\n        // TODO: In the future, destroyInactiveTabPane in rc-tabs needs to be upgrade to destroyOnHidden\n        destroyInactiveTabPane: mergedDestroyOnHidden\n      });\n    });\n  }\n  const childrenItems = toArray(children).map(node => {\n    if (/*#__PURE__*/React.isValidElement(node)) {\n      const {\n        key,\n        props\n      } = node;\n      const _a = props || {},\n        {\n          tab\n        } = _a,\n        restProps = __rest(_a, [\"tab\"]);\n      const item = Object.assign(Object.assign({\n        key: String(key)\n      }, restProps), {\n        label: tab\n      });\n      return item;\n    }\n    return null;\n  });\n  return filter(childrenItems);\n}", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "toArray", "devUseW<PERSON>ning", "filter", "items", "item", "useLegacyItems", "children", "process", "env", "NODE_ENV", "warning", "deprecated", "map", "_a", "mergedDestroyOnHidden", "destroyOnHidden", "destroyInactiveTabPane", "assign", "childrenItems", "node", "isValidElement", "key", "props", "tab", "restProps", "String", "label"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/antd/es/tabs/hooks/useLegacyItems.js"], "sourcesContent": ["var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { devUseWarning } from '../../_util/warning';\nfunction filter(items) {\n  return items.filter(item => item);\n}\nexport default function useLegacyItems(items, children) {\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Tabs');\n    warning.deprecated(!children, 'Tabs.TabPane', 'items');\n  }\n  if (items) {\n    return items.map(item => {\n      var _a;\n      const mergedDestroyOnHidden = (_a = item.destroyOnHidden) !== null && _a !== void 0 ? _a : item.destroyInactiveTabPane;\n      return Object.assign(Object.assign({}, item), {\n        // TODO: In the future, destroyInactiveTabPane in rc-tabs needs to be upgrade to destroyOnHidden\n        destroyInactiveTabPane: mergedDestroyOnHidden\n      });\n    });\n  }\n  const childrenItems = toArray(children).map(node => {\n    if (/*#__PURE__*/React.isValidElement(node)) {\n      const {\n        key,\n        props\n      } = node;\n      const _a = props || {},\n        {\n          tab\n        } = _a,\n        restProps = __rest(_a, [\"tab\"]);\n      const item = Object.assign(Object.assign({\n        key: String(key)\n      }, restProps), {\n        label: tab\n      });\n      return item;\n    }\n    return null;\n  });\n  return filter(childrenItems);\n}"], "mappings": "AAAA,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,MAAMA,CAACC,KAAK,EAAE;EACrB,OAAOA,KAAK,CAACD,MAAM,CAACE,IAAI,IAAIA,IAAI,CAAC;AACnC;AACA,eAAe,SAASC,cAAcA,CAACF,KAAK,EAAEG,QAAQ,EAAE;EACtD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGT,aAAa,CAAC,MAAM,CAAC;IACrCS,OAAO,CAACC,UAAU,CAAC,CAACL,QAAQ,EAAE,cAAc,EAAE,OAAO,CAAC;EACxD;EACA,IAAIH,KAAK,EAAE;IACT,OAAOA,KAAK,CAACS,GAAG,CAACR,IAAI,IAAI;MACvB,IAAIS,EAAE;MACN,MAAMC,qBAAqB,GAAG,CAACD,EAAE,GAAGT,IAAI,CAACW,eAAe,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGT,IAAI,CAACY,sBAAsB;MACtH,OAAO1B,MAAM,CAAC2B,MAAM,CAAC3B,MAAM,CAAC2B,MAAM,CAAC,CAAC,CAAC,EAAEb,IAAI,CAAC,EAAE;QAC5C;QACAY,sBAAsB,EAAEF;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA,MAAMI,aAAa,GAAGlB,OAAO,CAACM,QAAQ,CAAC,CAACM,GAAG,CAACO,IAAI,IAAI;IAClD,IAAI,aAAapB,KAAK,CAACqB,cAAc,CAACD,IAAI,CAAC,EAAE;MAC3C,MAAM;QACJE,GAAG;QACHC;MACF,CAAC,GAAGH,IAAI;MACR,MAAMN,EAAE,GAAGS,KAAK,IAAI,CAAC,CAAC;QACpB;UACEC;QACF,CAAC,GAAGV,EAAE;QACNW,SAAS,GAAGvC,MAAM,CAAC4B,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;MACjC,MAAMT,IAAI,GAAGd,MAAM,CAAC2B,MAAM,CAAC3B,MAAM,CAAC2B,MAAM,CAAC;QACvCI,GAAG,EAAEI,MAAM,CAACJ,GAAG;MACjB,CAAC,EAAEG,SAAS,CAAC,EAAE;QACbE,KAAK,EAAEH;MACT,CAAC,CAAC;MACF,OAAOnB,IAAI;IACb;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF,OAAOF,MAAM,CAACgB,aAAa,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}