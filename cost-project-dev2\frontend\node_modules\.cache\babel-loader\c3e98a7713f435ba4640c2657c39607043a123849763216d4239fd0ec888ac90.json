{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TagsOutlinedSvg from \"@ant-design/icons-svg/es/asn/TagsOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TagsOutlined = function TagsOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TagsOutlinedSvg\n  }));\n};\n\n/**![tags](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4My4yIDc5MC4zTDg2MS40IDQxMmMxLjctMS43IDIuNS00IDIuMy02LjNsLTI1LjUtMzAxLjRjLS43LTcuOC02LjgtMTMuOS0xNC42LTE0LjZMNTIyLjIgNjQuM2MtMi4zLS4yLTQuNy42LTYuMyAyLjNMMTM3LjcgNDQ0LjhhOC4wMyA4LjAzIDAgMDAwIDExLjNsMzM0LjIgMzM0LjJjMy4xIDMuMiA4LjIgMy4yIDExLjMgMHptNjIuNi02NTEuN2wyMjQuNiAxOSAxOSAyMjQuNkw0NzcuNSA2OTQgMjMzLjkgNDUwLjVsMzExLjktMzExLjl6bTYwLjE2IDE4Ni4yM2E0OCA0OCAwIDEwNjcuODgtNjcuODkgNDggNDggMCAxMC02Ny44OCA2Ny44OXpNODg5LjcgNTM5LjhsLTM5LjYtMzkuNWE4LjAzIDguMDMgMCAwMC0xMS4zIDBsLTM2MiAzNjEuMy0yMzcuNi0yMzdhOC4wMyA4LjAzIDAgMDAtMTEuMyAwbC0zOS42IDM5LjVhOC4wMyA4LjAzIDAgMDAwIDExLjNsMjQzLjIgMjQyLjggMzkuNiAzOS41YzMuMSAzLjEgOC4yIDMuMSAxMS4zIDBsNDA3LjMtNDA2LjZjMy4xLTMuMSAzLjEtOC4yIDAtMTEuM3oiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TagsOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TagsOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "TagsOutlinedSvg", "AntdIcon", "TagsOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/TagsOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TagsOutlinedSvg from \"@ant-design/icons-svg/es/asn/TagsOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TagsOutlined = function TagsOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TagsOutlinedSvg\n  }));\n};\n\n/**![tags](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4My4yIDc5MC4zTDg2MS40IDQxMmMxLjctMS43IDIuNS00IDIuMy02LjNsLTI1LjUtMzAxLjRjLS43LTcuOC02LjgtMTMuOS0xNC42LTE0LjZMNTIyLjIgNjQuM2MtMi4zLS4yLTQuNy42LTYuMyAyLjNMMTM3LjcgNDQ0LjhhOC4wMyA4LjAzIDAgMDAwIDExLjNsMzM0LjIgMzM0LjJjMy4xIDMuMiA4LjIgMy4yIDExLjMgMHptNjIuNi02NTEuN2wyMjQuNiAxOSAxOSAyMjQuNkw0NzcuNSA2OTQgMjMzLjkgNDUwLjVsMzExLjktMzExLjl6bTYwLjE2IDE4Ni4yM2E0OCA0OCAwIDEwNjcuODgtNjcuODkgNDggNDggMCAxMC02Ny44OCA2Ny44OXpNODg5LjcgNTM5LjhsLTM5LjYtMzkuNWE4LjAzIDguMDMgMCAwMC0xMS4zIDBsLTM2MiAzNjEuMy0yMzcuNi0yMzdhOC4wMyA4LjAzIDAgMDAtMTEuMyAwbC0zOS42IDM5LjVhOC4wMyA4LjAzIDAgMDAwIDExLjNsMjQzLjIgMjQyLjggMzkuNiAzOS41YzMuMSAzLjEgOC4yIDMuMSAxMS4zIDBsNDA3LjMtNDA2LjZjMy4xLTMuMSAzLjEtOC4yIDAtMTEuM3oiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TagsOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TagsOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,YAAY,CAAC;AACzD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,cAAc;AACtC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}