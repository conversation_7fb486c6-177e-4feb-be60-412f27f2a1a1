{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport AlertFilledSvg from \"@ant-design/icons-svg/es/asn/AlertFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar AlertFilled = function AlertFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: AlertFilledSvg\n  }));\n};\n\n/**![alert](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAyNDRjMTc2LjE4IDAgMzE5IDE0Mi44MiAzMTkgMzE5djIzM2EzMiAzMiAwIDAxLTMyIDMySDIyNWEzMiAzMiAwIDAxLTMyLTMyVjU2M2MwLTE3Ni4xOCAxNDIuODItMzE5IDMxOS0zMTl6TTQ4NCA2OGg1NmE4IDggMCAwMTggOHY5NmE4IDggMCAwMS04IDhoLTU2YTggOCAwIDAxLTgtOFY3NmE4IDggMCAwMTgtOHpNMTc3LjI1IDE5MS42NmE4IDggMCAwMTExLjMyIDBsNjcuODggNjcuODhhOCA4IDAgMDEwIDExLjMxbC0zOS42IDM5LjZhOCA4IDAgMDEtMTEuMzEgMGwtNjcuODgtNjcuODhhOCA4IDAgMDEwLTExLjMxbDM5LjYtMzkuNnptNjY5LjYgMGwzOS42IDM5LjZhOCA4IDAgMDEwIDExLjNsLTY3Ljg4IDY3LjlhOCA4IDAgMDEtMTEuMzIgMGwtMzkuNi0zOS42YTggOCAwIDAxMC0xMS4zMmw2Ny44OS02Ny44OGE4IDggMCAwMTExLjMxIDB6TTE5MiA4OTJoNjQwYTMyIDMyIDAgMDEzMiAzMnYyNGE4IDggMCAwMS04IDhIMTY4YTggOCAwIDAxLTgtOHYtMjRhMzIgMzIgMCAwMTMyLTMyem0xNDgtMzE3djI1M2g2NFY1NzVoLTY0eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(AlertFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'AlertFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "AlertFilledSvg", "AntdIcon", "<PERSON><PERSON><PERSON><PERSON>d", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/AlertFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport AlertFilledSvg from \"@ant-design/icons-svg/es/asn/AlertFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar AlertFilled = function AlertFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: AlertFilledSvg\n  }));\n};\n\n/**![alert](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAyNDRjMTc2LjE4IDAgMzE5IDE0Mi44MiAzMTkgMzE5djIzM2EzMiAzMiAwIDAxLTMyIDMySDIyNWEzMiAzMiAwIDAxLTMyLTMyVjU2M2MwLTE3Ni4xOCAxNDIuODItMzE5IDMxOS0zMTl6TTQ4NCA2OGg1NmE4IDggMCAwMTggOHY5NmE4IDggMCAwMS04IDhoLTU2YTggOCAwIDAxLTgtOFY3NmE4IDggMCAwMTgtOHpNMTc3LjI1IDE5MS42NmE4IDggMCAwMTExLjMyIDBsNjcuODggNjcuODhhOCA4IDAgMDEwIDExLjMxbC0zOS42IDM5LjZhOCA4IDAgMDEtMTEuMzEgMGwtNjcuODgtNjcuODhhOCA4IDAgMDEwLTExLjMxbDM5LjYtMzkuNnptNjY5LjYgMGwzOS42IDM5LjZhOCA4IDAgMDEwIDExLjNsLTY3Ljg4IDY3LjlhOCA4IDAgMDEtMTEuMzIgMGwtMzkuNi0zOS42YTggOCAwIDAxMC0xMS4zMmw2Ny44OS02Ny44OGE4IDggMCAwMTExLjMxIDB6TTE5MiA4OTJoNjQwYTMyIDMyIDAgMDEzMiAzMnYyNGE4IDggMCAwMS04IDhIMTY4YTggOCAwIDAxLTgtOHYtMjRhMzIgMzIgMCAwMTMyLTMyem0xNDgtMzE3djI1M2g2NFY1NzVoLTY0eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(AlertFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'AlertFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}