{"ast": null, "code": "import { isInOffscreenGroup } from '../../../util';\nimport ellipsis from './autoEllipsis';\nimport hide from './autoHide';\nimport rotate from './autoRotate';\nimport wrap from './autoWrap';\nexport var OverlapUtils = new Map([['hide', hide], ['rotate', rotate], ['ellipsis', ellipsis], ['wrap', wrap]]);\nexport function canProcessOverlap(labels, attr, type) {\n  if (attr.labelOverlap.length < 1) return false;\n  if (type === 'hide') return !isInOffscreenGroup(labels[0]);\n  if (type === 'rotate') return !labels.some(function (label) {\n    var _a;\n    return !!((_a = label.attr('transform')) === null || _a === void 0 ? void 0 : _a.includes('rotate'));\n  });\n  if (type === 'ellipsis' || type === 'wrap') return labels.filter(function (item) {\n    return item.querySelector('text');\n  }).length > 1;\n  return true;\n}\nexport function processOverlap(labels, attr, utils) {\n  var _a = attr.labelOverlap,\n    labelOverlap = _a === void 0 ? [] : _a;\n  if (!labelOverlap.length) return;\n  labelOverlap.forEach(function (overlapCfg) {\n    var type = overlapCfg.type;\n    var util = OverlapUtils.get(type);\n    if (canProcessOverlap(labels, attr, type)) util === null || util === void 0 ? void 0 : util(labels, overlapCfg, attr, utils);\n  });\n}", "map": {"version": 3, "names": ["isInOffscreenGroup", "ellipsis", "hide", "rotate", "wrap", "OverlapUtils", "Map", "canProcessOverlap", "labels", "attr", "type", "labelOverlap", "length", "some", "label", "_a", "includes", "filter", "item", "querySelector", "processOverlap", "utils", "for<PERSON>ach", "overlapCfg", "util", "get"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\component\\src\\ui\\axis\\overlap\\index.ts"], "sourcesContent": ["import type { DisplayObject } from '../../../shapes';\nimport { Text } from '../../../shapes';\nimport { isInOffscreenGroup } from '../../../util';\nimport type { AxisStyleProps, LabelOverlapCfg } from '../types';\nimport type { Utils as EllipsisUtils } from './autoEllipsis';\nimport ellipsis from './autoEllipsis';\nimport type { Utils as HideUtils } from './autoHide';\nimport hide from './autoHide';\nimport type { Utils as RotateUtils } from './autoRotate';\nimport rotate from './autoRotate';\nimport type { Utils as WrapUtils } from './autoWrap';\nimport wrap from './autoWrap';\n\nexport type OverlapCallback = (labels: Text[], overlapCfg: any, cfg: AxisStyleProps, utils: any) => any;\n\nexport type OverlapUtilsType = EllipsisUtils & HideUtils & RotateUtils & WrapUtils;\n\nexport const OverlapUtils = new Map<string, any>([\n  ['hide', hide],\n  ['rotate', rotate],\n  ['ellipsis', ellipsis],\n  ['wrap', wrap],\n]);\n\nexport function canProcessOverlap(\n  labels: DisplayObject[],\n  attr: Required<AxisStyleProps>,\n  type: LabelOverlapCfg['type']\n) {\n  if (attr.labelOverlap.length < 1) return false;\n  if (type === 'hide') return !isInOffscreenGroup(labels[0]);\n  if (type === 'rotate') return !labels.some((label) => !!label.attr('transform')?.includes('rotate'));\n  if (type === 'ellipsis' || type === 'wrap') return labels.filter((item) => item.querySelector('text')).length > 1;\n  return true;\n}\n\nexport function processOverlap(labels: DisplayObject[], attr: Required<AxisStyleProps>, utils: OverlapUtilsType) {\n  const { labelOverlap = [] } = attr;\n  if (!labelOverlap.length) return;\n  labelOverlap.forEach((overlapCfg) => {\n    const { type } = overlapCfg;\n    const util = OverlapUtils.get(type);\n    if (canProcessOverlap(labels, attr, type)) util?.(labels as any[], overlapCfg, attr, utils);\n  });\n}\n"], "mappings": "AAEA,SAASA,kBAAkB,QAAQ,eAAe;AAGlD,OAAOC,QAAQ,MAAM,gBAAgB;AAErC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAOC,MAAM,MAAM,cAAc;AAEjC,OAAOC,IAAI,MAAM,YAAY;AAM7B,OAAO,IAAMC,YAAY,GAAG,IAAIC,GAAG,CAAc,CAC/C,CAAC,MAAM,EAAEJ,IAAI,CAAC,EACd,CAAC,QAAQ,EAAEC,MAAM,CAAC,EAClB,CAAC,UAAU,EAAEF,QAAQ,CAAC,EACtB,CAAC,MAAM,EAAEG,IAAI,CAAC,CACf,CAAC;AAEF,OAAM,SAAUG,iBAAiBA,CAC/BC,MAAuB,EACvBC,IAA8B,EAC9BC,IAA6B;EAE7B,IAAID,IAAI,CAACE,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK;EAC9C,IAAIF,IAAI,KAAK,MAAM,EAAE,OAAO,CAACV,kBAAkB,CAACQ,MAAM,CAAC,CAAC,CAAC,CAAC;EAC1D,IAAIE,IAAI,KAAK,QAAQ,EAAE,OAAO,CAACF,MAAM,CAACK,IAAI,CAAC,UAACC,KAAK;IAAA,IAAAC,EAAA;IAAK,QAAC,EAAC,CAAAA,EAAA,GAAAD,KAAK,CAACL,IAAI,CAAC,WAAW,CAAC,cAAAM,EAAA,uBAAAA,EAAA,CAAEC,QAAQ,CAAC,QAAQ,CAAC;EAAA,EAAC;EACpG,IAAIN,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM,EAAE,OAAOF,MAAM,CAACS,MAAM,CAAC,UAACC,IAAI;IAAK,OAAAA,IAAI,CAACC,aAAa,CAAC,MAAM,CAAC;EAA1B,CAA0B,CAAC,CAACP,MAAM,GAAG,CAAC;EACjH,OAAO,IAAI;AACb;AAEA,OAAM,SAAUQ,cAAcA,CAACZ,MAAuB,EAAEC,IAA8B,EAAEY,KAAuB;EACrG,IAAAN,EAAA,GAAsBN,IAAI,CAAAE,YAAT;IAAjBA,YAAY,GAAAI,EAAA,cAAG,EAAE,GAAAA,EAAA;EACzB,IAAI,CAACJ,YAAY,CAACC,MAAM,EAAE;EAC1BD,YAAY,CAACW,OAAO,CAAC,UAACC,UAAU;IACtB,IAAAb,IAAI,GAAKa,UAAU,CAAAb,IAAf;IACZ,IAAMc,IAAI,GAAGnB,YAAY,CAACoB,GAAG,CAACf,IAAI,CAAC;IACnC,IAAIH,iBAAiB,CAACC,MAAM,EAAEC,IAAI,EAAEC,IAAI,CAAC,EAAEc,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAGhB,MAAe,EAAEe,UAAU,EAAEd,IAAI,EAAEY,KAAK,CAAC;EAC7F,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}