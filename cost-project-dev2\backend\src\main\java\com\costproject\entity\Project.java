package com.costproject.entity;

import com.costproject.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Table(name = "projects")
@EqualsAndHashCode(callSuper = true)
public class Project extends BaseEntity {

    @Column(nullable = false)
    private String name; // 项目名称

    @Column(columnDefinition = "TEXT")
    private String description; // 项目描述

    @Column(nullable = false)
    private LocalDate startDate; // 开工日期

    @Column
    private LocalDate endDate; // 完工日期

    @Column
    private String location; // 项目地点

    @Column
    private Integer dailyWorkHours = 8; // 每日工作小时数

    @Column
    private String timeZone = "Asia/Shanghai"; // 时区

    @Column
    private String status; // 项目状态

    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL)
    private List<Task> tasks = new ArrayList<>();

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getDailyWorkHours() {
        return dailyWorkHours;
    }

    public void setDailyWorkHours(Integer dailyWorkHours) {
        this.dailyWorkHours = dailyWorkHours;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<Task> getTasks() {
        return tasks;
    }

    public void setTasks(List<Task> tasks) {
        this.tasks = tasks;
    }
} 