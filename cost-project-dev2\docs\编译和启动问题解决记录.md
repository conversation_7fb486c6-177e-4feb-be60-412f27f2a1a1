# 编译和启动问题解决记录

## 问题1：Maven settings.xml 文件位置错误

**错误信息**：
```
[ERROR] The specified user settings file does not exist: C:\Users\<USER>\Desktop\dev\cost-project-dev2\settings.xml
```

**原因**：settings.xml 文件在 backend 目录中，而不是项目根目录。

**解决方案**：
```bash
cd backend
mvn spring-boot:run -s settings.xml
```

## 问题2：JSONB 类型不兼容

**错误信息**：
```
Unable to load class [jsonb]
```

**原因**：Resource 实体中使用了 `@Type(type = "jsonb")`，这是 PostgreSQL 特有的类型，H2 数据库不支持。

**解决方案**：
修改 `Resource.java`，将 jsonb 类型改为 TEXT：
```java
// 原代码
@Type(type = "jsonb")
@Column(columnDefinition = "jsonb")
private String costBreakdown;

// 修改后
@Column(columnDefinition = "TEXT")
private String costBreakdown;
```

## 问题3：Spring Profile 未生效

**错误信息**：
```
org.postgresql.util.PSQLException: 用户 "postgres" Password 认证失败
```

**原因**：应用没有使用开发配置文件（dev profile），仍在尝试连接 PostgreSQL。

**解决方案**：
使用环境变量设置 profile：
```powershell
$env:SPRING_PROFILES_ACTIVE="dev"
mvn spring-boot:run -s settings.xml
```

## 问题4：Repository 方法引用不存在的字段

**错误信息**：
```
No property 'status' found for type 'Supplier'!
```

**原因**：`SupplierRepository` 中定义了 `findByStatus` 方法，但 `Supplier` 实体没有 `status` 字段。

**解决方案**：
从 `SupplierRepository.java` 中移除该方法：
```java
// 删除这行
List<Supplier> findByStatus(String status);
```

## 问题5：前端缺少必要文件

**错误信息**：
```
Could not find a required file.
  Name: index.html
  Searched in: C:\Users\<USER>\Desktop\dev\cost-project-dev2\frontend\public
```

**原因**：前端项目缺少基础文件。

**解决方案**：
创建必要的前端文件：
- `frontend/public/index.html`
- `frontend/src/index.js`
- `frontend/src/App.js`
- `frontend/src/index.css`
- `frontend/src/App.css`

## 当前状态

### 后端
- ✅ 编译成功
- ✅ 使用 H2 内存数据库（开发模式）
- ✅ 数据库表自动创建
- ⏳ Spring Boot 应用正在运行中

### 前端
- ✅ 创建了基础文件结构
- ✅ 配置了 React + Ant Design
- ⏳ 需要运行 `npm install` 安装依赖
- ⏳ 需要运行 `npm start` 启动开发服务器

## 下一步操作

1. **验证后端服务**：
   ```bash
   # 检查端口
   netstat -an | findstr :8080
   
   # 访问 Swagger 文档
   http://localhost:8080/api/swagger-ui.html
   
   # 访问 H2 控制台
   http://localhost:8080/api/h2-console
   ```

2. **启动前端服务**：
   ```bash
   cd frontend
   npm install
   npm start
   ```

3. **访问应用**：
   - 前端：http://localhost:3000
   - 后端 API：http://localhost:8080/api
   - Swagger 文档：http://localhost:8080/api/swagger-ui.html 

### 15. Spring Profile激活问题

**问题描述**：
使用`mvn spring-boot:run -Dspring.profiles.active=dev`命令时，dev profile没有被正确激活，应用仍然使用默认配置尝试连接PostgreSQL数据库。

**错误信息**：
```
No active profile set, falling back to 1 default profile: "default"
org.postgresql.util.PSQLException: 连接错误: 用户 "postgres" Password 认证失败
```

**解决方案**：
1. 在PowerShell中需要使用引号包裹参数：
   ```powershell
   mvn spring-boot:run -s settings.xml "-Dspring.profiles.active=dev"
   ```

2. 或者使用环境变量方式：
   ```powershell
   $env:SPRING_PROFILES_ACTIVE="dev"
   mvn spring-boot:run -s settings.xml
   ```

3. 创建批处理文件`start-backend.bat`：
   ```batch
   @echo off
   echo Starting Cost-Project Backend with H2 Database...
   echo.
   mvn spring-boot:run -s settings.xml -Dspring.profiles.active=dev
   pause
   ```

### 16. Swagger与Spring Boot 2.7兼容性问题

**问题描述**：
Spring Boot 2.7与Springfox Swagger存在兼容性问题，导致启动失败。

**错误信息**：
```
java.lang.NullPointerException: Cannot invoke "org.springframework.web.servlet.mvc.condition.PatternsRequestCondition.getPatterns()" because "this.condition" is null
```

**解决方案**：
在`application-dev.properties`中添加：
```properties
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
```

## 当前状态

截至2025年5月28日，项目后端可以成功编译，但在启动时仍存在以下问题：

1. **Profile激活问题**：在某些情况下，dev profile可能无法正确激活
2. **建议的启动方式**：
   - Windows CMD：`mvn spring-boot:run -s settings.xml -Dspring.profiles.active=dev`
   - PowerShell：`mvn spring-boot:run -s settings.xml "-Dspring.profiles.active=dev"`
   - 使用批处理文件：`start-backend.bat`

3. **验证Profile是否激活**：
   - 查看启动日志中是否包含：`The following profiles are active: dev`
   - 确认使用的是H2数据库而不是PostgreSQL

## 后续建议

1. 考虑升级到Spring Boot 3.x以获得更好的兼容性
2. 考虑使用SpringDoc OpenAPI替代Springfox Swagger
3. 为不同环境创建更多的配置文件（如test、prod等） 