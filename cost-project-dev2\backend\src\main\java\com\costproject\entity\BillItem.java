package com.costproject.entity;

import com.costproject.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Table(name = "bill_items")
@EqualsAndHashCode(callSuper = true)
public class BillItem extends BaseEntity {

    @Column(length = 50)
    private String chapter; // 章节

    @Column(length = 12, nullable = false, unique = true)
    private String code; // 清单编码（12位）

    @Column(nullable = false)
    private String name; // 清单名称

    @Column(columnDefinition = "TEXT")
    private String description; // 工作内容描述

    @Column(length = 20)
    private String unit; // 计量单位

    @Column(precision = 18, scale = 4)
    private BigDecimal quantity; // 工程数量

    @Column(precision = 18, scale = 4)
    private BigDecimal unitPrice; // 综合单价

    @Column(precision = 18, scale = 4)
    private BigDecimal totalPrice; // 合价

    // 与定额的关联关系
    @ManyToMany
    @JoinTable(
        name = "bill_quota_refs",
        joinColumns = @JoinColumn(name = "bill_item_id"),
        inverseJoinColumns = @JoinColumn(name = "quota_item_id")
    )
    private List<QuotaItem> quotaItems = new ArrayList<>();

    // 与任务的关联关系
    @ManyToMany(mappedBy = "billItems")
    private List<Task> tasks = new ArrayList<>();

    // Getters and Setters
    public String getChapter() {
        return chapter;
    }

    public void setChapter(String chapter) {
        this.chapter = chapter;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
        calculateTotalPrice();
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
        calculateTotalPrice();
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public List<QuotaItem> getQuotaItems() {
        return quotaItems;
    }

    public void setQuotaItems(List<QuotaItem> quotaItems) {
        this.quotaItems = quotaItems;
    }

    public List<Task> getTasks() {
        return tasks;
    }

    public void setTasks(List<Task> tasks) {
        this.tasks = tasks;
    }

    // 业务方法
    
    /**
     * 计算合价
     */
    private void calculateTotalPrice() {
        if (quantity != null && unitPrice != null) {
            this.totalPrice = quantity.multiply(unitPrice);
        }
    }

    /**
     * 添加定额项
     */
    public void addQuotaItem(QuotaItem quotaItem) {
        if (!quotaItems.contains(quotaItem)) {
            quotaItems.add(quotaItem);
            quotaItem.getBillItems().add(this);
        }
    }

    /**
     * 移除定额项
     */
    public void removeQuotaItem(QuotaItem quotaItem) {
        quotaItems.remove(quotaItem);
        quotaItem.getBillItems().remove(this);
    }

    @PrePersist
    @PreUpdate
    private void prePersist() {
        calculateTotalPrice();
    }
}