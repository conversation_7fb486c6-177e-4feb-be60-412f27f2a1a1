{"ast": null, "code": "import * as React from 'react';\n// recursion (flat tree structure)\nfunction fillRecords(list, record, indent, childrenColumnName, expandedKeys, getRowKey, index) {\n  var key = getRowKey(record, index);\n  list.push({\n    record: record,\n    indent: indent,\n    index: index,\n    rowKey: key\n  });\n  var expanded = expandedKeys === null || expandedKeys === void 0 ? void 0 : expandedKeys.has(key);\n  if (record && Array.isArray(record[childrenColumnName]) && expanded) {\n    // expanded state, flat record\n    for (var i = 0; i < record[childrenColumnName].length; i += 1) {\n      fillRecords(list, record[childrenColumnName][i], indent + 1, childrenColumnName, expandedKeys, getRowKey, i);\n    }\n  }\n}\n/**\n * flat tree data on expanded state\n *\n * @export\n * @template T\n * @param {*} data : table data\n * @param {string} childrenColumnName : 指定树形结构的列名\n * @param {Set<Key>} expandedKeys : 展开的行对应的keys\n * @param {GetRowKey<T>} getRowKey  : 获取当前rowKey的方法\n * @returns flattened data\n */\nexport default function useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey) {\n  var arr = React.useMemo(function () {\n    if (expandedKeys !== null && expandedKeys !== void 0 && expandedKeys.size) {\n      var list = [];\n\n      // collect flattened record\n      for (var i = 0; i < (data === null || data === void 0 ? void 0 : data.length); i += 1) {\n        var record = data[i];\n\n        // using array.push or spread operator may cause \"Maximum call stack size exceeded\" exception if array size is big enough.\n        fillRecords(list, record, 0, childrenColumnName, expandedKeys, getRowKey, i);\n      }\n      return list;\n    }\n    return data === null || data === void 0 ? void 0 : data.map(function (item, index) {\n      return {\n        record: item,\n        indent: 0,\n        index: index,\n        rowKey: getRowKey(item, index)\n      };\n    });\n  }, [data, childrenColumnName, expandedKeys, getRowKey]);\n  return arr;\n}", "map": {"version": 3, "names": ["React", "fillRecords", "list", "record", "indent", "childrenColumnName", "expandedKeys", "getRowKey", "index", "key", "push", "<PERSON><PERSON><PERSON>", "expanded", "has", "Array", "isArray", "i", "length", "useFlattenRecords", "data", "arr", "useMemo", "size", "map", "item"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/rc-table/es/hooks/useFlattenRecords.js"], "sourcesContent": ["import * as React from 'react';\n// recursion (flat tree structure)\nfunction fillRecords(list, record, indent, childrenColumnName, expandedKeys, getRowKey, index) {\n  var key = getRowKey(record, index);\n  list.push({\n    record: record,\n    indent: indent,\n    index: index,\n    rowKey: key\n  });\n  var expanded = expandedKeys === null || expandedKeys === void 0 ? void 0 : expandedKeys.has(key);\n  if (record && Array.isArray(record[childrenColumnName]) && expanded) {\n    // expanded state, flat record\n    for (var i = 0; i < record[childrenColumnName].length; i += 1) {\n      fillRecords(list, record[childrenColumnName][i], indent + 1, childrenColumnName, expandedKeys, getRowKey, i);\n    }\n  }\n}\n/**\n * flat tree data on expanded state\n *\n * @export\n * @template T\n * @param {*} data : table data\n * @param {string} childrenColumnName : 指定树形结构的列名\n * @param {Set<Key>} expandedKeys : 展开的行对应的keys\n * @param {GetRowKey<T>} getRowKey  : 获取当前rowKey的方法\n * @returns flattened data\n */\nexport default function useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey) {\n  var arr = React.useMemo(function () {\n    if (expandedKeys !== null && expandedKeys !== void 0 && expandedKeys.size) {\n      var list = [];\n\n      // collect flattened record\n      for (var i = 0; i < (data === null || data === void 0 ? void 0 : data.length); i += 1) {\n        var record = data[i];\n\n        // using array.push or spread operator may cause \"Maximum call stack size exceeded\" exception if array size is big enough.\n        fillRecords(list, record, 0, childrenColumnName, expandedKeys, getRowKey, i);\n      }\n      return list;\n    }\n    return data === null || data === void 0 ? void 0 : data.map(function (item, index) {\n      return {\n        record: item,\n        indent: 0,\n        index: index,\n        rowKey: getRowKey(item, index)\n      };\n    });\n  }, [data, childrenColumnName, expandedKeys, getRowKey]);\n  return arr;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA,SAASC,WAAWA,CAACC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAC7F,IAAIC,GAAG,GAAGF,SAAS,CAACJ,MAAM,EAAEK,KAAK,CAAC;EAClCN,IAAI,CAACQ,IAAI,CAAC;IACRP,MAAM,EAAEA,MAAM;IACdC,MAAM,EAAEA,MAAM;IACdI,KAAK,EAAEA,KAAK;IACZG,MAAM,EAAEF;EACV,CAAC,CAAC;EACF,IAAIG,QAAQ,GAAGN,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACO,GAAG,CAACJ,GAAG,CAAC;EAChG,IAAIN,MAAM,IAAIW,KAAK,CAACC,OAAO,CAACZ,MAAM,CAACE,kBAAkB,CAAC,CAAC,IAAIO,QAAQ,EAAE;IACnE;IACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,MAAM,CAACE,kBAAkB,CAAC,CAACY,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC7Df,WAAW,CAACC,IAAI,EAAEC,MAAM,CAACE,kBAAkB,CAAC,CAACW,CAAC,CAAC,EAAEZ,MAAM,GAAG,CAAC,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAES,CAAC,CAAC;IAC9G;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASE,iBAAiBA,CAACC,IAAI,EAAEd,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAE;EAC3F,IAAIa,GAAG,GAAGpB,KAAK,CAACqB,OAAO,CAAC,YAAY;IAClC,IAAIf,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACgB,IAAI,EAAE;MACzE,IAAIpB,IAAI,GAAG,EAAE;;MAEb;MACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIG,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACF,MAAM,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE;QACrF,IAAIb,MAAM,GAAGgB,IAAI,CAACH,CAAC,CAAC;;QAEpB;QACAf,WAAW,CAACC,IAAI,EAAEC,MAAM,EAAE,CAAC,EAAEE,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAES,CAAC,CAAC;MAC9E;MACA,OAAOd,IAAI;IACb;IACA,OAAOiB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACI,GAAG,CAAC,UAAUC,IAAI,EAAEhB,KAAK,EAAE;MACjF,OAAO;QACLL,MAAM,EAAEqB,IAAI;QACZpB,MAAM,EAAE,CAAC;QACTI,KAAK,EAAEA,KAAK;QACZG,MAAM,EAAEJ,SAAS,CAACiB,IAAI,EAAEhB,KAAK;MAC/B,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACW,IAAI,EAAEd,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,CAAC,CAAC;EACvD,OAAOa,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}