{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport GitlabOutlinedSvg from \"@ant-design/icons-svg/es/asn/GitlabOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar GitlabOutlined = function GitlabOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: GitlabOutlinedSvg\n  }));\n};\n\n/**![gitlab](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMy45IDU1Mi4yTDgwNSAxODEuNHYtLjFjLTcuNi0yMi45LTI1LjctMzYuNS00OC4zLTM2LjUtMjMuNCAwLTQyLjUgMTMuNS00OS43IDM1LjJsLTcxLjQgMjEzSDM4OC44bC03MS40LTIxM2MtNy4yLTIxLjctMjYuMy0zNS4yLTQ5LjctMzUuMi0yMy4xIDAtNDIuNSAxNC44LTQ4LjQgMzYuNkwxMTAuNSA1NTIuMmMtNC40IDE0LjcgMS4yIDMxLjQgMTMuNSA0MC43bDM2OC41IDI3Ni40YzIuNiAzLjYgNi4yIDYuMyAxMC40IDcuOGw4LjYgNi40IDguNS02LjRjNC45LTEuNyA5LTQuNyAxMS45LTguOWwzNjguNC0yNzUuNGMxMi40LTkuMiAxOC0yNS45IDEzLjYtNDAuNnpNNzUxLjcgMTkzLjRjMS0xLjggMi45LTEuOSAzLjUtMS45IDEuMSAwIDIuNS4zIDMuNCAzTDgxOCAzOTQuM0g2ODQuNWw2Ny4yLTIwMC45em0tNDg3LjQgMWMuOS0yLjYgMi4zLTIuOSAzLjQtMi45IDIuNyAwIDIuOS4xIDMuNCAxLjdsNjcuMyAyMDEuMkgyMDYuNWw1Ny44LTIwMHpNMTU4LjggNTU4LjdsMjguMi05Ny4zIDIwMi40IDI3MC4yLTIzMC42LTE3Mi45em03My45LTExNi40aDEyMi4xbDkwLjggMjg0LjMtMjEyLjktMjg0LjN6TTUxMi45IDc3Nkw0MDUuNyA0NDIuM0g2MjBMNTEyLjkgNzc2em0xNTcuOS0zMzMuN2gxMTkuNUw1ODAgNzIzLjFsOTAuOC0yODAuOHptLTQwLjcgMjkzLjlsMjA3LjMtMjc2LjcgMjkuNSA5OS4yLTIzNi44IDE3Ny41eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(GitlabOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'GitlabOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "GitlabOutlinedSvg", "AntdIcon", "GitlabOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/GitlabOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport GitlabOutlinedSvg from \"@ant-design/icons-svg/es/asn/GitlabOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar GitlabOutlined = function GitlabOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: GitlabOutlinedSvg\n  }));\n};\n\n/**![gitlab](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMy45IDU1Mi4yTDgwNSAxODEuNHYtLjFjLTcuNi0yMi45LTI1LjctMzYuNS00OC4zLTM2LjUtMjMuNCAwLTQyLjUgMTMuNS00OS43IDM1LjJsLTcxLjQgMjEzSDM4OC44bC03MS40LTIxM2MtNy4yLTIxLjctMjYuMy0zNS4yLTQ5LjctMzUuMi0yMy4xIDAtNDIuNSAxNC44LTQ4LjQgMzYuNkwxMTAuNSA1NTIuMmMtNC40IDE0LjcgMS4yIDMxLjQgMTMuNSA0MC43bDM2OC41IDI3Ni40YzIuNiAzLjYgNi4yIDYuMyAxMC40IDcuOGw4LjYgNi40IDguNS02LjRjNC45LTEuNyA5LTQuNyAxMS45LTguOWwzNjguNC0yNzUuNGMxMi40LTkuMiAxOC0yNS45IDEzLjYtNDAuNnpNNzUxLjcgMTkzLjRjMS0xLjggMi45LTEuOSAzLjUtMS45IDEuMSAwIDIuNS4zIDMuNCAzTDgxOCAzOTQuM0g2ODQuNWw2Ny4yLTIwMC45em0tNDg3LjQgMWMuOS0yLjYgMi4zLTIuOSAzLjQtMi45IDIuNyAwIDIuOS4xIDMuNCAxLjdsNjcuMyAyMDEuMkgyMDYuNWw1Ny44LTIwMHpNMTU4LjggNTU4LjdsMjguMi05Ny4zIDIwMi40IDI3MC4yLTIzMC42LTE3Mi45em03My45LTExNi40aDEyMi4xbDkwLjggMjg0LjMtMjEyLjktMjg0LjN6TTUxMi45IDc3Nkw0MDUuNyA0NDIuM0g2MjBMNTEyLjkgNzc2em0xNTcuOS0zMzMuN2gxMTkuNUw1ODAgNzIzLjFsOTAuOC0yODAuOHptLTQwLjcgMjkzLjlsMjA3LjMtMjc2LjcgMjkuNSA5OS4yLTIzNi44IDE3Ny41eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(GitlabOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'GitlabOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,cAAc,CAAC;AAC3D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,gBAAgB;AACxC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}