{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CustomerServiceTwoToneSvg from \"@ant-design/icons-svg/es/asn/CustomerServiceTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CustomerServiceTwoTone = function CustomerServiceTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CustomerServiceTwoToneSvg\n  }));\n};\n\n/**![customer-service](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5NiA2MzJoMTI4djE5Mkg2OTZ6bS00OTYgMGgxMjh2MTkySDIwMHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTUxMiAxMjhjLTIxMi4xIDAtMzg0IDE3MS45LTM4NCAzODR2MzYwYzAgMTMuMyAxMC43IDI0IDI0IDI0aDE4NGMzNS4zIDAgNjQtMjguNyA2NC02NFY2MjRjMC0zNS4zLTI4LjctNjQtNjQtNjRIMjAwdi00OGMwLTE3Mi4zIDEzOS43LTMxMiAzMTItMzEyczMxMiAxMzkuNyAzMTIgMzEydjQ4SDY4OGMtMzUuMyAwLTY0IDI4LjctNjQgNjR2MjA4YzAgMzUuMyAyOC43IDY0IDY0IDY0aDE4NGMxMy4zIDAgMjQtMTAuNyAyNC0yNFY1MTJjMC0yMTIuMS0xNzEuOS0zODQtMzg0LTM4NHpNMzI4IDYzMnYxOTJIMjAwVjYzMmgxMjh6bTQ5NiAxOTJINjk2VjYzMmgxMjh2MTkyeiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CustomerServiceTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CustomerServiceTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "CustomerServiceTwoToneSvg", "AntdIcon", "CustomerServiceTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/CustomerServiceTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CustomerServiceTwoToneSvg from \"@ant-design/icons-svg/es/asn/CustomerServiceTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CustomerServiceTwoTone = function CustomerServiceTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CustomerServiceTwoToneSvg\n  }));\n};\n\n/**![customer-service](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5NiA2MzJoMTI4djE5Mkg2OTZ6bS00OTYgMGgxMjh2MTkySDIwMHoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTUxMiAxMjhjLTIxMi4xIDAtMzg0IDE3MS45LTM4NCAzODR2MzYwYzAgMTMuMyAxMC43IDI0IDI0IDI0aDE4NGMzNS4zIDAgNjQtMjguNyA2NC02NFY2MjRjMC0zNS4zLTI4LjctNjQtNjQtNjRIMjAwdi00OGMwLTE3Mi4zIDEzOS43LTMxMiAzMTItMzEyczMxMiAxMzkuNyAzMTIgMzEydjQ4SDY4OGMtMzUuMyAwLTY0IDI4LjctNjQgNjR2MjA4YzAgMzUuMyAyOC43IDY0IDY0IDY0aDE4NGMxMy4zIDAgMjQtMTAuNyAyNC0yNFY1MTJjMC0yMTIuMS0xNzEuOS0zODQtMzg0LTM4NHpNMzI4IDYzMnYxOTJIMjAwVjYzMmgxMjh6bTQ5NiAxOTJINjk2VjYzMmgxMjh2MTkyeiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CustomerServiceTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CustomerServiceTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,yBAAyB,MAAM,qDAAqD;AAC3F,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,sBAAsB,CAAC;AACnE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,wBAAwB;AAChD;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}