package com.costproject.service;

import com.costproject.entity.Resource;
import com.costproject.entity.ResourcePrice;
import com.costproject.entity.Supplier;
import com.costproject.repository.ResourceRepository;
import com.costproject.repository.SupplierRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ResourceService {

    private static final Logger log = LoggerFactory.getLogger(ResourceService.class);

    private final ResourceRepository resourceRepository;
    private final SupplierRepository supplierRepository;
    
    public ResourceService(ResourceRepository resourceRepository,
                          SupplierRepository supplierRepository) {
        this.resourceRepository = resourceRepository;
        this.supplierRepository = supplierRepository;
    }

    /**
     * 创建资源
     */
    @Transactional
    public Resource createResource(Resource resource) {
        validateResource(resource);
        return resourceRepository.save(resource);
    }

    /**
     * 更新资源
     */
    @Transactional
    public Resource updateResource(Long id, Resource resource) {
        Resource existing = getResource(id);
        updateResourceFields(existing, resource);
        return resourceRepository.save(existing);
    }

    /**
     * 删除资源
     */
    @Transactional
    public void deleteResource(Long id) {
        Resource resource = getResource(id);
        resourceRepository.delete(resource);
    }

    /**
     * 获取资源
     */
    public Resource getResource(Long id) {
        return resourceRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("资源不存在: " + id));
    }

    /**
     * 查询资源列表
     */
    public List<Resource> searchResources(String keyword, Resource.ResourceType type) {
        if (keyword != null && type != null) {
            return resourceRepository.findByNameContainingAndType(keyword, type);
        } else if (keyword != null) {
            return resourceRepository.findByNameContaining(keyword);
        } else if (type != null) {
            return resourceRepository.findByType(type);
        }
        return resourceRepository.findAll();
    }

    /**
     * 更新资源价格
     */
    @Transactional
    public void updatePrice(Long resourceId, BigDecimal price, LocalDateTime effectiveDate) {
        Resource resource = getResource(resourceId);
        resource.addPriceRecord(price, effectiveDate);
        resourceRepository.save(resource);
    }

    /**
     * 获取价格历史
     */
    public List<ResourcePrice> getPriceHistory(Long resourceId) {
        Resource resource = getResource(resourceId);
        return resource.getPriceHistory().stream()
            .sorted(Comparator.comparing(ResourcePrice::getEffectiveDate).reversed())
            .collect(Collectors.toList());
    }

    /**
     * 添加供应商
     */
    @Transactional
    public void addSupplier(Long resourceId, Long supplierId) {
        Resource resource = getResource(resourceId);
        Supplier supplier = supplierRepository.findById(supplierId)
            .orElseThrow(() -> new RuntimeException("供应商不存在: " + supplierId));
        
        resource.addSupplier(supplier);
        resourceRepository.save(resource);
    }

    /**
     * 更新成本拆分
     */
    @Transactional
    public void updateCostBreakdown(Long resourceId, Resource.CostBreakdown breakdown) {
        Resource resource = getResource(resourceId);
        resource.updateCostBreakdown(breakdown);
        resourceRepository.save(resource);
    }

    /**
     * 更新资源使用量
     */
    @Transactional
    public void updateUsage(Long resourceId, BigDecimal quantity) {
        Resource resource = getResource(resourceId);
        resource.updateUsage(quantity);
        resourceRepository.save(resource);
    }

    /**
     * 检查资源库存
     */
    public Map<String, Object> checkStock(Long resourceId, BigDecimal requiredQuantity) {
        Resource resource = getResource(resourceId);
        boolean hasEnough = resource.hasEnoughStock(requiredQuantity);
        
        Map<String, Object> result = new HashMap<>();
        result.put("hasEnough", hasEnough);
        result.put("available", resource.getRemainingQuantity());
        result.put("required", requiredQuantity);
        result.put("shortage", hasEnough ? BigDecimal.ZERO : 
            requiredQuantity.subtract(resource.getRemainingQuantity()));
        
        return result;
    }

    /**
     * 生成资源使用报告
     */
    public Map<String, Object> generateUsageReport(Long resourceId) {
        Resource resource = getResource(resourceId);
        
        Map<String, Object> report = new HashMap<>();
        report.put("resource", resource);
        report.put("totalQuantity", resource.getTotalQuantity());
        report.put("usedQuantity", resource.getUsedQuantity());
        report.put("remainingQuantity", resource.getRemainingQuantity());
        report.put("currentPrice", resource.getCurrentPrice());
        report.put("totalValue", resource.getCurrentPrice().multiply(resource.getRemainingQuantity()));
        
        // 添加价格趋势
        List<Map<String, Object>> priceTrend = resource.getPriceHistory().stream()
            .sorted(Comparator.comparing(ResourcePrice::getEffectiveDate))
            .map(price -> {
                Map<String, Object> pricePoint = new HashMap<>();
                pricePoint.put("date", price.getEffectiveDate());
                pricePoint.put("price", price.getPrice());
                return pricePoint;
            })
            .collect(Collectors.toList());
        report.put("priceTrend", priceTrend);
        
        return report;
    }

    /**
     * 生成资源预警
     */
    public List<Map<String, Object>> generateAlerts() {
        List<Map<String, Object>> alerts = new ArrayList<>();
        List<Resource> resources = resourceRepository.findAll();
        
        for (Resource resource : resources) {
            // 库存预警
            if (resource.getRemainingQuantity().compareTo(getMinimumStock(resource)) < 0) {
                Map<String, Object> alert = new HashMap<>();
                alert.put("type", "STOCK_LOW");
                alert.put("resource", resource);
                alert.put("current", resource.getRemainingQuantity());
                alert.put("minimum", getMinimumStock(resource));
                alerts.add(alert);
            }
            
            // 价格异常预警
            if (isPriceAbnormal(resource)) {
                Map<String, Object> alert = new HashMap<>();
                alert.put("type", "PRICE_ABNORMAL");
                alert.put("resource", resource);
                alert.put("current", resource.getCurrentPrice());
                alert.put("average", calculateAveragePrice(resource));
                alerts.add(alert);
            }
        }
        
        return alerts;
    }

    // 私有辅助方法

    private void validateResource(Resource resource) {
        if (resource.getCode() == null || resource.getCode().isEmpty()) {
            throw new IllegalArgumentException("资源编号不能为空");
        }
        if (resource.getName() == null || resource.getName().isEmpty()) {
            throw new IllegalArgumentException("资源名称不能为空");
        }
        if (resource.getType() == null) {
            throw new IllegalArgumentException("资源类型不能为空");
        }
    }

    private void updateResourceFields(Resource existing, Resource updated) {
        existing.setName(updated.getName());
        existing.setSpecification(updated.getSpecification());
        existing.setUnit(updated.getUnit());
        existing.setType(updated.getType());
        // 不更新code，这是标识字段
    }

    private BigDecimal getMinimumStock(Resource resource) {
        // TODO: 根据资源类型和历史使用情况计算最低库存
        return BigDecimal.ZERO;
    }

    private boolean isPriceAbnormal(Resource resource) {
        if (resource.getPriceHistory().size() < 2) {
            return false;
        }
        
        BigDecimal average = calculateAveragePrice(resource);
        BigDecimal current = resource.getCurrentPrice();
        
        // 价格波动超过20%视为异常
        BigDecimal threshold = average.multiply(new BigDecimal("0.2"));
        return current.subtract(average).abs().compareTo(threshold) > 0;
    }

    private BigDecimal calculateAveragePrice(Resource resource) {
        return resource.getPriceHistory().stream()
            .map(ResourcePrice::getPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .divide(new BigDecimal(resource.getPriceHistory().size()), 2, BigDecimal.ROUND_HALF_UP);
    }
}
