{"ast": null, "code": "import { format } from './print';\n/**\n * <zh/> 获取节点/边/Combo 的 ID\n *\n * <en/> get the id of node/edge/combo\n * @param data - <zh/> 节点/边/Combo 的数据 | <en/> data of node/edge/combo\n * @returns <zh/> 节点/边/Combo 的 ID | <en/> ID of node/edge/combo\n */\nexport function idOf(data) {\n  if (data.id !== undefined) return data.id;\n  if (data.source !== undefined && data.target !== undefined) return `${data.source}-${data.target}`;\n  throw new Error(format('The datum does not have available id.'));\n}\n/**\n * <zh/> 获取节点/Combo 的父节点 ID\n *\n * <en/> get the parent id of node/combo\n * @param data - <zh/> 节点/Combo 的数据 | <en/> data of node/combo\n * @returns <zh/> 节点/Combo 的父节点 ID | <en/> parent id of node/combo\n */\nexport function parentIdOf(data) {\n  return data.combo;\n}\n/**\n * <zh/> 获取图数据中所有节点/边/Combo 的 ID\n *\n * <en/> Get the IDs of all nodes/edges/combos in the graph data\n * @param data - <zh/> 图数据 | <en/> graph data\n * @param flat - <zh/> 是否扁平化返回 | <en/> Whether to return flat\n * @returns - <zh/> 返回元素 ID 数组 | <en/> Returns an array of element IDs\n */\nexport function idsOf(data, flat) {\n  const ids = {\n    nodes: (data.nodes || []).map(idOf),\n    edges: (data.edges || []).map(idOf),\n    combos: (data.combos || []).map(idOf)\n  };\n  return flat ? Object.values(ids).flat() : ids;\n}", "map": {"version": 3, "names": ["format", "idOf", "data", "id", "undefined", "source", "target", "Error", "parentIdOf", "combo", "idsOf", "flat", "ids", "nodes", "map", "edges", "combos", "Object", "values"], "sources": ["../../src/utils/id.ts"], "sourcesContent": [null], "mappings": "AAEA,SAASA,MAAM,QAAQ,SAAS;AAEhC;;;;;;;AAOA,OAAM,SAAUC,IAAIA,CAACC,IAA8C;EACjE,IAAIA,IAAI,CAACC,EAAE,KAAKC,SAAS,EAAE,OAAOF,IAAI,CAACC,EAAE;EACzC,IAAID,IAAI,CAACG,MAAM,KAAKD,SAAS,IAAIF,IAAI,CAACI,MAAM,KAAKF,SAAS,EAAE,OAAO,GAAGF,IAAI,CAACG,MAAM,IAAIH,IAAI,CAACI,MAAM,EAAE;EAElG,MAAM,IAAIC,KAAK,CAACP,MAAM,CAAC,uCAAuC,CAAC,CAAC;AAClE;AAEA;;;;;;;AAOA,OAAM,SAAUQ,UAAUA,CAACN,IAAmC;EAC5D,OAAOA,IAAI,CAACO,KAAK;AACnB;AAIA;;;;;;;;AAQA,OAAM,SAAUC,KAAKA,CAACR,IAAe,EAAES,IAAa;EAClD,MAAMC,GAAG,GAAG;IACVC,KAAK,EAAE,CAACX,IAAI,CAACW,KAAK,IAAI,EAAE,EAAEC,GAAG,CAACb,IAAI,CAAC;IACnCc,KAAK,EAAE,CAACb,IAAI,CAACa,KAAK,IAAI,EAAE,EAAED,GAAG,CAACb,IAAI,CAAC;IACnCe,MAAM,EAAE,CAACd,IAAI,CAACc,MAAM,IAAI,EAAE,EAAEF,GAAG,CAACb,IAAI;GACrC;EACD,OAAOU,IAAI,GAAGM,MAAM,CAACC,MAAM,CAACN,GAAG,CAAC,CAACD,IAAI,EAAE,GAAGC,GAAG;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}