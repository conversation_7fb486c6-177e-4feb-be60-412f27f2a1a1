{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TikTokOutlinedSvg from \"@ant-design/icons-svg/es/asn/TikTokOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TikTokOutlined = function TikTokOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TikTokOutlinedSvg\n  }));\n};\n\n/**![tik-tok](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTMwLjAxIDExMi42N2M0My42Ny0uNjcgODctLjM0IDEzMC4zMy0uNjcgMi42NyA1MSAyMSAxMDMgNTguMzMgMTM5IDM3LjMzIDM3IDkwIDU0IDE0MS4zMyA1OS42NlY0NDVjLTQ4LTEuNjctOTYuMzMtMTEuNjctMTQwLTMyLjM0LTE5LTguNjYtMzYuNjYtMTkuNjYtNTQtMzEtLjMzIDk3LjMzLjM0IDE5NC42Ny0uNjYgMjkxLjY3LTIuNjcgNDYuNjYtMTggOTMtNDUgMTMxLjMzLTQzLjY2IDY0LTExOS4zMiAxMDUuNjYtMTk2Ljk5IDEwNy00Ny42NiAyLjY2LTk1LjMzLTEwLjM0LTEzNi0zNC4zNEMyMjAuMDQgODM3LjY2IDE3Mi43IDc2NSAxNjUuNyA2ODdjLS42Ny0xNi42Ni0xLTMzLjMzLS4zNC00OS42NiA2LTYzLjM0IDM3LjMzLTEyNCA4Ni0xNjUuMzQgNTUuMzMtNDggMTMyLjY2LTcxIDIwNC45OS01Ny4zMy42NyA0OS4zNC0xLjMzIDk4LjY3LTEuMzMgMTQ4LTMzLTEwLjY3LTcxLjY3LTcuNjctMTAwLjY3IDEyLjMzLTIxIDEzLjY3LTM3IDM0LjY3LTQ1LjMzIDU4LjM0LTcgMTctNSAzNS42Ni00LjY2IDUzLjY2IDggNTQuNjcgNjAuNjYgMTAwLjY3IDExNi42NiA5NS42NyAzNy4zMy0uMzQgNzMtMjIgOTIuMzMtNTMuNjcgNi4zMy0xMSAxMy4zMy0yMi4zMyAxMy42Ni0zNS4zMyAzLjM0LTU5LjY3IDItMTE5IDIuMzQtMTc4LjY2LjMzLTEzNC4zNC0uMzQtMjY4LjMzLjY2LTQwMi4zMyIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TikTokOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TikTokOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "TikTokOutlinedSvg", "AntdIcon", "TikTokOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/TikTokOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TikTokOutlinedSvg from \"@ant-design/icons-svg/es/asn/TikTokOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TikTokOutlined = function TikTokOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TikTokOutlinedSvg\n  }));\n};\n\n/**![tik-tok](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTMwLjAxIDExMi42N2M0My42Ny0uNjcgODctLjM0IDEzMC4zMy0uNjcgMi42NyA1MSAyMSAxMDMgNTguMzMgMTM5IDM3LjMzIDM3IDkwIDU0IDE0MS4zMyA1OS42NlY0NDVjLTQ4LTEuNjctOTYuMzMtMTEuNjctMTQwLTMyLjM0LTE5LTguNjYtMzYuNjYtMTkuNjYtNTQtMzEtLjMzIDk3LjMzLjM0IDE5NC42Ny0uNjYgMjkxLjY3LTIuNjcgNDYuNjYtMTggOTMtNDUgMTMxLjMzLTQzLjY2IDY0LTExOS4zMiAxMDUuNjYtMTk2Ljk5IDEwNy00Ny42NiAyLjY2LTk1LjMzLTEwLjM0LTEzNi0zNC4zNEMyMjAuMDQgODM3LjY2IDE3Mi43IDc2NSAxNjUuNyA2ODdjLS42Ny0xNi42Ni0xLTMzLjMzLS4zNC00OS42NiA2LTYzLjM0IDM3LjMzLTEyNCA4Ni0xNjUuMzQgNTUuMzMtNDggMTMyLjY2LTcxIDIwNC45OS01Ny4zMy42NyA0OS4zNC0xLjMzIDk4LjY3LTEuMzMgMTQ4LTMzLTEwLjY3LTcxLjY3LTcuNjctMTAwLjY3IDEyLjMzLTIxIDEzLjY3LTM3IDM0LjY3LTQ1LjMzIDU4LjM0LTcgMTctNSAzNS42Ni00LjY2IDUzLjY2IDggNTQuNjcgNjAuNjYgMTAwLjY3IDExNi42NiA5NS42NyAzNy4zMy0uMzQgNzMtMjIgOTIuMzMtNTMuNjcgNi4zMy0xMSAxMy4zMy0yMi4zMyAxMy42Ni0zNS4zMyAzLjM0LTU5LjY3IDItMTE5IDIuMzQtMTc4LjY2LjMzLTEzNC4zNC0uMzQtMjY4LjMzLjY2LTQwMi4zMyIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TikTokOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TikTokOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,cAAc,CAAC;AAC3D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,gBAAgB;AACxC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}