{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\cost-project-dev2\\\\frontend\\\\src\\\\pages\\\\calendar\\\\CalendarPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Calendar, Card, Badge, Modal, Button } from 'antd';\nimport { PlusOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CalendarPage = () => {\n  _s();\n  const [modalVisible, setModalVisible] = useState(false);\n  const [selectedDate, setSelectedDate] = useState(null);\n\n  // 模拟事件数据\n  const events = {\n    '2024-01-15': [{\n      type: 'success',\n      content: '项目1启动会议'\n    }, {\n      type: 'warning',\n      content: '预算审核'\n    }],\n    '2024-01-20': [{\n      type: 'error',\n      content: '项目截止日期'\n    }],\n    '2024-01-25': [{\n      type: 'processing',\n      content: '成本评估会议'\n    }]\n  };\n  const getListData = value => {\n    const dateStr = value.format('YYYY-MM-DD');\n    return events[dateStr] || [];\n  };\n  const cellRender = (value, info) => {\n    // 只处理日期类型的单元格\n    if (info.type !== 'date') {\n      return info.originNode;\n    }\n    const listData = getListData(value);\n    return /*#__PURE__*/_jsxDEV(\"ul\", {\n      style: {\n        listStyle: 'none',\n        padding: 0,\n        margin: 0\n      },\n      children: listData.map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Badge, {\n          status: item.type,\n          text: item.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this);\n  };\n  const onSelect = value => {\n    setSelectedDate(value);\n    setModalVisible(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u9879\\u76EE\\u65E5\\u7A0B\\u7BA1\\u7406\",\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 40\n        }, this),\n        children: \"\\u65B0\\u5EFA\\u4E8B\\u4EF6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Calendar, {\n        dateCellRender: dateCellRender,\n        onSelect: onSelect\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `${selectedDate === null || selectedDate === void 0 ? void 0 : selectedDate.format('YYYY-MM-DD')} 的事件`,\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        children: \"\\u6DFB\\u52A0\\u4E8B\\u4EF6\"\n      }, \"add\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 11\n      }, this)],\n      children: selectedDate && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u65E5\\u671F\\uFF1A\", selectedDate.format('YYYY年MM月DD日')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u5F53\\u65E5\\u4E8B\\u4EF6\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this), getListData(selectedDate).length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: getListData(selectedDate).map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Badge, {\n              status: item.type,\n              text: item.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 21\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u6682\\u65E0\\u4E8B\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s(CalendarPage, \"7JzcHFC430JnH/3/ikx+pWF3YS0=\");\n_c = CalendarPage;\nexport default CalendarPage;\nvar _c;\n$RefreshReg$(_c, \"CalendarPage\");", "map": {"version": 3, "names": ["React", "useState", "Calendar", "Card", "Badge", "Modal", "<PERSON><PERSON>", "PlusOutlined", "jsxDEV", "_jsxDEV", "CalendarPage", "_s", "modalVisible", "setModalVisible", "selectedDate", "setSelectedDate", "events", "type", "content", "getListData", "value", "dateStr", "format", "cellRender", "info", "originNode", "listData", "style", "listStyle", "padding", "margin", "children", "map", "item", "index", "status", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSelect", "title", "extra", "icon", "date<PERSON>ell<PERSON><PERSON>", "open", "onCancel", "footer", "onClick", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/src/pages/calendar/CalendarPage.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Calendar, Card, Badge, Modal, Button } from 'antd';\r\nimport { PlusOutlined } from '@ant-design/icons';\r\n\r\nconst CalendarPage = () => {\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [selectedDate, setSelectedDate] = useState(null);\r\n\r\n  // 模拟事件数据\r\n  const events = {\r\n    '2024-01-15': [\r\n      { type: 'success', content: '项目1启动会议' },\r\n      { type: 'warning', content: '预算审核' },\r\n    ],\r\n    '2024-01-20': [\r\n      { type: 'error', content: '项目截止日期' },\r\n    ],\r\n    '2024-01-25': [\r\n      { type: 'processing', content: '成本评估会议' },\r\n    ],\r\n  };\r\n\r\n  const getListData = (value) => {\r\n    const dateStr = value.format('YYYY-MM-DD');\r\n    return events[dateStr] || [];\r\n  };\r\n\r\n  const cellRender = (value, info) => {\r\n    // 只处理日期类型的单元格\r\n    if (info.type !== 'date') {\r\n      return info.originNode;\r\n    }\r\n\r\n    const listData = getListData(value);\r\n    return (\r\n      <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>\r\n        {listData.map((item, index) => (\r\n          <li key={index}>\r\n            <Badge status={item.type} text={item.content} />\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    );\r\n  };\r\n\r\n  const onSelect = (value) => {\r\n    setSelectedDate(value);\r\n    setModalVisible(true);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <Card \r\n        title=\"项目日程管理\" \r\n        extra={\r\n          <Button type=\"primary\" icon={<PlusOutlined />}>\r\n            新建事件\r\n          </Button>\r\n        }\r\n      >\r\n        <Calendar \r\n          dateCellRender={dateCellRender}\r\n          onSelect={onSelect}\r\n        />\r\n      </Card>\r\n\r\n      <Modal\r\n        title={`${selectedDate?.format('YYYY-MM-DD')} 的事件`}\r\n        open={modalVisible}\r\n        onCancel={() => setModalVisible(false)}\r\n        footer={[\r\n          <Button key=\"close\" onClick={() => setModalVisible(false)}>\r\n            关闭\r\n          </Button>,\r\n          <Button key=\"add\" type=\"primary\">\r\n            添加事件\r\n          </Button>,\r\n        ]}\r\n      >\r\n        {selectedDate && (\r\n          <div>\r\n            <p>日期：{selectedDate.format('YYYY年MM月DD日')}</p>\r\n            <p>当日事件：</p>\r\n            {getListData(selectedDate).length > 0 ? (\r\n              <ul>\r\n                {getListData(selectedDate).map((item, index) => (\r\n                  <li key={index}>\r\n                    <Badge status={item.type} text={item.content} />\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            ) : (\r\n              <p>暂无事件</p>\r\n            )}\r\n          </div>\r\n        )}\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CalendarPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC3D,SAASC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAMe,MAAM,GAAG;IACb,YAAY,EAAE,CACZ;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAU,CAAC,EACvC;MAAED,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAO,CAAC,CACrC;IACD,YAAY,EAAE,CACZ;MAAED,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAS,CAAC,CACrC;IACD,YAAY,EAAE,CACZ;MAAED,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE;IAAS,CAAC;EAE7C,CAAC;EAED,MAAMC,WAAW,GAAIC,KAAK,IAAK;IAC7B,MAAMC,OAAO,GAAGD,KAAK,CAACE,MAAM,CAAC,YAAY,CAAC;IAC1C,OAAON,MAAM,CAACK,OAAO,CAAC,IAAI,EAAE;EAC9B,CAAC;EAED,MAAME,UAAU,GAAGA,CAACH,KAAK,EAAEI,IAAI,KAAK;IAClC;IACA,IAAIA,IAAI,CAACP,IAAI,KAAK,MAAM,EAAE;MACxB,OAAOO,IAAI,CAACC,UAAU;IACxB;IAEA,MAAMC,QAAQ,GAAGP,WAAW,CAACC,KAAK,CAAC;IACnC,oBACEX,OAAA;MAAIkB,KAAK,EAAE;QAAEC,SAAS,EAAE,MAAM;QAAEC,OAAO,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAE;MAAAC,QAAA,EACrDL,QAAQ,CAACM,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxBzB,OAAA;QAAAsB,QAAA,eACEtB,OAAA,CAACL,KAAK;UAAC+B,MAAM,EAAEF,IAAI,CAAChB,IAAK;UAACmB,IAAI,EAAEH,IAAI,CAACf;QAAQ;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GADzCN,KAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAET,CAAC;EAED,MAAMC,QAAQ,GAAIrB,KAAK,IAAK;IAC1BL,eAAe,CAACK,KAAK,CAAC;IACtBP,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,oBACEJ,OAAA;IAAAsB,QAAA,gBACEtB,OAAA,CAACN,IAAI;MACHuC,KAAK,EAAC,sCAAQ;MACdC,KAAK,eACHlC,OAAA,CAACH,MAAM;QAACW,IAAI,EAAC,SAAS;QAAC2B,IAAI,eAAEnC,OAAA,CAACF,YAAY;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAT,QAAA,EAAC;MAE/C;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;MAAAT,QAAA,eAEDtB,OAAA,CAACP,QAAQ;QACP2C,cAAc,EAAEA,cAAe;QAC/BJ,QAAQ,EAAEA;MAAS;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEP/B,OAAA,CAACJ,KAAK;MACJqC,KAAK,EAAE,GAAG5B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEQ,MAAM,CAAC,YAAY,CAAC,MAAO;MACnDwB,IAAI,EAAElC,YAAa;MACnBmC,QAAQ,EAAEA,CAAA,KAAMlC,eAAe,CAAC,KAAK,CAAE;MACvCmC,MAAM,EAAE,cACNvC,OAAA,CAACH,MAAM;QAAa2C,OAAO,EAAEA,CAAA,KAAMpC,eAAe,CAAC,KAAK,CAAE;QAAAkB,QAAA,EAAC;MAE3D,GAFY,OAAO;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,eACT/B,OAAA,CAACH,MAAM;QAAWW,IAAI,EAAC,SAAS;QAAAc,QAAA,EAAC;MAEjC,GAFY,KAAK;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAET,CAAC,CACT;MAAAT,QAAA,EAEDjB,YAAY,iBACXL,OAAA;QAAAsB,QAAA,gBACEtB,OAAA;UAAAsB,QAAA,GAAG,oBAAG,EAACjB,YAAY,CAACQ,MAAM,CAAC,aAAa,CAAC;QAAA;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9C/B,OAAA;UAAAsB,QAAA,EAAG;QAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EACXrB,WAAW,CAACL,YAAY,CAAC,CAACoC,MAAM,GAAG,CAAC,gBACnCzC,OAAA;UAAAsB,QAAA,EACGZ,WAAW,CAACL,YAAY,CAAC,CAACkB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzCzB,OAAA;YAAAsB,QAAA,eACEtB,OAAA,CAACL,KAAK;cAAC+B,MAAM,EAAEF,IAAI,CAAChB,IAAK;cAACmB,IAAI,EAAEH,IAAI,CAACf;YAAQ;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GADzCN,KAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,gBAEL/B,OAAA;UAAAsB,QAAA,EAAG;QAAI;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC7B,EAAA,CA/FID,YAAY;AAAAyC,EAAA,GAAZzC,YAAY;AAiGlB,eAAeA,YAAY;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}