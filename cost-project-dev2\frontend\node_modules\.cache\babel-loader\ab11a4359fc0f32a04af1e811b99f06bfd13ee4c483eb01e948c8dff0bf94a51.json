{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport MailTwoToneSvg from \"@ant-design/icons-svg/es/asn/MailTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar MailTwoTone = function MailTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: MailTwoToneSvg\n  }));\n};\n\n/**![mail](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ3Ny41IDUzNi4zTDEzNS45IDI3MC43bC0yNy41LTIxLjQgMjcuNiAyMS41Vjc5Mmg3NTJWMjcwLjhMNTQ2LjIgNTM2LjNhNTUuOTkgNTUuOTkgMCAwMS02OC43IDB6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04NzYuMyAxOTguOGwzOS4zIDUwLjUtMjcuNiAyMS41IDI3LjctMjEuNS0zOS4zLTUwLjV6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik05MjggMTYwSDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NDBjMCAxNy43IDE0LjMgMzIgMzIgMzJoODMyYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE5MmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTk0LjUgNzIuMUw1MTIgNDgyIDE5MC41IDIzMi4xaDY0M3ptNTQuNSAzOC43Vjc5MkgxMzZWMjcwLjhsLTI3LjYtMjEuNSAyNy41IDIxLjQgMzQxLjYgMjY1LjZhNTUuOTkgNTUuOTkgMCAwMDY4LjcgMEw4ODggMjcwLjhsMjcuNi0yMS41LTM5LjMtNTAuNWguMWwzOS4zIDUwLjUtMjcuNyAyMS41eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(MailTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'MailTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "MailTwoToneSvg", "AntdIcon", "MailTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/MailTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport MailTwoToneSvg from \"@ant-design/icons-svg/es/asn/MailTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar MailTwoTone = function MailTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: MailTwoToneSvg\n  }));\n};\n\n/**![mail](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ3Ny41IDUzNi4zTDEzNS45IDI3MC43bC0yNy41LTIxLjQgMjcuNiAyMS41Vjc5Mmg3NTJWMjcwLjhMNTQ2LjIgNTM2LjNhNTUuOTkgNTUuOTkgMCAwMS02OC43IDB6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04NzYuMyAxOTguOGwzOS4zIDUwLjUtMjcuNiAyMS41IDI3LjctMjEuNS0zOS4zLTUwLjV6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik05MjggMTYwSDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NDBjMCAxNy43IDE0LjMgMzIgMzIgMzJoODMyYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE5MmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTk0LjUgNzIuMUw1MTIgNDgyIDE5MC41IDIzMi4xaDY0M3ptNTQuNSAzOC43Vjc5MkgxMzZWMjcwLjhsLTI3LjYtMjEuNSAyNy41IDIxLjQgMzQxLjYgMjY1LjZhNTUuOTkgNTUuOTkgMCAwMDY4LjcgMEw4ODggMjcwLjhsMjcuNi0yMS41LTM5LjMtNTAuNWguMWwzOS4zIDUwLjUtMjcuNyAyMS41eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(MailTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'MailTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}