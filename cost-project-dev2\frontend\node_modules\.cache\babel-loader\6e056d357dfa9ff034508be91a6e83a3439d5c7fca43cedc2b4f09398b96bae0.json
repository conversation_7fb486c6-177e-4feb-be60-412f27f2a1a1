{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FileSyncOutlinedSvg from \"@ant-design/icons-svg/es/asn/FileSyncOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FileSyncOutlined = function FileSyncOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FileSyncOutlinedSvg\n  }));\n};\n\n/**![file-sync](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5NiAyNTZjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMzg0YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04SDI5NnptMTkyIDIwMHYtNDhjMC00LjQtMy42LTgtOC04SDI5NmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgxODRjNC40IDAgOC0zLjYgOC04em0tNDggMzk2SDIwOFYxNDhoNTYwdjM0NGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjEwOGMwLTE3LjctMTQuMy0zMi0zMi0zMkgxNjhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjc4NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgyNzJjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6bTEwNC4xLTExNS42YzEuOC0zNC41IDE2LjItNjYuOCA0MC44LTkxLjQgMjYuMi0yNi4yIDYyLTQxIDk5LjEtNDEgMzcuNCAwIDcyLjYgMTQuNiA5OS4xIDQxIDMuMiAzLjIgNi4zIDYuNiA5LjIgMTAuMUw3NjkuMiA2NzNhOCA4IDAgMDAzIDE0LjFsOTMuMyAyMi41YzUgMS4yIDkuOC0yLjYgOS45LTcuN2wuNi05NS40YTggOCAwIDAwLTEyLjktNi40bC0yMC4zIDE1LjhDODA1LjQgNTY5LjYgNzQ4LjEgNTQwIDY4NCA1NDBjLTEwOS45IDAtMTk5LjYgODYuOS0yMDQgMTk1LjctLjIgNC41IDMuNSA4LjMgOCA4LjNoNDguMWM0LjMgMCA3LjgtMy4zIDgtNy42ek04ODAgNzQ0aC00OC4xYy00LjMgMC03LjggMy4zLTggNy42LTEuOCAzNC41LTE2LjIgNjYuOC00MC44IDkxLjQtMjYuMiAyNi4yLTYyIDQxLTk5LjEgNDEtMzcuNCAwLTcyLjYtMTQuNi05OS4xLTQxLTMuMi0zLjItNi4zLTYuNi05LjItMTAuMWwyMy4xLTE3LjlhOCA4IDAgMDAtMy0xNC4xbC05My4zLTIyLjVjLTUtMS4yLTkuOCAyLjYtOS45IDcuN2wtLjYgOTUuNGE4IDggMCAwMDEyLjkgNi40bDIwLjMtMTUuOEM1NjIuNiA5MTguNCA2MTkuOSA5NDggNjg0IDk0OGMxMDkuOSAwIDE5OS42LTg2LjkgMjA0LTE5NS43LjItNC41LTMuNS04LjMtOC04LjN6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FileSyncOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FileSyncOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "FileSyncOutlinedSvg", "AntdIcon", "FileSyncOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/FileSyncOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FileSyncOutlinedSvg from \"@ant-design/icons-svg/es/asn/FileSyncOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FileSyncOutlined = function FileSyncOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FileSyncOutlinedSvg\n  }));\n};\n\n/**![file-sync](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5NiAyNTZjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMzg0YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04SDI5NnptMTkyIDIwMHYtNDhjMC00LjQtMy42LTgtOC04SDI5NmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgxODRjNC40IDAgOC0zLjYgOC04em0tNDggMzk2SDIwOFYxNDhoNTYwdjM0NGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjEwOGMwLTE3LjctMTQuMy0zMi0zMi0zMkgxNjhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjc4NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgyNzJjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6bTEwNC4xLTExNS42YzEuOC0zNC41IDE2LjItNjYuOCA0MC44LTkxLjQgMjYuMi0yNi4yIDYyLTQxIDk5LjEtNDEgMzcuNCAwIDcyLjYgMTQuNiA5OS4xIDQxIDMuMiAzLjIgNi4zIDYuNiA5LjIgMTAuMUw3NjkuMiA2NzNhOCA4IDAgMDAzIDE0LjFsOTMuMyAyMi41YzUgMS4yIDkuOC0yLjYgOS45LTcuN2wuNi05NS40YTggOCAwIDAwLTEyLjktNi40bC0yMC4zIDE1LjhDODA1LjQgNTY5LjYgNzQ4LjEgNTQwIDY4NCA1NDBjLTEwOS45IDAtMTk5LjYgODYuOS0yMDQgMTk1LjctLjIgNC41IDMuNSA4LjMgOCA4LjNoNDguMWM0LjMgMCA3LjgtMy4zIDgtNy42ek04ODAgNzQ0aC00OC4xYy00LjMgMC03LjggMy4zLTggNy42LTEuOCAzNC41LTE2LjIgNjYuOC00MC44IDkxLjQtMjYuMiAyNi4yLTYyIDQxLTk5LjEgNDEtMzcuNCAwLTcyLjYtMTQuNi05OS4xLTQxLTMuMi0zLjItNi4zLTYuNi05LjItMTAuMWwyMy4xLTE3LjlhOCA4IDAgMDAtMy0xNC4xbC05My4zLTIyLjVjLTUtMS4yLTkuOCAyLjYtOS45IDcuN2wtLjYgOTUuNGE4IDggMCAwMDEyLjkgNi40bDIwLjMtMTUuOEM1NjIuNiA5MTguNCA2MTkuOSA5NDggNjg0IDk0OGMxMDkuOSAwIDE5OS42LTg2LjkgMjA0LTE5NS43LjItNC41LTMuNS04LjMtOC04LjN6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FileSyncOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FileSyncOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC3D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,gBAAgB,CAAC;AAC7D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,kBAAkB;AAC1C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}