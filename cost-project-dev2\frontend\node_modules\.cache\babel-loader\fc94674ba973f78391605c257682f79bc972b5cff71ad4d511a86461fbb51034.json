{"ast": null, "code": "import { deepMix } from '@antv/util';\nimport { Light } from './light';\n/**\n * Default theme.\n */\nexport const Classic = options => {\n  return deepMix({}, Light(), {\n    category10: 'category10',\n    category20: 'category20'\n  }, options);\n};\nClassic.props = {};", "map": {"version": 3, "names": ["deepMix", "Light", "Classic", "options", "category10", "category20", "props"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\g2\\src\\theme\\classic.ts"], "sourcesContent": ["import { deepMix } from '@antv/util';\nimport { ThemeComponent as TC, Theme } from '../runtime';\nimport { Light } from './light';\n\nexport type ClassicOptions = Theme;\n\n/**\n * Default theme.\n */\nexport const Classic: TC<ClassicOptions> = (options) => {\n  return deepMix(\n    {},\n    Light(),\n    {\n      category10: 'category10',\n      category20: 'category20',\n    },\n    options,\n  );\n};\n\nClassic.props = {};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AAEpC,SAASC,KAAK,QAAQ,SAAS;AAI/B;;;AAGA,OAAO,MAAMC,OAAO,GAAwBC,OAAO,IAAI;EACrD,OAAOH,OAAO,CACZ,EAAE,EACFC,KAAK,EAAE,EACP;IACEG,UAAU,EAAE,YAAY;IACxBC,UAAU,EAAE;GACb,EACDF,OAAO,CACR;AACH,CAAC;AAEDD,OAAO,CAACI,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}