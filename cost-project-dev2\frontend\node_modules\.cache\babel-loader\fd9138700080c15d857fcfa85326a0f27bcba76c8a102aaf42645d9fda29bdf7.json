{"ast": null, "code": "export { Polyline } from '@antv/g';", "map": {"version": 3, "names": ["Polyline"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\component\\src\\shapes\\Polyline.ts"], "sourcesContent": ["import type { PolylineStyleProps as GPolylineStyleProps } from '@antv/g';\nimport { OmitConflictStyleProps } from './types';\n\nexport { Polyline } from '@antv/g';\nexport type PolylineStyleProps = OmitConflictStyleProps<GPolylineStyleProps>;\n"], "mappings": "AAGA,SAASA,QAAQ,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}