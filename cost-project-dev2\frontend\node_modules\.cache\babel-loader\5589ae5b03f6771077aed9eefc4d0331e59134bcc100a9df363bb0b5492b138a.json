{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FileZipTwoToneSvg from \"@ant-design/icons-svg/es/asn/FileZipTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FileZipTwoTone = function FileZipTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FileZipTwoToneSvg\n  }));\n};\n\n/**![file-zip](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM0NCA2MzBoMzJ2MmgtMzJ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik01MzQgMzUyVjEzNkgzNjB2NjRoNjR2NjRoLTY0djY0aDY0djY0aC02NHY2NGg2NHY2NGgtNjR2NjJoNjR2MTYwSDI5NlY1MjBoNjR2LTY0aC02NHYtNjRoNjR2LTY0aC02NHYtNjRoNjR2LTY0aC02NHYtNjRoLTY0djc1Mmg1NjBWMzk0SDU3NmE0MiA0MiAwIDAxLTQyLTQyeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODU0LjYgMjg4LjZMNjM5LjQgNzMuNGMtNi02LTE0LjEtOS40LTIyLjYtOS40SDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2ODMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDY0MGMxNy43IDAgMzItMTQuMyAzMi0zMlYzMTEuM2MwLTguNS0zLjQtMTYuNy05LjQtMjIuN3pNNjAyIDEzNy44TDc5MC4yIDMyNkg2MDJWMTM3Ljh6TTc5MiA4ODhIMjMyVjEzNmg2NHY2NGg2NHYtNjRoMTc0djIxNmE0MiA0MiAwIDAwNDIgNDJoMjE2djQ5NHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTI5NiAzOTJoNjR2NjRoLTY0em0wLTEyOGg2NHY2NGgtNjR6bTAgMzE4djE2MGgxMjhWNTgyaC02NHYtNjJoLTY0djYyem00OCA1MHYtMmgzMnY2NGgtMzJ2LTYyem0xNi00MzJoNjR2NjRoLTY0em0wIDI1Nmg2NHY2NGgtNjR6bTAtMTI4aDY0djY0aC02NHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FileZipTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FileZipTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "FileZipTwoToneSvg", "AntdIcon", "FileZipTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/FileZipTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FileZipTwoToneSvg from \"@ant-design/icons-svg/es/asn/FileZipTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FileZipTwoTone = function FileZipTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FileZipTwoToneSvg\n  }));\n};\n\n/**![file-zip](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM0NCA2MzBoMzJ2MmgtMzJ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik01MzQgMzUyVjEzNkgzNjB2NjRoNjR2NjRoLTY0djY0aDY0djY0aC02NHY2NGg2NHY2NGgtNjR2NjJoNjR2MTYwSDI5NlY1MjBoNjR2LTY0aC02NHYtNjRoNjR2LTY0aC02NHYtNjRoNjR2LTY0aC02NHYtNjRoLTY0djc1Mmg1NjBWMzk0SDU3NmE0MiA0MiAwIDAxLTQyLTQyeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODU0LjYgMjg4LjZMNjM5LjQgNzMuNGMtNi02LTE0LjEtOS40LTIyLjYtOS40SDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2ODMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDY0MGMxNy43IDAgMzItMTQuMyAzMi0zMlYzMTEuM2MwLTguNS0zLjQtMTYuNy05LjQtMjIuN3pNNjAyIDEzNy44TDc5MC4yIDMyNkg2MDJWMTM3Ljh6TTc5MiA4ODhIMjMyVjEzNmg2NHY2NGg2NHYtNjRoMTc0djIxNmE0MiA0MiAwIDAwNDIgNDJoMjE2djQ5NHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTI5NiAzOTJoNjR2NjRoLTY0em0wLTEyOGg2NHY2NGgtNjR6bTAgMzE4djE2MGgxMjhWNTgyaC02NHYtNjJoLTY0djYyem00OCA1MHYtMmgzMnY2NGgtMzJ2LTYyem0xNi00MzJoNjR2NjRoLTY0em0wIDI1Nmg2NHY2NGgtNjR6bTAtMTI4aDY0djY0aC02NHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FileZipTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FileZipTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,cAAc,CAAC;AAC3D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,gBAAgB;AACxC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}