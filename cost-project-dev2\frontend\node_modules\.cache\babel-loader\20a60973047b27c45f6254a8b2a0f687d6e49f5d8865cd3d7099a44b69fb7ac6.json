{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SkinFilledSvg from \"@ant-design/icons-svg/es/asn/SkinFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SkinFilled = function SkinFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SkinFilledSvg\n  }));\n};\n\n/**![skin](data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SkinFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SkinFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "SkinFilledSvg", "AntdIcon", "SkinFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/SkinFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SkinFilledSvg from \"@ant-design/icons-svg/es/asn/SkinFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SkinFilled = function SkinFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SkinFilledSvg\n  }));\n};\n\n/**![skin](data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SkinFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SkinFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,yCAAyC;AACnE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/C,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,UAAU,CAAC;AACvD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,YAAY;AACpC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}