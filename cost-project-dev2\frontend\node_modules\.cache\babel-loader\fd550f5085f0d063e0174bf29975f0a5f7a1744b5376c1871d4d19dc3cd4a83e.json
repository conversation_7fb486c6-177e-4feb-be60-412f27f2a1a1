{"ast": null, "code": "import { __classPrivateFieldGet, __read, __spreadArray, __values } from \"tslib\";\nimport { flatten } from '@antv/util';\nimport { Circle, Ellipse, Group, HTML, Image, Line, Path, Polygon, Polyline, Rect, Text } from '../shapes';\nimport { group } from './group';\nfunction error(msg) {\n  throw new Error(msg);\n}\n/**\n * A simple implementation of d3-selection for @antv/g.\n * It has the core features of d3-selection and extended ability.\n * Every methods of selection returns new selection if elements\n * are mutated(e.g. append, remove), otherwise return the selection itself(e.g. attr, style).\n * @see https://github.com/d3/d3-selection\n * @see https://github.com/antvis/g\n * @todo Nested selections.\n * @todo More useful functor.\n */\nvar Selection = /** @class */function () {\n  function Selection(elements, data, parent, document, selections, transitions, updateElements) {\n    if (elements === void 0) {\n      elements = null;\n    }\n    if (data === void 0) {\n      data = null;\n    }\n    if (parent === void 0) {\n      parent = null;\n    }\n    if (document === void 0) {\n      document = null;\n    }\n    if (selections === void 0) {\n      selections = [null, null, null, null, null];\n    }\n    if (transitions === void 0) {\n      transitions = [];\n    }\n    if (updateElements === void 0) {\n      updateElements = [];\n    }\n    _Selection_instances.add(this);\n    this._elements = Array.from(elements);\n    this._data = data;\n    this._parent = parent;\n    this._document = document;\n    this._enter = selections[0];\n    this._update = selections[1];\n    this._exit = selections[2];\n    this._merge = selections[3];\n    this._split = selections[4];\n    this._transitions = transitions;\n    this._facetElements = updateElements;\n  }\n  Selection.prototype.selectAll = function (selector) {\n    var elements = typeof selector === 'string' ? this._parent.querySelectorAll(selector) : selector;\n    return new _a(elements, null, this._elements[0], this._document);\n  };\n  Selection.prototype.selectFacetAll = function (selector) {\n    var elements = typeof selector === 'string' ? this._parent.querySelectorAll(selector) : selector;\n    return new _a(this._elements, null, this._parent, this._document, undefined, undefined, elements);\n  };\n  /**\n   * @todo Replace with querySelector which has bug now.\n   */\n  Selection.prototype.select = function (selector) {\n    var element = typeof selector === 'string' ? this._parent.querySelectorAll(selector)[0] || null : selector;\n    return new _a([element], null, element, this._document);\n  };\n  Selection.prototype.append = function (node) {\n    var _this = this;\n    var callback = typeof node === 'function' ? node : function () {\n      return _this.createElement(node);\n    };\n    var elements = [];\n    if (this._data !== null) {\n      // For empty selection, append new element to parent.\n      // Each element is bind with datum.\n      for (var i = 0; i < this._data.length; i++) {\n        var d = this._data[i];\n        var _b = __read(Array.isArray(d) ? d : [d, null], 2),\n          datum = _b[0],\n          from = _b[1];\n        var newElement = callback(datum, i);\n        newElement.__data__ = datum;\n        if (from !== null) newElement.__fromElements__ = from;\n        this._parent.appendChild(newElement);\n        elements.push(newElement);\n      }\n      return new _a(elements, null, this._parent, this._document);\n    }\n    // For non-empty selection, append new element to\n    // selected element and return new selection.\n    for (var i = 0; i < this._elements.length; i++) {\n      var element = this._elements[i];\n      var datum = element.__data__;\n      var newElement = callback(datum, i);\n      element.appendChild(newElement);\n      elements.push(newElement);\n    }\n    return new _a(elements, null, elements[0], this._document);\n  };\n  Selection.prototype.maybeAppend = function (id, node) {\n    var element = __classPrivateFieldGet(this, _Selection_instances, \"m\", _Selection_maybeAppend).call(this, id[0] === '#' ? id : \"#\".concat(id), node);\n    element.attr('id', id);\n    return element;\n  };\n  Selection.prototype.maybeAppendByClassName = function (className, node) {\n    var cls = className.toString();\n    var element = __classPrivateFieldGet(this, _Selection_instances, \"m\", _Selection_maybeAppend).call(this, cls[0] === '.' ? cls : \".\".concat(cls), node);\n    element.attr('className', cls);\n    return element;\n  };\n  Selection.prototype.maybeAppendByName = function (name, node) {\n    var element = __classPrivateFieldGet(this, _Selection_instances, \"m\", _Selection_maybeAppend).call(this, \"[name=\\\"\".concat(name, \"\\\"]\"), node);\n    element.attr('name', name);\n    return element;\n  };\n  /**\n   * Bind data to elements, and produce three selection:\n   * Enter: Selection with empty elements and data to be bind to elements.\n   * Update: Selection with elements to be updated.\n   * Exit: Selection with elements to be removed.\n   */\n  Selection.prototype.data = function (data, id, groupId) {\n    var e_1, _b;\n    if (id === void 0) {\n      id = function (d) {\n        return d;\n      };\n    }\n    if (groupId === void 0) {\n      groupId = function () {\n        return null;\n      };\n    }\n    // An Array of new data.\n    var enter = [];\n    // An Array of elements to be updated.\n    var update = [];\n    // A Set of elements to be removed.\n    var exit = new Set(this._elements);\n    // An Array of data to be merged into one element.\n    var merge = [];\n    // A Set of elements to be split into multiple datum.\n    var split = new Set();\n    // A Map from key to each element.\n    var keyElement = new Map(this._elements.map(function (d, i) {\n      return [id(d.__data__, i), d];\n    }));\n    // A Map from key to exist element. The Update Selection\n    // can get element from this map, this is for diff among\n    // facets.\n    var keyUpdateElement = new Map(this._facetElements.map(function (d, i) {\n      return [id(d.__data__, i), d];\n    }));\n    // A Map from groupKey to a group of elements.\n    var groupKeyElements = group(this._elements, function (d) {\n      return groupId(d.__data__);\n    });\n    // Diff data with selection(elements with data).\n    // !!! Note\n    // The switch is strictly ordered, not not change the order of them.\n    for (var i = 0; i < data.length; i++) {\n      var datum = data[i];\n      var key = id(datum, i);\n      var groupKey = groupId(datum, i);\n      // Append element to update selection if incoming data has\n      // exactly the same key with elements.\n      if (keyElement.has(key)) {\n        var element = keyElement.get(key);\n        element.__data__ = datum;\n        element.__facet__ = false;\n        update.push(element);\n        exit.delete(element);\n        keyElement.delete(key);\n        // Append element to update selection if incoming data has\n        // exactly the same key with updateElements.\n      } else if (keyUpdateElement.has(key)) {\n        var element = keyUpdateElement.get(key);\n        element.__data__ = datum;\n        // Flag this element should update its parentNode.\n        element.__facet__ = true;\n        update.push(element);\n        keyUpdateElement.delete(key);\n        // Append datum to merge selection if existed elements has\n        // its key as groupKey.\n      } else if (groupKeyElements.has(key)) {\n        var group_2 = groupKeyElements.get(key);\n        merge.push([datum, group_2]);\n        try {\n          for (var group_1 = (e_1 = void 0, __values(group_2)), group_1_1 = group_1.next(); !group_1_1.done; group_1_1 = group_1.next()) {\n            var element = group_1_1.value;\n            exit.delete(element);\n          }\n        } catch (e_1_1) {\n          e_1 = {\n            error: e_1_1\n          };\n        } finally {\n          try {\n            if (group_1_1 && !group_1_1.done && (_b = group_1.return)) _b.call(group_1);\n          } finally {\n            if (e_1) throw e_1.error;\n          }\n        }\n        groupKeyElements.delete(key);\n        // Append element to split selection if incoming data has\n        // groupKey as its key, and bind to datum for it.\n      } else if (keyElement.has(groupKey)) {\n        var element = keyElement.get(groupKey);\n        if (element.__toData__) element.__toData__.push(datum);else element.__toData__ = [datum];\n        split.add(element);\n        exit.delete(element);\n      } else {\n        enter.push(datum);\n      }\n    }\n    // Create new selection with enter, update and exit.\n    var S = [new _a([], enter, this._parent, this._document), new _a(update, null, this._parent, this._document), new _a(exit, null, this._parent, this._document), new _a([], merge, this._parent, this._document), new _a(split, null, this._parent, this._document)];\n    return new _a(this._elements, null, this._parent, this._document, S);\n  };\n  Selection.prototype.merge = function (other) {\n    var elements = __spreadArray(__spreadArray([], __read(this._elements), false), __read(other._elements), false);\n    var transitions = __spreadArray(__spreadArray([], __read(this._transitions), false), __read(other._transitions), false);\n    return new _a(elements, null, this._parent, this._document, undefined, transitions);\n  };\n  Selection.prototype.createElement = function (type) {\n    if (this._document) {\n      return this._document.createElement(type, {});\n    }\n    var Ctor = _a.registry[type];\n    if (Ctor) return new Ctor();\n    return error(\"Unknown node type: \".concat(type));\n  };\n  /**\n   * Apply callback for each selection(enter, update, exit)\n   * and merge them into one selection.\n   */\n  Selection.prototype.join = function (enter, update, exit, merge, split) {\n    if (enter === void 0) {\n      enter = function (d) {\n        return d;\n      };\n    }\n    if (update === void 0) {\n      update = function (d) {\n        return d;\n      };\n    }\n    if (exit === void 0) {\n      exit = function (d) {\n        return d.remove();\n      };\n    }\n    if (merge === void 0) {\n      merge = function (d) {\n        return d;\n      };\n    }\n    if (split === void 0) {\n      split = function (d) {\n        return d.remove();\n      };\n    }\n    var newEnter = enter(this._enter);\n    var newUpdate = update(this._update);\n    var newExit = exit(this._exit);\n    var newMerge = merge(this._merge);\n    var newSplit = split(this._split);\n    return newUpdate.merge(newEnter).merge(newExit).merge(newMerge).merge(newSplit);\n  };\n  Selection.prototype.remove = function () {\n    var _loop_1 = function (i) {\n      var element = this_1._elements[i];\n      var transition = this_1._transitions[i];\n      if (transition) {\n        transition.then(function () {\n          return element.remove();\n        });\n      } else {\n        element.remove();\n      }\n    };\n    var this_1 = this;\n    // Remove node immediately if there is no transition,\n    // otherwise wait until transition finished.\n    for (var i = 0; i < this._elements.length; i++) {\n      _loop_1(i);\n    }\n    return new _a([], null, this._parent, this._document, undefined, this._transitions);\n  };\n  Selection.prototype.each = function (callback) {\n    for (var i = 0; i < this._elements.length; i++) {\n      var element = this._elements[i];\n      var datum = element.__data__;\n      callback.call(element, datum, i);\n    }\n    return this;\n  };\n  Selection.prototype.attr = function (key, value) {\n    var callback = typeof value !== 'function' ? function () {\n      return value;\n    } : value;\n    return this.each(function (d, i) {\n      if (value !== undefined) this[key] = callback.call(this, d, i);\n    });\n  };\n  Selection.prototype.style = function (key, value, callable) {\n    if (callable === void 0) {\n      callable = true;\n    }\n    var callback = typeof value !== 'function' || !callable ? function () {\n      return value;\n    } : value;\n    return this.each(function (d, i) {\n      if (value !== undefined) this.style[key] = callback.call(this, d, i);\n    });\n  };\n  Selection.prototype.styles = function (style, callable) {\n    if (style === void 0) {\n      style = {};\n    }\n    if (callable === void 0) {\n      callable = true;\n    }\n    return this.each(function (d, i) {\n      var _this = this;\n      Object.entries(style).forEach(function (_b) {\n        var _c = __read(_b, 2),\n          key = _c[0],\n          value = _c[1];\n        var callback = typeof value !== 'function' || !callable ? function () {\n          return value;\n        } : value;\n        if (value !== undefined) _this.attr(key, callback.call(_this, d, i));\n      });\n    });\n  };\n  Selection.prototype.update = function (option, callable) {\n    if (callable === void 0) {\n      callable = true;\n    }\n    var callback = typeof option !== 'function' || !callable ? function () {\n      return option;\n    } : option;\n    return this.each(function (d, i) {\n      if (option && this.update) this.update(callback.call(this, d, i));\n    });\n  };\n  /** if current stage is maybeAppend, skip update stage */\n  Selection.prototype.maybeUpdate = function (option, callable) {\n    if (callable === void 0) {\n      callable = true;\n    }\n    var callback = typeof option !== 'function' || !callable ? function () {\n      return option;\n    } : option;\n    return this.each(function (d, i) {\n      if (option && this.update) this.update(callback.call(this, d, i));\n    });\n  };\n  Selection.prototype.transition = function (callback) {\n    var T = this._transitions;\n    var newTransitions = new Array(this._elements.length);\n    this.each(function (d, i) {\n      newTransitions[i] = callback.call(this, d, i);\n    });\n    this._transitions = flatten(newTransitions);\n    return this;\n  };\n  Selection.prototype.on = function (event, handler) {\n    this.each(function () {\n      this.addEventListener(event, handler);\n    });\n    return this;\n  };\n  Selection.prototype.call = function (callback) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    callback.call.apply(callback, __spreadArray([this._parent, this], __read(args), false));\n    return this;\n  };\n  Selection.prototype.node = function () {\n    return this._elements[0];\n  };\n  Selection.prototype.nodes = function () {\n    return this._elements;\n  };\n  Selection.prototype.transitions = function () {\n    return this._transitions.filter(function (t) {\n      return !!t;\n    });\n  };\n  Selection.prototype.parent = function () {\n    return this._parent;\n  };\n  var _Selection_instances, _a, _Selection_maybeAppend;\n  _a = Selection, _Selection_instances = new WeakSet(), _Selection_maybeAppend = function _Selection_maybeAppend(selector, node) {\n    var element = this._elements[0];\n    var child = element.querySelector(selector);\n    if (child) return new _a([child], null, this._parent, this._document);\n    var newChild = typeof node === 'string' ? this.createElement(node) : node();\n    element.appendChild(newChild);\n    return new _a([newChild], null, this._parent, this._document);\n  };\n  Selection.registry = {\n    g: Group,\n    rect: Rect,\n    circle: Circle,\n    path: Path,\n    text: Text,\n    ellipse: Ellipse,\n    image: Image,\n    line: Line,\n    polygon: Polygon,\n    polyline: Polyline,\n    html: HTML\n  };\n  return Selection;\n}();\nexport { Selection };\nexport function select(node) {\n  return new Selection([node], null, node, node.ownerDocument);\n}\nexport function maybeAppend(parent, selector, node) {\n  if (!parent.querySelector(selector)) {\n    return select(parent).append(node);\n  }\n  return select(parent).select(selector);\n}", "map": {"version": 3, "names": ["flatten", "Circle", "Ellipse", "Group", "HTML", "Image", "Line", "Path", "Polygon", "Polyline", "Rect", "Text", "group", "error", "msg", "Error", "Selection", "elements", "data", "parent", "document", "selections", "transitions", "updateElements", "_elements", "Array", "from", "_data", "_parent", "_document", "_enter", "_update", "_exit", "_merge", "_split", "_transitions", "_facetElements", "prototype", "selectAll", "selector", "querySelectorAll", "_a", "selectFacetAll", "undefined", "select", "element", "append", "node", "_this", "callback", "createElement", "i", "length", "d", "_b", "__read", "isArray", "datum", "newElement", "__data__", "__fromElements__", "append<PERSON><PERSON><PERSON>", "push", "maybe<PERSON><PERSON>nd", "id", "__classPrivateFieldGet", "_Selection_instances", "_Selection_maybeAppend", "call", "concat", "attr", "maybeAppendByClassName", "className", "cls", "toString", "maybeAppendByName", "name", "groupId", "enter", "update", "exit", "Set", "merge", "split", "keyElement", "Map", "map", "keyUpdateElement", "groupKeyElements", "key", "groupKey", "has", "get", "__facet__", "delete", "group_2", "group_1", "e_1", "__values", "group_1_1", "next", "done", "value", "__toData__", "add", "S", "other", "__spread<PERSON><PERSON>y", "type", "Ctor", "registry", "join", "remove", "newEnter", "newUpdate", "newExit", "newMerge", "newSplit", "this_1", "transition", "then", "each", "style", "callable", "styles", "Object", "entries", "for<PERSON>ach", "_c", "option", "maybeUpdate", "T", "newTransitions", "on", "event", "handler", "addEventListener", "args", "_i", "arguments", "apply", "nodes", "filter", "t", "child", "querySelector", "<PERSON><PERSON><PERSON><PERSON>", "g", "rect", "circle", "path", "text", "ellipse", "image", "line", "polygon", "polyline", "html", "ownerDocument"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\component\\src\\util\\selection.ts"], "sourcesContent": ["// @ts-nocheck\nimport type { IAnimation, IDocument } from '@antv/g';\nimport { flatten } from '@antv/util';\nimport type { AnimationResult } from '../animation';\nimport type { BaseStyleProps as BP, DisplayObject } from '../shapes';\nimport { Circle, Ellipse, Group, HTML, Image, Line, Path, Polygon, Polyline, Rect, Text } from '../shapes';\nimport { group } from './group';\n\nexport type _Element = DisplayObject & {\n  // Data for this element.\n  __data__?: any;\n  // An Array of data to be splitted to.\n  __toData__?: any[];\n  // An Array of elements to be merged from.\n  __fromElements__?: DisplayObject[];\n  // Whether to update parent if it in update selection.\n  __facet__?: boolean;\n};\n\nfunction error(msg: string) {\n  throw new Error(msg);\n}\n\n/**\n * A simple implementation of d3-selection for @antv/g.\n * It has the core features of d3-selection and extended ability.\n * Every methods of selection returns new selection if elements\n * are mutated(e.g. append, remove), otherwise return the selection itself(e.g. attr, style).\n * @see https://github.com/d3/d3-selection\n * @see https://github.com/antvis/g\n * @todo Nested selections.\n * @todo More useful functor.\n */\nexport class Selection<T = any> {\n  static registry: Record<string, new () => _Element> = {\n    g: Group,\n    rect: Rect,\n    circle: Circle,\n    path: Path,\n    text: Text,\n    ellipse: Ellipse,\n    image: Image,\n    line: Line,\n    polygon: Polygon,\n    polyline: Polyline,\n    html: HTML,\n  };\n\n  private _elements: _Element[];\n\n  private _parent: _Element;\n\n  private _data: T[] | [T, _Element[]][];\n\n  private _enter: Selection;\n\n  private _exit: Selection;\n\n  private _update: Selection;\n\n  private _merge: Selection;\n\n  private _split: Selection;\n\n  private _document: IDocument;\n\n  private _transitions: AnimationResult[];\n\n  private _facetElements: _Element[];\n\n  constructor(\n    elements: Iterable<_Element> = null,\n    data: T[] | [T, _Element[]][] = null,\n    parent: _Element = null,\n    document: IDocument | null = null,\n    selections: [Selection, Selection, Selection, Selection, Selection] = [null, null, null, null, null],\n    transitions: Promise<void>[] = [],\n    updateElements: _Element[] = []\n  ) {\n    this._elements = Array.from(elements);\n    this._data = data;\n    this._parent = parent;\n    this._document = document;\n    this._enter = selections[0];\n    this._update = selections[1];\n    this._exit = selections[2];\n    this._merge = selections[3];\n    this._split = selections[4];\n    this._transitions = transitions;\n    this._facetElements = updateElements;\n  }\n\n  selectAll(selector: string | _Element[]): Selection<T> {\n    const elements = typeof selector === 'string' ? this._parent.querySelectorAll<_Element>(selector) : selector;\n    return new Selection<T>(elements, null, this._elements[0], this._document);\n  }\n\n  selectFacetAll(selector: string | _Element[]): Selection<T> {\n    const elements = typeof selector === 'string' ? this._parent.querySelectorAll<_Element>(selector) : selector;\n    return new Selection<T>(this._elements, null, this._parent, this._document, undefined, undefined, elements);\n  }\n\n  /**\n   * @todo Replace with querySelector which has bug now.\n   */\n  select(selector: string | _Element): Selection<T> {\n    const element =\n      typeof selector === 'string' ? this._parent.querySelectorAll<_Element>(selector)[0] || null : selector;\n    return new Selection<T>([element], null, element, this._document);\n  }\n\n  append(node: string | ((data: T, i: number) => _Element)): Selection<T> {\n    const callback = typeof node === 'function' ? node : () => this.createElement(node);\n\n    const elements = [];\n    if (this._data !== null) {\n      // For empty selection, append new element to parent.\n      // Each element is bind with datum.\n      for (let i = 0; i < this._data.length; i++) {\n        const d = this._data[i];\n        const [datum, from] = Array.isArray(d) ? d : [d, null];\n        const newElement = callback(datum, i);\n        newElement.__data__ = datum;\n        if (from !== null) newElement.__fromElements__ = from;\n        this._parent.appendChild(newElement);\n        elements.push(newElement);\n      }\n      return new Selection(elements, null, this._parent, this._document);\n    }\n    // For non-empty selection, append new element to\n    // selected element and return new selection.\n    for (let i = 0; i < this._elements.length; i++) {\n      const element = this._elements[i];\n      const datum = element.__data__;\n      const newElement = callback(datum, i);\n      element.appendChild(newElement);\n      elements.push(newElement);\n    }\n    return new Selection(elements, null, elements[0], this._document);\n  }\n\n  #maybeAppend<T extends DisplayObject>(selector: string, node: string | (() => _Element)): Selection<T> {\n    const element = this._elements[0];\n    const child = element.querySelector<_Element>(selector);\n    if (child) return new Selection([child], null, this._parent, this._document);\n    const newChild = typeof node === 'string' ? this.createElement(node) : node();\n    element.appendChild(newChild);\n    return new Selection([newChild], null, this._parent, this._document);\n  }\n\n  maybeAppend<T = DisplayObject>(id: string, node: string | (() => _Element)) {\n    const element = this.#maybeAppend<T>(id[0] === '#' ? id : `#${id}`, node);\n    element.attr('id', id);\n    return element;\n  }\n\n  maybeAppendByClassName<T = DisplayObject>(className: any, node: string | (() => _Element)) {\n    const cls: string = className.toString();\n    const element = this.#maybeAppend<T>(cls[0] === '.' ? cls : `.${cls}`, node);\n    element.attr('className', cls);\n    return element;\n  }\n\n  maybeAppendByName<T = DisplayObject>(name: string, node: string | (() => _Element)) {\n    const element = this.#maybeAppend<T>(`[name=\"${name}\"]`, node);\n    element.attr('name', name);\n    return element;\n  }\n\n  /**\n   * Bind data to elements, and produce three selection:\n   * Enter: Selection with empty elements and data to be bind to elements.\n   * Update: Selection with elements to be updated.\n   * Exit: Selection with elements to be removed.\n   */\n  data<T = any>(\n    data: T[],\n    id: (d: T, index?: number) => any = (d) => d,\n    groupId: (d: T, index?: number) => any = () => null\n  ): Selection<T> {\n    // An Array of new data.\n    const enter: T[] = [];\n\n    // An Array of elements to be updated.\n    const update: _Element[] = [];\n\n    // A Set of elements to be removed.\n    const exit = new Set<_Element>(this._elements);\n\n    // An Array of data to be merged into one element.\n    const merge: [T, _Element[]][] = [];\n\n    // A Set of elements to be split into multiple datum.\n    const split = new Set<_Element>();\n\n    // A Map from key to each element.\n    const keyElement = new Map<string, _Element>(this._elements.map((d, i) => [id(d.__data__, i), d]));\n\n    // A Map from key to exist element. The Update Selection\n    // can get element from this map, this is for diff among\n    // facets.\n    const keyUpdateElement = new Map<string, _Element>(this._facetElements.map((d, i) => [id(d.__data__, i), d]));\n\n    // A Map from groupKey to a group of elements.\n    const groupKeyElements = group(this._elements, (d) => groupId(d.__data__));\n\n    // Diff data with selection(elements with data).\n    // !!! Note\n    // The switch is strictly ordered, not not change the order of them.\n    for (let i = 0; i < data.length; i++) {\n      const datum = data[i];\n      const key = id(datum, i);\n      const groupKey = groupId(datum, i);\n      // Append element to update selection if incoming data has\n      // exactly the same key with elements.\n      if (keyElement.has(key)) {\n        const element = keyElement.get(key);\n        element.__data__ = datum;\n        element.__facet__ = false;\n        update.push(element);\n        exit.delete(element);\n        keyElement.delete(key);\n        // Append element to update selection if incoming data has\n        // exactly the same key with updateElements.\n      } else if (keyUpdateElement.has(key)) {\n        const element = keyUpdateElement.get(key);\n        element.__data__ = datum;\n        // Flag this element should update its parentNode.\n        element.__facet__ = true;\n        update.push(element);\n        keyUpdateElement.delete(key);\n        // Append datum to merge selection if existed elements has\n        // its key as groupKey.\n      } else if (groupKeyElements.has(key)) {\n        const group = groupKeyElements.get(key);\n        merge.push([datum, group]);\n        for (const element of group) exit.delete(element);\n        groupKeyElements.delete(key);\n        // Append element to split selection if incoming data has\n        // groupKey as its key, and bind to datum for it.\n      } else if (keyElement.has(groupKey)) {\n        const element = keyElement.get(groupKey);\n        if (element.__toData__) element.__toData__.push(datum);\n        else element.__toData__ = [datum];\n        split.add(element);\n        exit.delete(element);\n      } else {\n        enter.push(datum);\n      }\n    }\n\n    // Create new selection with enter, update and exit.\n    const S: [Selection<T>, Selection<T>, Selection<T>, Selection<T>, Selection<T>] = [\n      new Selection<T>([], enter, this._parent, this._document),\n      new Selection<T>(update, null, this._parent, this._document),\n      new Selection<T>(exit, null, this._parent, this._document),\n      new Selection<T>([], merge, this._parent, this._document),\n      new Selection<T>(split, null, this._parent, this._document),\n    ];\n\n    return new Selection<T>(this._elements, null, this._parent, this._document, S);\n  }\n\n  merge(other: Selection<T>): Selection<T> {\n    const elements = [...this._elements, ...other._elements];\n    const transitions = [...this._transitions, ...other._transitions];\n    return new Selection<T>(elements, null, this._parent, this._document, undefined, transitions);\n  }\n\n  createElement(type: string): _Element {\n    if (this._document) {\n      return this._document.createElement<_Element, BP>(type, {});\n    }\n    const Ctor = Selection.registry[type];\n    if (Ctor) return new Ctor();\n    return error(`Unknown node type: ${type}`);\n  }\n\n  /**\n   * Apply callback for each selection(enter, update, exit)\n   * and merge them into one selection.\n   */\n  join(\n    enter: (selection: Selection<T>) => any = (d) => d,\n    update: (selection: Selection<T>) => any = (d) => d,\n    exit: (selection: Selection<T>) => any = (d) => d.remove(),\n    merge: (selection: Selection<T>) => any = (d) => d,\n    split: (selection: Selection<T>) => any = (d) => d.remove()\n  ): Selection<T> {\n    const newEnter = enter(this._enter);\n    const newUpdate = update(this._update);\n    const newExit = exit(this._exit);\n    const newMerge = merge(this._merge);\n    const newSplit = split(this._split);\n    return newUpdate.merge(newEnter).merge(newExit).merge(newMerge).merge(newSplit);\n  }\n\n  remove(): Selection<T> {\n    // Remove node immediately if there is no transition,\n    // otherwise wait until transition finished.\n    for (let i = 0; i < this._elements.length; i++) {\n      const element = this._elements[i];\n      const transition = this._transitions[i];\n      if (transition) {\n        transition.then(() => element.remove());\n      } else {\n        element.remove();\n      }\n    }\n    return new Selection<T>([], null, this._parent, this._document, undefined, this._transitions);\n  }\n\n  each(callback: (datum: T, index: number) => any): Selection<T> {\n    for (let i = 0; i < this._elements.length; i++) {\n      const element = this._elements[i];\n      const datum = element.__data__;\n      callback.call(element, datum, i);\n    }\n    return this;\n  }\n\n  attr(key: string, value: any): Selection<T> {\n    const callback = typeof value !== 'function' ? () => value : value;\n    return this.each(function (d, i) {\n      if (value !== undefined) this[key] = callback.call(this, d, i);\n    });\n  }\n\n  style(key: string, value: any, callable: boolean = true): Selection<T> {\n    const callback = typeof value !== 'function' || !callable ? () => value : value;\n    return this.each(function (d, i) {\n      if (value !== undefined) this.style[key] = callback.call(this, d, i);\n    });\n  }\n\n  styles(style: Record<string, any> = {}, callable: boolean = true): Selection<T> {\n    return this.each(function (d, i) {\n      Object.entries(style).forEach(([key, value]) => {\n        const callback = typeof value !== 'function' || !callable ? () => value : value;\n        if (value !== undefined) this.attr(key, callback.call(this, d, i));\n      });\n    });\n  }\n\n  update(option: any, callable: boolean = true): Selection<T> {\n    const callback = typeof option !== 'function' || !callable ? () => option : option;\n    return this.each(function (d, i) {\n      if (option && this.update) this.update(callback.call(this, d, i));\n    });\n  }\n\n  /** if current stage is maybeAppend, skip update stage */\n  maybeUpdate(option: any, callable: boolean = true): Selection<T> {\n    const callback = typeof option !== 'function' || !callable ? () => option : option;\n    return this.each(function (d, i) {\n      if (option && this.update) this.update(callback.call(this, d, i));\n    });\n  }\n\n  transition(callback?: (datum: T, index: number) => AnimationResult | AnimationResult[]): Selection<T> {\n    const { _transitions: T } = this;\n    const newTransitions = new Array<ReturnType<Exclude<typeof callback, undefined>>>(this._elements.length);\n    this.each(function (d, i) {\n      newTransitions[i] = callback.call(this, d, i);\n    });\n    this._transitions = flatten(newTransitions);\n    return this;\n  }\n\n  on(event: string, handler: any) {\n    this.each(function () {\n      this.addEventListener(event, handler);\n    });\n    return this;\n  }\n\n  call(callback: (selection: Selection<T>, ...args: any[]) => any, ...args: any[]): Selection<T> {\n    callback.call(this._parent, this, ...args);\n    return this;\n  }\n\n  node<T extends _Element = _Element>(): T {\n    return this._elements[0];\n  }\n\n  nodes(): _Element[] {\n    return this._elements;\n  }\n\n  transitions() {\n    return this._transitions.filter((t) => !!t);\n  }\n\n  parent(): DisplayObject {\n    return this._parent;\n  }\n}\n\nexport function select<T = any>(node: DisplayObject) {\n  return new Selection<T>([node], null, node, node.ownerDocument);\n}\n\nexport function maybeAppend<T>(\n  parent: Group,\n  selector: string,\n  node: string | ((data: T, i: number) => _Element)\n): Selection<T> {\n  if (!parent.querySelector(selector)) {\n    return select(parent).append(node);\n  }\n  return select(parent).select(selector);\n}\n"], "mappings": ";AAEA,SAASA,OAAO,QAAQ,YAAY;AAGpC,SAASC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,QAAQ,WAAW;AAC1G,SAASC,KAAK,QAAQ,SAAS;AAa/B,SAASC,KAAKA,CAACC,GAAW;EACxB,MAAM,IAAIC,KAAK,CAACD,GAAG,CAAC;AACtB;AAEA;;;;;;;;;;AAUA,IAAAE,SAAA;EAqCE,SAAAA,UACEC,QAAmC,EACnCC,IAAoC,EACpCC,MAAuB,EACvBC,QAAiC,EACjCC,UAAoG,EACpGC,WAAiC,EACjCC,cAA+B;IAN/B,IAAAN,QAAA;MAAAA,QAAA,OAAmC;IAAA;IACnC,IAAAC,IAAA;MAAAA,IAAA,OAAoC;IAAA;IACpC,IAAAC,MAAA;MAAAA,MAAA,OAAuB;IAAA;IACvB,IAAAC,QAAA;MAAAA,QAAA,OAAiC;IAAA;IACjC,IAAAC,UAAA;MAAAA,UAAA,IAAuE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAAA;IACpG,IAAAC,WAAA;MAAAA,WAAA,KAAiC;IAAA;IACjC,IAAAC,cAAA;MAAAA,cAAA,KAA+B;IAAA;;IAE/B,IAAI,CAACC,SAAS,GAAGC,KAAK,CAACC,IAAI,CAACT,QAAQ,CAAC;IACrC,IAAI,CAACU,KAAK,GAAGT,IAAI;IACjB,IAAI,CAACU,OAAO,GAAGT,MAAM;IACrB,IAAI,CAACU,SAAS,GAAGT,QAAQ;IACzB,IAAI,CAACU,MAAM,GAAGT,UAAU,CAAC,CAAC,CAAC;IAC3B,IAAI,CAACU,OAAO,GAAGV,UAAU,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACW,KAAK,GAAGX,UAAU,CAAC,CAAC,CAAC;IAC1B,IAAI,CAACY,MAAM,GAAGZ,UAAU,CAAC,CAAC,CAAC;IAC3B,IAAI,CAACa,MAAM,GAAGb,UAAU,CAAC,CAAC,CAAC;IAC3B,IAAI,CAACc,YAAY,GAAGb,WAAW;IAC/B,IAAI,CAACc,cAAc,GAAGb,cAAc;EACtC;EAEAP,SAAA,CAAAqB,SAAA,CAAAC,SAAS,GAAT,UAAUC,QAA6B;IACrC,IAAMtB,QAAQ,GAAG,OAAOsB,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAACX,OAAO,CAACY,gBAAgB,CAAWD,QAAQ,CAAC,GAAGA,QAAQ;IAC5G,OAAO,IAAIE,EAAS,CAAIxB,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAACK,SAAS,CAAC;EAC5E,CAAC;EAEDb,SAAA,CAAAqB,SAAA,CAAAK,cAAc,GAAd,UAAeH,QAA6B;IAC1C,IAAMtB,QAAQ,GAAG,OAAOsB,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAACX,OAAO,CAACY,gBAAgB,CAAWD,QAAQ,CAAC,GAAGA,QAAQ;IAC5G,OAAO,IAAIE,EAAS,CAAI,IAAI,CAACjB,SAAS,EAAE,IAAI,EAAE,IAAI,CAACI,OAAO,EAAE,IAAI,CAACC,SAAS,EAAEc,SAAS,EAAEA,SAAS,EAAE1B,QAAQ,CAAC;EAC7G,CAAC;EAED;;;EAGAD,SAAA,CAAAqB,SAAA,CAAAO,MAAM,GAAN,UAAOL,QAA2B;IAChC,IAAMM,OAAO,GACX,OAAON,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAACX,OAAO,CAACY,gBAAgB,CAAWD,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,GAAGA,QAAQ;IACxG,OAAO,IAAIE,EAAS,CAAI,CAACI,OAAO,CAAC,EAAE,IAAI,EAAEA,OAAO,EAAE,IAAI,CAAChB,SAAS,CAAC;EACnE,CAAC;EAEDb,SAAA,CAAAqB,SAAA,CAAAS,MAAM,GAAN,UAAOC,IAAiD;IAAxD,IAAAC,KAAA;IACE,IAAMC,QAAQ,GAAG,OAAOF,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAG;MAAM,OAAAC,KAAI,CAACE,aAAa,CAACH,IAAI,CAAC;IAAxB,CAAwB;IAEnF,IAAM9B,QAAQ,GAAG,EAAE;IACnB,IAAI,IAAI,CAACU,KAAK,KAAK,IAAI,EAAE;MACvB;MACA;MACA,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACxB,KAAK,CAACyB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC1C,IAAME,CAAC,GAAG,IAAI,CAAC1B,KAAK,CAACwB,CAAC,CAAC;QACjB,IAAAG,EAAA,GAAAC,MAAA,CAAgB9B,KAAK,CAAC+B,OAAO,CAACH,CAAC,CAAC,GAAGA,CAAC,GAAG,CAACA,CAAC,EAAE,IAAI,CAAC;UAA/CI,KAAK,GAAAH,EAAA;UAAE5B,IAAI,GAAA4B,EAAA,GAAoC;QACtD,IAAMI,UAAU,GAAGT,QAAQ,CAACQ,KAAK,EAAEN,CAAC,CAAC;QACrCO,UAAU,CAACC,QAAQ,GAAGF,KAAK;QAC3B,IAAI/B,IAAI,KAAK,IAAI,EAAEgC,UAAU,CAACE,gBAAgB,GAAGlC,IAAI;QACrD,IAAI,CAACE,OAAO,CAACiC,WAAW,CAACH,UAAU,CAAC;QACpCzC,QAAQ,CAAC6C,IAAI,CAACJ,UAAU,CAAC;MAC3B;MACA,OAAO,IAAIjB,EAAS,CAACxB,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACW,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC;IACpE;IACA;IACA;IACA,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3B,SAAS,CAAC4B,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9C,IAAMN,OAAO,GAAG,IAAI,CAACrB,SAAS,CAAC2B,CAAC,CAAC;MACjC,IAAMM,KAAK,GAAGZ,OAAO,CAACc,QAAQ;MAC9B,IAAMD,UAAU,GAAGT,QAAQ,CAACQ,KAAK,EAAEN,CAAC,CAAC;MACrCN,OAAO,CAACgB,WAAW,CAACH,UAAU,CAAC;MAC/BzC,QAAQ,CAAC6C,IAAI,CAACJ,UAAU,CAAC;IAC3B;IACA,OAAO,IAAIjB,EAAS,CAACxB,QAAQ,EAAE,IAAI,EAAEA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACY,SAAS,CAAC;EACnE,CAAC;EAWDb,SAAA,CAAAqB,SAAA,CAAA0B,WAAW,GAAX,UAA+BC,EAAU,EAAEjB,IAA+B;IACxE,IAAMF,OAAO,GAAGoB,sBAAA,KAAI,EAAAC,oBAAA,OAAAC,sBAAA,CAAa,CAAAC,IAAA,CAAjB,IAAI,EAAiBJ,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGA,EAAE,GAAG,IAAAK,MAAA,CAAIL,EAAE,CAAE,EAAEjB,IAAI,CAAC;IACzEF,OAAO,CAACyB,IAAI,CAAC,IAAI,EAAEN,EAAE,CAAC;IACtB,OAAOnB,OAAO;EAChB,CAAC;EAED7B,SAAA,CAAAqB,SAAA,CAAAkC,sBAAsB,GAAtB,UAA0CC,SAAc,EAAEzB,IAA+B;IACvF,IAAM0B,GAAG,GAAWD,SAAS,CAACE,QAAQ,EAAE;IACxC,IAAM7B,OAAO,GAAGoB,sBAAA,KAAI,EAAAC,oBAAA,OAAAC,sBAAA,CAAa,CAAAC,IAAA,CAAjB,IAAI,EAAiBK,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGA,GAAG,GAAG,IAAAJ,MAAA,CAAII,GAAG,CAAE,EAAE1B,IAAI,CAAC;IAC5EF,OAAO,CAACyB,IAAI,CAAC,WAAW,EAAEG,GAAG,CAAC;IAC9B,OAAO5B,OAAO;EAChB,CAAC;EAED7B,SAAA,CAAAqB,SAAA,CAAAsC,iBAAiB,GAAjB,UAAqCC,IAAY,EAAE7B,IAA+B;IAChF,IAAMF,OAAO,GAAGoB,sBAAA,KAAI,EAAAC,oBAAA,OAAAC,sBAAA,CAAa,CAAAC,IAAA,CAAjB,IAAI,EAAiB,WAAAC,MAAA,CAAUO,IAAI,QAAI,EAAE7B,IAAI,CAAC;IAC9DF,OAAO,CAACyB,IAAI,CAAC,MAAM,EAAEM,IAAI,CAAC;IAC1B,OAAO/B,OAAO;EAChB,CAAC;EAED;;;;;;EAMA7B,SAAA,CAAAqB,SAAA,CAAAnB,IAAI,GAAJ,UACEA,IAAS,EACT8C,EAA4C,EAC5Ca,OAAmD;;IADnD,IAAAb,EAAA;MAAAA,EAAA,YAAAA,CAAqCX,CAAC;QAAK,OAAAA,CAAC;MAAD,CAAC;IAAA;IAC5C,IAAAwB,OAAA;MAAAA,OAAA,YAAAA,CAAA;QAA+C,WAAI;MAAJ,CAAI;IAAA;IAEnD;IACA,IAAMC,KAAK,GAAQ,EAAE;IAErB;IACA,IAAMC,MAAM,GAAe,EAAE;IAE7B;IACA,IAAMC,IAAI,GAAG,IAAIC,GAAG,CAAW,IAAI,CAACzD,SAAS,CAAC;IAE9C;IACA,IAAM0D,KAAK,GAAsB,EAAE;IAEnC;IACA,IAAMC,KAAK,GAAG,IAAIF,GAAG,EAAY;IAEjC;IACA,IAAMG,UAAU,GAAG,IAAIC,GAAG,CAAmB,IAAI,CAAC7D,SAAS,CAAC8D,GAAG,CAAC,UAACjC,CAAC,EAAEF,CAAC;MAAK,QAACa,EAAE,CAACX,CAAC,CAACM,QAAQ,EAAER,CAAC,CAAC,EAAEE,CAAC,CAAC;IAAtB,CAAsB,CAAC,CAAC;IAElG;IACA;IACA;IACA,IAAMkC,gBAAgB,GAAG,IAAIF,GAAG,CAAmB,IAAI,CAACjD,cAAc,CAACkD,GAAG,CAAC,UAACjC,CAAC,EAAEF,CAAC;MAAK,QAACa,EAAE,CAACX,CAAC,CAACM,QAAQ,EAAER,CAAC,CAAC,EAAEE,CAAC,CAAC;IAAtB,CAAsB,CAAC,CAAC;IAE7G;IACA,IAAMmC,gBAAgB,GAAG5E,KAAK,CAAC,IAAI,CAACY,SAAS,EAAE,UAAC6B,CAAC;MAAK,OAAAwB,OAAO,CAACxB,CAAC,CAACM,QAAQ,CAAC;IAAnB,CAAmB,CAAC;IAE1E;IACA;IACA;IACA,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjC,IAAI,CAACkC,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,IAAMM,KAAK,GAAGvC,IAAI,CAACiC,CAAC,CAAC;MACrB,IAAMsC,GAAG,GAAGzB,EAAE,CAACP,KAAK,EAAEN,CAAC,CAAC;MACxB,IAAMuC,QAAQ,GAAGb,OAAO,CAACpB,KAAK,EAAEN,CAAC,CAAC;MAClC;MACA;MACA,IAAIiC,UAAU,CAACO,GAAG,CAACF,GAAG,CAAC,EAAE;QACvB,IAAM5C,OAAO,GAAGuC,UAAU,CAACQ,GAAG,CAACH,GAAG,CAAC;QACnC5C,OAAO,CAACc,QAAQ,GAAGF,KAAK;QACxBZ,OAAO,CAACgD,SAAS,GAAG,KAAK;QACzBd,MAAM,CAACjB,IAAI,CAACjB,OAAO,CAAC;QACpBmC,IAAI,CAACc,MAAM,CAACjD,OAAO,CAAC;QACpBuC,UAAU,CAACU,MAAM,CAACL,GAAG,CAAC;QACtB;QACA;MACF,CAAC,MAAM,IAAIF,gBAAgB,CAACI,GAAG,CAACF,GAAG,CAAC,EAAE;QACpC,IAAM5C,OAAO,GAAG0C,gBAAgB,CAACK,GAAG,CAACH,GAAG,CAAC;QACzC5C,OAAO,CAACc,QAAQ,GAAGF,KAAK;QACxB;QACAZ,OAAO,CAACgD,SAAS,GAAG,IAAI;QACxBd,MAAM,CAACjB,IAAI,CAACjB,OAAO,CAAC;QACpB0C,gBAAgB,CAACO,MAAM,CAACL,GAAG,CAAC;QAC5B;QACA;MACF,CAAC,MAAM,IAAID,gBAAgB,CAACG,GAAG,CAACF,GAAG,CAAC,EAAE;QACpC,IAAMM,OAAK,GAAGP,gBAAgB,CAACI,GAAG,CAACH,GAAG,CAAC;QACvCP,KAAK,CAACpB,IAAI,CAAC,CAACL,KAAK,EAAEsC,OAAK,CAAC,CAAC;;UAC1B,KAAsB,IAAAC,OAAA,IAAAC,GAAA,WAAAC,QAAA,CAAAH,OAAK,IAAAI,SAAA,GAAAH,OAAA,CAAAI,IAAA,KAAAD,SAAA,CAAAE,IAAA,EAAAF,SAAA,GAAAH,OAAA,CAAAI,IAAA;YAAtB,IAAMvD,OAAO,GAAAsD,SAAA,CAAAG,KAAA;YAAWtB,IAAI,CAACc,MAAM,CAACjD,OAAO,CAAC;;;;;;;;;;;;;QACjD2C,gBAAgB,CAACM,MAAM,CAACL,GAAG,CAAC;QAC5B;QACA;MACF,CAAC,MAAM,IAAIL,UAAU,CAACO,GAAG,CAACD,QAAQ,CAAC,EAAE;QACnC,IAAM7C,OAAO,GAAGuC,UAAU,CAACQ,GAAG,CAACF,QAAQ,CAAC;QACxC,IAAI7C,OAAO,CAAC0D,UAAU,EAAE1D,OAAO,CAAC0D,UAAU,CAACzC,IAAI,CAACL,KAAK,CAAC,CAAC,KAClDZ,OAAO,CAAC0D,UAAU,GAAG,CAAC9C,KAAK,CAAC;QACjC0B,KAAK,CAACqB,GAAG,CAAC3D,OAAO,CAAC;QAClBmC,IAAI,CAACc,MAAM,CAACjD,OAAO,CAAC;MACtB,CAAC,MAAM;QACLiC,KAAK,CAAChB,IAAI,CAACL,KAAK,CAAC;MACnB;IACF;IAEA;IACA,IAAMgD,CAAC,GAA2E,CAChF,IAAIhE,EAAS,CAAI,EAAE,EAAEqC,KAAK,EAAE,IAAI,CAAClD,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC,EACzD,IAAIY,EAAS,CAAIsC,MAAM,EAAE,IAAI,EAAE,IAAI,CAACnD,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC,EAC5D,IAAIY,EAAS,CAAIuC,IAAI,EAAE,IAAI,EAAE,IAAI,CAACpD,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC,EAC1D,IAAIY,EAAS,CAAI,EAAE,EAAEyC,KAAK,EAAE,IAAI,CAACtD,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC,EACzD,IAAIY,EAAS,CAAI0C,KAAK,EAAE,IAAI,EAAE,IAAI,CAACvD,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC,CAC5D;IAED,OAAO,IAAIY,EAAS,CAAI,IAAI,CAACjB,SAAS,EAAE,IAAI,EAAE,IAAI,CAACI,OAAO,EAAE,IAAI,CAACC,SAAS,EAAE4E,CAAC,CAAC;EAChF,CAAC;EAEDzF,SAAA,CAAAqB,SAAA,CAAA6C,KAAK,GAAL,UAAMwB,KAAmB;IACvB,IAAMzF,QAAQ,GAAA0F,aAAA,CAAAA,aAAA,KAAApD,MAAA,CAAO,IAAI,CAAC/B,SAAS,WAAA+B,MAAA,CAAKmD,KAAK,CAAClF,SAAS,SAAC;IACxD,IAAMF,WAAW,GAAAqF,aAAA,CAAAA,aAAA,KAAApD,MAAA,CAAO,IAAI,CAACpB,YAAY,WAAAoB,MAAA,CAAKmD,KAAK,CAACvE,YAAY,SAAC;IACjE,OAAO,IAAIM,EAAS,CAAIxB,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACW,OAAO,EAAE,IAAI,CAACC,SAAS,EAAEc,SAAS,EAAErB,WAAW,CAAC;EAC/F,CAAC;EAEDN,SAAA,CAAAqB,SAAA,CAAAa,aAAa,GAAb,UAAc0D,IAAY;IACxB,IAAI,IAAI,CAAC/E,SAAS,EAAE;MAClB,OAAO,IAAI,CAACA,SAAS,CAACqB,aAAa,CAAe0D,IAAI,EAAE,EAAE,CAAC;IAC7D;IACA,IAAMC,IAAI,GAAGpE,EAAS,CAACqE,QAAQ,CAACF,IAAI,CAAC;IACrC,IAAIC,IAAI,EAAE,OAAO,IAAIA,IAAI,EAAE;IAC3B,OAAOhG,KAAK,CAAC,sBAAAwD,MAAA,CAAsBuC,IAAI,CAAE,CAAC;EAC5C,CAAC;EAED;;;;EAIA5F,SAAA,CAAAqB,SAAA,CAAA0E,IAAI,GAAJ,UACEjC,KAAkD,EAClDC,MAAmD,EACnDC,IAA0D,EAC1DE,KAAkD,EAClDC,KAA2D;IAJ3D,IAAAL,KAAA;MAAAA,KAAA,YAAAA,CAA2CzB,CAAC;QAAK,OAAAA,CAAC;MAAD,CAAC;IAAA;IAClD,IAAA0B,MAAA;MAAAA,MAAA,YAAAA,CAA4C1B,CAAC;QAAK,OAAAA,CAAC;MAAD,CAAC;IAAA;IACnD,IAAA2B,IAAA;MAAAA,IAAA,YAAAA,CAA0C3B,CAAC;QAAK,OAAAA,CAAC,CAAC2D,MAAM,EAAE;MAAV,CAAU;IAAA;IAC1D,IAAA9B,KAAA;MAAAA,KAAA,YAAAA,CAA2C7B,CAAC;QAAK,OAAAA,CAAC;MAAD,CAAC;IAAA;IAClD,IAAA8B,KAAA;MAAAA,KAAA,YAAAA,CAA2C9B,CAAC;QAAK,OAAAA,CAAC,CAAC2D,MAAM,EAAE;MAAV,CAAU;IAAA;IAE3D,IAAMC,QAAQ,GAAGnC,KAAK,CAAC,IAAI,CAAChD,MAAM,CAAC;IACnC,IAAMoF,SAAS,GAAGnC,MAAM,CAAC,IAAI,CAAChD,OAAO,CAAC;IACtC,IAAMoF,OAAO,GAAGnC,IAAI,CAAC,IAAI,CAAChD,KAAK,CAAC;IAChC,IAAMoF,QAAQ,GAAGlC,KAAK,CAAC,IAAI,CAACjD,MAAM,CAAC;IACnC,IAAMoF,QAAQ,GAAGlC,KAAK,CAAC,IAAI,CAACjD,MAAM,CAAC;IACnC,OAAOgF,SAAS,CAAChC,KAAK,CAAC+B,QAAQ,CAAC,CAAC/B,KAAK,CAACiC,OAAO,CAAC,CAACjC,KAAK,CAACkC,QAAQ,CAAC,CAAClC,KAAK,CAACmC,QAAQ,CAAC;EACjF,CAAC;EAEDrG,SAAA,CAAAqB,SAAA,CAAA2E,MAAM,GAAN;4BAGW7D,CAAC;MACR,IAAMN,OAAO,GAAGyE,MAAA,CAAK9F,SAAS,CAAC2B,CAAC,CAAC;MACjC,IAAMoE,UAAU,GAAGD,MAAA,CAAKnF,YAAY,CAACgB,CAAC,CAAC;MACvC,IAAIoE,UAAU,EAAE;QACdA,UAAU,CAACC,IAAI,CAAC;UAAM,OAAA3E,OAAO,CAACmE,MAAM,EAAE;QAAhB,CAAgB,CAAC;MACzC,CAAC,MAAM;QACLnE,OAAO,CAACmE,MAAM,EAAE;MAClB;;;IATF;IACA;IACA,KAAK,IAAI7D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3B,SAAS,CAAC4B,MAAM,EAAED,CAAC,EAAE;cAArCA,CAAC;;IASV,OAAO,IAAIV,EAAS,CAAI,EAAE,EAAE,IAAI,EAAE,IAAI,CAACb,OAAO,EAAE,IAAI,CAACC,SAAS,EAAEc,SAAS,EAAE,IAAI,CAACR,YAAY,CAAC;EAC/F,CAAC;EAEDnB,SAAA,CAAAqB,SAAA,CAAAoF,IAAI,GAAJ,UAAKxE,QAA0C;IAC7C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3B,SAAS,CAAC4B,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9C,IAAMN,OAAO,GAAG,IAAI,CAACrB,SAAS,CAAC2B,CAAC,CAAC;MACjC,IAAMM,KAAK,GAAGZ,OAAO,CAACc,QAAQ;MAC9BV,QAAQ,CAACmB,IAAI,CAACvB,OAAO,EAAEY,KAAK,EAAEN,CAAC,CAAC;IAClC;IACA,OAAO,IAAI;EACb,CAAC;EAEDnC,SAAA,CAAAqB,SAAA,CAAAiC,IAAI,GAAJ,UAAKmB,GAAW,EAAEa,KAAU;IAC1B,IAAMrD,QAAQ,GAAG,OAAOqD,KAAK,KAAK,UAAU,GAAG;MAAM,OAAAA,KAAK;IAAL,CAAK,GAAGA,KAAK;IAClE,OAAO,IAAI,CAACmB,IAAI,CAAC,UAAUpE,CAAC,EAAEF,CAAC;MAC7B,IAAImD,KAAK,KAAK3D,SAAS,EAAE,IAAI,CAAC8C,GAAG,CAAC,GAAGxC,QAAQ,CAACmB,IAAI,CAAC,IAAI,EAAEf,CAAC,EAAEF,CAAC,CAAC;IAChE,CAAC,CAAC;EACJ,CAAC;EAEDnC,SAAA,CAAAqB,SAAA,CAAAqF,KAAK,GAAL,UAAMjC,GAAW,EAAEa,KAAU,EAAEqB,QAAwB;IAAxB,IAAAA,QAAA;MAAAA,QAAA,OAAwB;IAAA;IACrD,IAAM1E,QAAQ,GAAG,OAAOqD,KAAK,KAAK,UAAU,IAAI,CAACqB,QAAQ,GAAG;MAAM,OAAArB,KAAK;IAAL,CAAK,GAAGA,KAAK;IAC/E,OAAO,IAAI,CAACmB,IAAI,CAAC,UAAUpE,CAAC,EAAEF,CAAC;MAC7B,IAAImD,KAAK,KAAK3D,SAAS,EAAE,IAAI,CAAC+E,KAAK,CAACjC,GAAG,CAAC,GAAGxC,QAAQ,CAACmB,IAAI,CAAC,IAAI,EAAEf,CAAC,EAAEF,CAAC,CAAC;IACtE,CAAC,CAAC;EACJ,CAAC;EAEDnC,SAAA,CAAAqB,SAAA,CAAAuF,MAAM,GAAN,UAAOF,KAA+B,EAAEC,QAAwB;IAAzD,IAAAD,KAAA;MAAAA,KAAA,KAA+B;IAAA;IAAE,IAAAC,QAAA;MAAAA,QAAA,OAAwB;IAAA;IAC9D,OAAO,IAAI,CAACF,IAAI,CAAC,UAAUpE,CAAC,EAAEF,CAAC;MAAd,IAAAH,KAAA;MACf6E,MAAM,CAACC,OAAO,CAACJ,KAAK,CAAC,CAACK,OAAO,CAAC,UAACzE,EAAY;YAAZ0E,EAAA,GAAAzE,MAAA,CAAAD,EAAA,IAAY;UAAXmC,GAAG,GAAAuC,EAAA;UAAE1B,KAAK,GAAA0B,EAAA;QACxC,IAAM/E,QAAQ,GAAG,OAAOqD,KAAK,KAAK,UAAU,IAAI,CAACqB,QAAQ,GAAG;UAAM,OAAArB,KAAK;QAAL,CAAK,GAAGA,KAAK;QAC/E,IAAIA,KAAK,KAAK3D,SAAS,EAAEK,KAAI,CAACsB,IAAI,CAACmB,GAAG,EAAExC,QAAQ,CAACmB,IAAI,CAACpB,KAAI,EAAEK,CAAC,EAAEF,CAAC,CAAC,CAAC;MACpE,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDnC,SAAA,CAAAqB,SAAA,CAAA0C,MAAM,GAAN,UAAOkD,MAAW,EAAEN,QAAwB;IAAxB,IAAAA,QAAA;MAAAA,QAAA,OAAwB;IAAA;IAC1C,IAAM1E,QAAQ,GAAG,OAAOgF,MAAM,KAAK,UAAU,IAAI,CAACN,QAAQ,GAAG;MAAM,OAAAM,MAAM;IAAN,CAAM,GAAGA,MAAM;IAClF,OAAO,IAAI,CAACR,IAAI,CAAC,UAAUpE,CAAC,EAAEF,CAAC;MAC7B,IAAI8E,MAAM,IAAI,IAAI,CAAClD,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC9B,QAAQ,CAACmB,IAAI,CAAC,IAAI,EAAEf,CAAC,EAAEF,CAAC,CAAC,CAAC;IACnE,CAAC,CAAC;EACJ,CAAC;EAED;EACAnC,SAAA,CAAAqB,SAAA,CAAA6F,WAAW,GAAX,UAAYD,MAAW,EAAEN,QAAwB;IAAxB,IAAAA,QAAA;MAAAA,QAAA,OAAwB;IAAA;IAC/C,IAAM1E,QAAQ,GAAG,OAAOgF,MAAM,KAAK,UAAU,IAAI,CAACN,QAAQ,GAAG;MAAM,OAAAM,MAAM;IAAN,CAAM,GAAGA,MAAM;IAClF,OAAO,IAAI,CAACR,IAAI,CAAC,UAAUpE,CAAC,EAAEF,CAAC;MAC7B,IAAI8E,MAAM,IAAI,IAAI,CAAClD,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC9B,QAAQ,CAACmB,IAAI,CAAC,IAAI,EAAEf,CAAC,EAAEF,CAAC,CAAC,CAAC;IACnE,CAAC,CAAC;EACJ,CAAC;EAEDnC,SAAA,CAAAqB,SAAA,CAAAkF,UAAU,GAAV,UAAWtE,QAA2E;IAC5E,IAAckF,CAAC,GAAK,IAAI,CAAAhG,YAAT;IACvB,IAAMiG,cAAc,GAAG,IAAI3G,KAAK,CAAkD,IAAI,CAACD,SAAS,CAAC4B,MAAM,CAAC;IACxG,IAAI,CAACqE,IAAI,CAAC,UAAUpE,CAAC,EAAEF,CAAC;MACtBiF,cAAc,CAACjF,CAAC,CAAC,GAAGF,QAAQ,CAACmB,IAAI,CAAC,IAAI,EAAEf,CAAC,EAAEF,CAAC,CAAC;IAC/C,CAAC,CAAC;IACF,IAAI,CAAChB,YAAY,GAAGnC,OAAO,CAACoI,cAAc,CAAC;IAC3C,OAAO,IAAI;EACb,CAAC;EAEDpH,SAAA,CAAAqB,SAAA,CAAAgG,EAAE,GAAF,UAAGC,KAAa,EAAEC,OAAY;IAC5B,IAAI,CAACd,IAAI,CAAC;MACR,IAAI,CAACe,gBAAgB,CAACF,KAAK,EAAEC,OAAO,CAAC;IACvC,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC;EAEDvH,SAAA,CAAAqB,SAAA,CAAA+B,IAAI,GAAJ,UAAKnB,QAA0D;IAAE,IAAAwF,IAAA;SAAA,IAAAC,EAAA,IAAc,EAAdA,EAAA,GAAAC,SAAA,CAAAvF,MAAc,EAAdsF,EAAA,EAAc;MAAdD,IAAA,CAAAC,EAAA,QAAAC,SAAA,CAAAD,EAAA;;IAC/DzF,QAAQ,CAACmB,IAAI,CAAAwE,KAAA,CAAb3F,QAAQ,EAAA0D,aAAA,EAAM,IAAI,CAAC/E,OAAO,EAAE,IAAI,GAAA2B,MAAA,CAAKkF,IAAI;IACzC,OAAO,IAAI;EACb,CAAC;EAEDzH,SAAA,CAAAqB,SAAA,CAAAU,IAAI,GAAJ;IACE,OAAO,IAAI,CAACvB,SAAS,CAAC,CAAC,CAAC;EAC1B,CAAC;EAEDR,SAAA,CAAAqB,SAAA,CAAAwG,KAAK,GAAL;IACE,OAAO,IAAI,CAACrH,SAAS;EACvB,CAAC;EAEDR,SAAA,CAAAqB,SAAA,CAAAf,WAAW,GAAX;IACE,OAAO,IAAI,CAACa,YAAY,CAAC2G,MAAM,CAAC,UAACC,CAAC;MAAK,QAAC,CAACA,CAAC;IAAH,CAAG,CAAC;EAC7C,CAAC;EAED/H,SAAA,CAAAqB,SAAA,CAAAlB,MAAM,GAAN;IACE,OAAO,IAAI,CAACS,OAAO;EACrB,CAAC;;iHA9PqCW,QAAgB,EAAEQ,IAA+B;IACrF,IAAMF,OAAO,GAAG,IAAI,CAACrB,SAAS,CAAC,CAAC,CAAC;IACjC,IAAMwH,KAAK,GAAGnG,OAAO,CAACoG,aAAa,CAAW1G,QAAQ,CAAC;IACvD,IAAIyG,KAAK,EAAE,OAAO,IAAIvG,EAAS,CAAC,CAACuG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAACpH,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC;IAC5E,IAAMqH,QAAQ,GAAG,OAAOnG,IAAI,KAAK,QAAQ,GAAG,IAAI,CAACG,aAAa,CAACH,IAAI,CAAC,GAAGA,IAAI,EAAE;IAC7EF,OAAO,CAACgB,WAAW,CAACqF,QAAQ,CAAC;IAC7B,OAAO,IAAIzG,EAAS,CAAC,CAACyG,QAAQ,CAAC,EAAE,IAAI,EAAE,IAAI,CAACtH,OAAO,EAAE,IAAI,CAACC,SAAS,CAAC;EACtE,CAAC;EAlHMb,SAAA,CAAA8F,QAAQ,GAAuC;IACpDqC,CAAC,EAAEhJ,KAAK;IACRiJ,IAAI,EAAE1I,IAAI;IACV2I,MAAM,EAAEpJ,MAAM;IACdqJ,IAAI,EAAE/I,IAAI;IACVgJ,IAAI,EAAE5I,IAAI;IACV6I,OAAO,EAAEtJ,OAAO;IAChBuJ,KAAK,EAAEpJ,KAAK;IACZqJ,IAAI,EAAEpJ,IAAI;IACVqJ,OAAO,EAAEnJ,OAAO;IAChBoJ,QAAQ,EAAEnJ,QAAQ;IAClBoJ,IAAI,EAAEzJ;GACP;EA8VH,OAAAY,SAAC;CAAA,CA3WD;SAAaA,SAAS;AA6WtB,OAAM,SAAU4B,MAAMA,CAAUG,IAAmB;EACjD,OAAO,IAAI/B,SAAS,CAAI,CAAC+B,IAAI,CAAC,EAAE,IAAI,EAAEA,IAAI,EAAEA,IAAI,CAAC+G,aAAa,CAAC;AACjE;AAEA,OAAM,SAAU/F,WAAWA,CACzB5C,MAAa,EACboB,QAAgB,EAChBQ,IAAiD;EAEjD,IAAI,CAAC5B,MAAM,CAAC8H,aAAa,CAAC1G,QAAQ,CAAC,EAAE;IACnC,OAAOK,MAAM,CAACzB,MAAM,CAAC,CAAC2B,MAAM,CAACC,IAAI,CAAC;EACpC;EACA,OAAOH,MAAM,CAACzB,MAAM,CAAC,CAACyB,MAAM,CAACL,QAAQ,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}