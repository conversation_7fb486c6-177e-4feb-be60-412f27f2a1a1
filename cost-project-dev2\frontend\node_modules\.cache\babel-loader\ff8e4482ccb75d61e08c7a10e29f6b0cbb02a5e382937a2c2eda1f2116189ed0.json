{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nimport React, { forwardRef } from 'react';\nimport { BaseChart } from '../../base';\nvar TinyAreaChart = forwardRef(function (props, ref) {\n  return React.createElement(BaseChart, __assign({}, props, {\n    chartType: \"TinyArea\",\n    ref: ref\n  }));\n});\nexport default TinyAreaChart;", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "React", "forwardRef", "BaseChart", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "ref", "createElement", "chartType"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/plots/es/components/tiny/area/index.js"], "sourcesContent": ["var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport React, { forwardRef } from 'react';\nimport { BaseChart } from '../../base';\nvar TinyAreaChart = forwardRef(function (props, ref) { return React.createElement(BaseChart, __assign({}, props, { chartType: \"TinyArea\", ref: ref })); });\nexport default TinyAreaChart;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAASC,CAAC,EAAE;IACpC,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAII,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAC3DN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IACnB;IACA,OAAON,CAAC;EACZ,CAAC;EACD,OAAOH,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAC1C,CAAC;AACD,OAAOO,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,SAAS,QAAQ,YAAY;AACtC,IAAIC,aAAa,GAAGF,UAAU,CAAC,UAAUG,KAAK,EAAEC,GAAG,EAAE;EAAE,OAAOL,KAAK,CAACM,aAAa,CAACJ,SAAS,EAAEhB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;IAAEG,SAAS,EAAE,UAAU;IAAEF,GAAG,EAAEA;EAAI,CAAC,CAAC,CAAC;AAAE,CAAC,CAAC;AAC1J,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}