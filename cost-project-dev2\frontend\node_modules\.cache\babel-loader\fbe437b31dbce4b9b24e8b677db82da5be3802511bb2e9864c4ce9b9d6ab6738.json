{"ast": null, "code": "/**\n * <zh/> 节点事件\n *\n * <en/> Node event\n */\nexport var NodeEvent;\n(function (NodeEvent) {\n  /**\n   * <zh/> 点击时触发\n   *\n   * <en/> Triggered when click\n   */\n  NodeEvent[\"CLICK\"] = \"node:click\";\n  /**\n   * <zh/> 双击时触发\n   *\n   * <en/> Triggered when double click\n   */\n  NodeEvent[\"DBLCLICK\"] = \"node:dblclick\";\n  /**\n   * <zh/> 指针移入时触发\n   *\n   * <en/> Triggered when the pointer enters\n   */\n  NodeEvent[\"POINTER_OVER\"] = \"node:pointerover\";\n  /**\n   * <zh/> 指针移出时触发\n   *\n   * <en/> Triggered when the pointer leaves\n   */\n  NodeEvent[\"POINTER_LEAVE\"] = \"node:pointerleave\";\n  /**\n   * <zh/> 指针移入时或移入子元素时触发（不会冒泡）\n   *\n   * <en/> Triggered when the pointer enters or enters a child element (does not bubble)\n   */\n  NodeEvent[\"POINTER_ENTER\"] = \"node:pointerenter\";\n  /**\n   * <zh/> 指针移动时触发\n   *\n   * <en/> Triggered when the pointer moves\n   */\n  NodeEvent[\"POINTER_MOVE\"] = \"node:pointermove\";\n  /**\n   * <zh/> 指针移出时触发\n   *\n   * <en/> Triggered when the pointer leaves\n   */\n  NodeEvent[\"POINTER_OUT\"] = \"node:pointerout\";\n  /**\n   * <zh/> 指针按下时触发\n   *\n   * <en/> Triggered when the pointer is pressed\n   */\n  NodeEvent[\"POINTER_DOWN\"] = \"node:pointerdown\";\n  /**\n   * <zh/> 指针抬起时触发\n   *\n   * <en/> Triggered when the pointer is lifted\n   */\n  NodeEvent[\"POINTER_UP\"] = \"node:pointerup\";\n  /**\n   * <zh/> 打开上下文菜单时触发\n   *\n   * <en/> Triggered when the context menu is opened\n   */\n  NodeEvent[\"CONTEXT_MENU\"] = \"node:contextmenu\";\n  /**\n   * <zh/> 开始拖拽时触发\n   *\n   * <en/> Triggered when dragging starts\n   */\n  NodeEvent[\"DRAG_START\"] = \"node:dragstart\";\n  /**\n   * <zh/> 拖拽过程中触发\n   *\n   * <en/> Triggered when dragging\n   */\n  NodeEvent[\"DRAG\"] = \"node:drag\";\n  /**\n   * <zh/> 拖拽结束时触发\n   *\n   * <en/> Triggered when dragging ends\n   */\n  NodeEvent[\"DRAG_END\"] = \"node:dragend\";\n  /**\n   * <zh/> 拖拽进入时触发\n   *\n   * <en/> Triggered when dragging enters\n   */\n  NodeEvent[\"DRAG_ENTER\"] = \"node:dragenter\";\n  /**\n   * <zh/> 拖拽经过时触发\n   *\n   * <en/> Triggered when dragging passes\n   */\n  NodeEvent[\"DRAG_OVER\"] = \"node:dragover\";\n  /**\n   * <zh/> 拖拽离开时触发\n   *\n   * <en/> Triggered when dragging leaves\n   */\n  NodeEvent[\"DRAG_LEAVE\"] = \"node:dragleave\";\n  /**\n   * <zh/> 拖拽放下时触发\n   *\n   * <en/> Triggered when dragging is dropped\n   */\n  NodeEvent[\"DROP\"] = \"node:drop\";\n})(NodeEvent || (NodeEvent = {}));", "map": {"version": 3, "names": ["NodeEvent"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\g6\\src\\constants\\events\\node.ts"], "sourcesContent": ["/**\n * <zh/> 节点事件\n *\n * <en/> Node event\n */\nexport enum NodeEvent {\n  /**\n   * <zh/> 点击时触发\n   *\n   * <en/> Triggered when click\n   */\n  CLICK = 'node:click',\n  /**\n   * <zh/> 双击时触发\n   *\n   * <en/> Triggered when double click\n   */\n  DBLCLICK = 'node:dblclick',\n  /**\n   * <zh/> 指针移入时触发\n   *\n   * <en/> Triggered when the pointer enters\n   */\n  POINTER_OVER = 'node:pointerover',\n  /**\n   * <zh/> 指针移出时触发\n   *\n   * <en/> Triggered when the pointer leaves\n   */\n  POINTER_LEAVE = 'node:pointerleave',\n  /**\n   * <zh/> 指针移入时或移入子元素时触发（不会冒泡）\n   *\n   * <en/> Triggered when the pointer enters or enters a child element (does not bubble)\n   */\n  POINTER_ENTER = 'node:pointerenter',\n  /**\n   * <zh/> 指针移动时触发\n   *\n   * <en/> Triggered when the pointer moves\n   */\n  POINTER_MOVE = 'node:pointermove',\n  /**\n   * <zh/> 指针移出时触发\n   *\n   * <en/> Triggered when the pointer leaves\n   */\n  POINTER_OUT = 'node:pointerout',\n  /**\n   * <zh/> 指针按下时触发\n   *\n   * <en/> Triggered when the pointer is pressed\n   */\n  POINTER_DOWN = 'node:pointerdown',\n  /**\n   * <zh/> 指针抬起时触发\n   *\n   * <en/> Triggered when the pointer is lifted\n   */\n  POINTER_UP = 'node:pointerup',\n  /**\n   * <zh/> 打开上下文菜单时触发\n   *\n   * <en/> Triggered when the context menu is opened\n   */\n  CONTEXT_MENU = 'node:contextmenu',\n  /**\n   * <zh/> 开始拖拽时触发\n   *\n   * <en/> Triggered when dragging starts\n   */\n  DRAG_START = 'node:dragstart',\n  /**\n   * <zh/> 拖拽过程中触发\n   *\n   * <en/> Triggered when dragging\n   */\n  DRAG = 'node:drag',\n  /**\n   * <zh/> 拖拽结束时触发\n   *\n   * <en/> Triggered when dragging ends\n   */\n  DRAG_END = 'node:dragend',\n  /**\n   * <zh/> 拖拽进入时触发\n   *\n   * <en/> Triggered when dragging enters\n   */\n  DRAG_ENTER = 'node:dragenter',\n  /**\n   * <zh/> 拖拽经过时触发\n   *\n   * <en/> Triggered when dragging passes\n   */\n  DRAG_OVER = 'node:dragover',\n  /**\n   * <zh/> 拖拽离开时触发\n   *\n   * <en/> Triggered when dragging leaves\n   */\n  DRAG_LEAVE = 'node:dragleave',\n  /**\n   * <zh/> 拖拽放下时触发\n   *\n   * <en/> Triggered when dragging is dropped\n   */\n  DROP = 'node:drop',\n}\n"], "mappings": "AAAA;;;;;AAKA,WAAYA,SAuGX;AAvGD,WAAYA,SAAS;EACnB;;;;;EAKAA,SAAA,wBAAoB;EACpB;;;;;EAKAA,SAAA,8BAA0B;EAC1B;;;;;EAKAA,SAAA,qCAAiC;EACjC;;;;;EAKAA,SAAA,uCAAmC;EACnC;;;;;EAKAA,SAAA,uCAAmC;EACnC;;;;;EAKAA,SAAA,qCAAiC;EACjC;;;;;EAKAA,SAAA,mCAA+B;EAC/B;;;;;EAKAA,SAAA,qCAAiC;EACjC;;;;;EAKAA,SAAA,iCAA6B;EAC7B;;;;;EAKAA,SAAA,qCAAiC;EACjC;;;;;EAKAA,SAAA,iCAA6B;EAC7B;;;;;EAKAA,SAAA,sBAAkB;EAClB;;;;;EAKAA,SAAA,6BAAyB;EACzB;;;;;EAKAA,SAAA,iCAA6B;EAC7B;;;;;EAKAA,SAAA,+BAA2B;EAC3B;;;;;EAKAA,SAAA,iCAA6B;EAC7B;;;;;EAKAA,SAAA,sBAAkB;AACpB,CAAC,EAvGWA,SAAS,KAATA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}