{"ast": null, "code": "import { deepMix } from '@antv/util';\nexport class Base {\n  /**\n   * 构造函数，根据自定义的选项和默认选项生成当前选项\n   * @param options 需要自定义配置的选项\n   */\n  constructor(options) {\n    this.options = deepMix({}, this.getDefaultOptions());\n    this.update(options);\n  }\n  /**\n   * 返回当前的所有选项\n   * @returns 当前的所有选项\n   */\n  getOptions() {\n    return this.options;\n  }\n  /**\n   * 更新选项和比例尺的内部状态\n   * @param updateOptions 需要更新的选项\n   */\n  update(updateOptions = {}) {\n    this.options = deepMix({}, this.options, updateOptions);\n    this.rescale(updateOptions);\n  }\n  /**\n   * 根据需要更新 options 和更新后的 options 更新 scale 的内部状态，\n   * 在函数内部可以用 this.options 获得更新后的 options\n   * @param options 需要更新的 options\n   */\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  rescale(options) {}\n}", "map": {"version": 3, "names": ["deepMix", "Base", "constructor", "options", "getDefaultOptions", "update", "getOptions", "updateOptions", "rescale"], "sources": ["scales/base.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AAGpC,OAAM,MAAgBC,IAAI;EA6BxB;;;;EAIAC,YAAYC,OAAW;IACrB,IAAI,CAACA,OAAO,GAAGH,OAAO,CAAC,EAAE,EAAE,IAAI,CAACI,iBAAiB,EAAE,CAAC;IACpD,IAAI,CAACC,MAAM,CAACF,OAAO,CAAC;EACtB;EAEA;;;;EAIOG,UAAUA,CAAA;IACf,OAAO,IAAI,CAACH,OAAO;EACrB;EAEA;;;;EAIOE,MAAMA,CAACE,aAAA,GAA4B,EAAE;IAC1C,IAAI,CAACJ,OAAO,GAAGH,OAAO,CAAC,EAAE,EAAE,IAAI,CAACG,OAAO,EAAEI,aAAa,CAAC;IACvD,IAAI,CAACC,OAAO,CAACD,aAAa,CAAC;EAC7B;EAEA;;;;;EAKA;EACUC,OAAOA,CAACL,OAAoB,GAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}