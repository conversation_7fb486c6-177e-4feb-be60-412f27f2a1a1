
package com.costproject.repository;

import com.costproject.entity.Resource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ResourceRepository extends JpaRepository<Resource, Long> {
    List<Resource> findByNameContaining(String name);
    List<Resource> findByType(Resource.ResourceType type);
    List<Resource> findByNameContainingAndType(String name, Resource.ResourceType type);
}
