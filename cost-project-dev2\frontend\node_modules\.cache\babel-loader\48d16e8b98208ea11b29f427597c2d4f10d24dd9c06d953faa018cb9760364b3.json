{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CloudServerOutlinedSvg from \"@ant-design/icons-svg/es/asn/CloudServerOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CloudServerOutlined = function CloudServerOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CloudServerOutlinedSvg\n  }));\n};\n\n/**![cloud-server](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcwNCA0NDZIMzIwYy00LjQgMC04IDMuNi04IDh2NDAyYzAgNC40IDMuNiA4IDggOGgzODRjNC40IDAgOC0zLjYgOC04VjQ1NGMwLTQuNC0zLjYtOC04LTh6bS0zMjggNjRoMjcydjExN0gzNzZWNTEwem0yNzIgMjkwSDM3NlY2ODNoMjcydjExN3oiIC8+PHBhdGggZD0iTTQyNCA3NDhhMzIgMzIgMCAxMDY0IDAgMzIgMzIgMCAxMC02NCAwem0wLTE3OGEzMiAzMiAwIDEwNjQgMCAzMiAzMiAwIDEwLTY0IDB6IiAvPjxwYXRoIGQ9Ik04MTEuNCAzNjguOUM3NjUuNiAyNDggNjQ4LjkgMTYyIDUxMi4yIDE2MlMyNTguOCAyNDcuOSAyMTMgMzY4LjhDMTI2LjkgMzkxLjUgNjMuNSA0NzAuMiA2NCA1NjMuNiA2NC42IDY2OCAxNDUuNiA3NTIuOSAyNDcuNiA3NjJjNC43LjQgOC43LTMuMyA4LjctOHYtNjAuNGMwLTQtMy03LjQtNy03LjktMjctMy40LTUyLjUtMTUuMi03Mi4xLTM0LjUtMjQtMjMuNS0zNy4yLTU1LjEtMzcuMi04OC42IDAtMjggOS4xLTU0LjQgMjYuMi03Ni40IDE2LjctMjEuNCA0MC4yLTM2LjkgNjYuMS00My43bDM3LjktMTAgMTMuOS0zNi43YzguNi0yMi44IDIwLjYtNDQuMiAzNS43LTYzLjUgMTQuOS0xOS4yIDMyLjYtMzYgNTIuNC01MCA0MS4xLTI4LjkgODkuNS00NC4yIDE0MC00NC4yczk4LjkgMTUuMyAxNDAgNDQuM2MxOS45IDE0IDM3LjUgMzAuOCA1Mi40IDUwIDE1LjEgMTkuMyAyNy4xIDQwLjcgMzUuNyA2My41bDEzLjggMzYuNiAzNy44IDEwYzU0LjIgMTQuNCA5Mi4xIDYzLjcgOTIuMSAxMjAgMCAzMy42LTEzLjIgNjUuMS0zNy4yIDg4LjYtMTkuNSAxOS4yLTQ0LjkgMzEuMS03MS45IDM0LjUtNCAuNS02LjkgMy45LTYuOSA3LjlWNzU0YzAgNC43IDQuMSA4LjQgOC44IDggMTAxLjctOS4yIDE4Mi41LTk0IDE4My4yLTE5OC4yLjYtOTMuNC02Mi43LTE3Mi4xLTE0OC42LTE5NC45eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CloudServerOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CloudServerOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "CloudServerOutlinedSvg", "AntdIcon", "CloudServerOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/CloudServerOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CloudServerOutlinedSvg from \"@ant-design/icons-svg/es/asn/CloudServerOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CloudServerOutlined = function CloudServerOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CloudServerOutlinedSvg\n  }));\n};\n\n/**![cloud-server](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcwNCA0NDZIMzIwYy00LjQgMC04IDMuNi04IDh2NDAyYzAgNC40IDMuNiA4IDggOGgzODRjNC40IDAgOC0zLjYgOC04VjQ1NGMwLTQuNC0zLjYtOC04LTh6bS0zMjggNjRoMjcydjExN0gzNzZWNTEwem0yNzIgMjkwSDM3NlY2ODNoMjcydjExN3oiIC8+PHBhdGggZD0iTTQyNCA3NDhhMzIgMzIgMCAxMDY0IDAgMzIgMzIgMCAxMC02NCAwem0wLTE3OGEzMiAzMiAwIDEwNjQgMCAzMiAzMiAwIDEwLTY0IDB6IiAvPjxwYXRoIGQ9Ik04MTEuNCAzNjguOUM3NjUuNiAyNDggNjQ4LjkgMTYyIDUxMi4yIDE2MlMyNTguOCAyNDcuOSAyMTMgMzY4LjhDMTI2LjkgMzkxLjUgNjMuNSA0NzAuMiA2NCA1NjMuNiA2NC42IDY2OCAxNDUuNiA3NTIuOSAyNDcuNiA3NjJjNC43LjQgOC43LTMuMyA4LjctOHYtNjAuNGMwLTQtMy03LjQtNy03LjktMjctMy40LTUyLjUtMTUuMi03Mi4xLTM0LjUtMjQtMjMuNS0zNy4yLTU1LjEtMzcuMi04OC42IDAtMjggOS4xLTU0LjQgMjYuMi03Ni40IDE2LjctMjEuNCA0MC4yLTM2LjkgNjYuMS00My43bDM3LjktMTAgMTMuOS0zNi43YzguNi0yMi44IDIwLjYtNDQuMiAzNS43LTYzLjUgMTQuOS0xOS4yIDMyLjYtMzYgNTIuNC01MCA0MS4xLTI4LjkgODkuNS00NC4yIDE0MC00NC4yczk4LjkgMTUuMyAxNDAgNDQuM2MxOS45IDE0IDM3LjUgMzAuOCA1Mi40IDUwIDE1LjEgMTkuMyAyNy4xIDQwLjcgMzUuNyA2My41bDEzLjggMzYuNiAzNy44IDEwYzU0LjIgMTQuNCA5Mi4xIDYzLjcgOTIuMSAxMjAgMCAzMy42LTEzLjIgNjUuMS0zNy4yIDg4LjYtMTkuNSAxOS4yLTQ0LjkgMzEuMS03MS45IDM0LjUtNCAuNS02LjkgMy45LTYuOSA3LjlWNzU0YzAgNC43IDQuMSA4LjQgOC44IDggMTAxLjctOS4yIDE4Mi41LTk0IDE4My4yLTE5OC4yLjYtOTMuNC02Mi43LTE3Mi4xLTE0OC42LTE5NC45eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CloudServerOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CloudServerOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,mBAAmB,CAAC;AAChE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,qBAAqB;AAC7C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}