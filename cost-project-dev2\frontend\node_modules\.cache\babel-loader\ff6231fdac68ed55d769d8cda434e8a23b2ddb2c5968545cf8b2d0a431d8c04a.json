{"ast": null, "code": "export { dark } from './dark';\nexport { light } from './light';", "map": {"version": 3, "names": ["dark", "light"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\g6\\src\\themes\\index.ts"], "sourcesContent": ["export { dark } from './dark';\nexport { light } from './light';\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,QAAQ;AAC7B,SAASC,KAAK,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}