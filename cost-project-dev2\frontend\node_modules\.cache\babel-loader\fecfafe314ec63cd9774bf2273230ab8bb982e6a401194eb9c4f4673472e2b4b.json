{"ast": null, "code": "/**\n * TODO: The median method consistently performs better than the barycenter method and has a slight theoretical advantage\n */\nexport const barycenter = (g, movable) => {\n  return movable.map(v => {\n    const inV = g.getRelatedEdges(v, 'in');\n    if (!(inV === null || inV === void 0 ? void 0 : inV.length)) {\n      return {\n        v\n      };\n    }\n    const result = {\n      sum: 0,\n      weight: 0\n    };\n    inV === null || inV === void 0 ? void 0 : inV.forEach(e => {\n      const nodeU = g.getNode(e.source);\n      result.sum += e.data.weight * nodeU.data.order;\n      result.weight += e.data.weight;\n    });\n    return {\n      v,\n      barycenter: result.sum / result.weight,\n      weight: result.weight\n    };\n  });\n};", "map": {"version": 3, "names": ["barycenter", "g", "movable", "map", "v", "inV", "getRelatedEdges", "length", "result", "sum", "weight", "for<PERSON>ach", "e", "nodeU", "getNode", "source", "data", "order"], "sources": ["../../../src/antv-dagre/order/barycenter.ts"], "sourcesContent": [null], "mappings": "AAGA;;;AAGA,OAAO,MAAMA,UAAU,GAAGA,CAACC,CAAQ,EAAEC,OAAa,KAAI;EACpD,OAAOA,OAAO,CAACC,GAAG,CAAEC,CAAC,IAAI;IACvB,MAAMC,GAAG,GAAGJ,CAAC,CAACK,eAAe,CAACF,CAAC,EAAE,IAAI,CAAC;IACtC,IAAI,EAACC,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEE,MAAM,GAAE;MAChB,OAAO;QAAEH;MAAC,CAAE;;IAGd,MAAMI,MAAM,GAAG;MAAEC,GAAG,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAC,CAAE;IACpCL,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEM,OAAO,CAAEC,CAAC,IAAI;MACjB,MAAMC,KAAK,GAAGZ,CAAC,CAACa,OAAO,CAACF,CAAC,CAACG,MAAM,CAAE;MAClCP,MAAM,CAACC,GAAG,IAAIG,CAAC,CAACI,IAAI,CAACN,MAAO,GAAGG,KAAK,CAACG,IAAI,CAACC,KAAM;MAChDT,MAAM,CAACE,MAAM,IAAIE,CAAC,CAACI,IAAI,CAACN,MAAO;IACjC,CAAC,CAAC;IACF,OAAO;MACLN,CAAC;MACDJ,UAAU,EAAEQ,MAAM,CAACC,GAAG,GAAGD,MAAM,CAACE,MAAM;MACtCA,MAAM,EAAEF,MAAM,CAACE;KAChB;EACH,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}