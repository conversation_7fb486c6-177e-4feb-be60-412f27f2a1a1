{"ast": null, "code": "// 临时注释掉，因为包已经被移除\n// import { initToolbar } from '@stagewise/toolbar';\n\n// 简化版本的Stagewise工具条配置\n// 移除复杂的配置对象，避免语法错误\n\n// 初始化Stagewise工具条\nexport function setupStagewise() {\n  // 只在开发模式下初始化\n  if (process.env.NODE_ENV === 'development') {\n    try {\n      console.log('🎯 Stagewise工具条已初始化（简化模式）');\n\n      // 简单的调试模式切换\n      window.toggleDebugMode = () => {\n        const isDebugMode = localStorage.getItem('debugMode') === 'true';\n        localStorage.setItem('debugMode', (!isDebugMode).toString());\n        if (!isDebugMode) {\n          document.body.style.border = '3px solid #ff4d4f';\n          console.log('🔧 调试模式已启用');\n        } else {\n          document.body.style.border = 'none';\n          console.log('✅ 调试模式已禁用');\n        }\n      };\n    } catch (error) {\n      console.error('❌ Stagewise工具条初始化失败:', error);\n    }\n  }\n}\n\n// 导出一个简单的默认配置\nexport default {\n  name: 'cost-project-stagewise',\n  version: '1.0.0',\n  enabled: process.env.NODE_ENV === 'development'\n};", "map": {"version": 3, "names": ["setupStagewise", "process", "env", "NODE_ENV", "console", "log", "window", "toggleDebugMode", "isDebugMode", "localStorage", "getItem", "setItem", "toString", "document", "body", "style", "border", "error", "name", "version", "enabled"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/src/utils/stagewise.js"], "sourcesContent": ["// 临时注释掉，因为包已经被移除\n// import { initToolbar } from '@stagewise/toolbar';\n\n// 简化版本的Stagewise工具条配置\n// 移除复杂的配置对象，避免语法错误\n\n// 初始化Stagewise工具条\nexport function setupStagewise() {\n  // 只在开发模式下初始化\n  if (process.env.NODE_ENV === 'development') {\n    try {\n      console.log('🎯 Stagewise工具条已初始化（简化模式）');\n      \n      // 简单的调试模式切换\n      window.toggleDebugMode = () => {\n        const isDebugMode = localStorage.getItem('debugMode') === 'true';\n        localStorage.setItem('debugMode', (!isDebugMode).toString());\n        \n        if (!isDebugMode) {\n          document.body.style.border = '3px solid #ff4d4f';\n          console.log('🔧 调试模式已启用');\n        } else {\n          document.body.style.border = 'none';\n          console.log('✅ 调试模式已禁用');\n        }\n      };\n      \n    } catch (error) {\n      console.error('❌ Stagewise工具条初始化失败:', error);\n    }\n  }\n}\n\n// 导出一个简单的默认配置\nexport default {\n  name: 'cost-project-stagewise',\n  version: '1.0.0',\n  enabled: process.env.NODE_ENV === 'development'\n}; "], "mappings": "AAAA;AACA;;AAEA;AACA;;AAEA;AACA,OAAO,SAASA,cAAcA,CAAA,EAAG;EAC/B;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1C,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACAC,MAAM,CAACC,eAAe,GAAG,MAAM;QAC7B,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,KAAK,MAAM;QAChED,YAAY,CAACE,OAAO,CAAC,WAAW,EAAE,CAAC,CAACH,WAAW,EAAEI,QAAQ,CAAC,CAAC,CAAC;QAE5D,IAAI,CAACJ,WAAW,EAAE;UAChBK,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG,mBAAmB;UAChDZ,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B,CAAC,MAAM;UACLQ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;UACnCZ,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QAC1B;MACF,CAAC;IAEH,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdb,OAAO,CAACa,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF;AACF;;AAEA;AACA,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAEnB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}