{"ast": null, "code": "\"use client\";\n\nimport InternalAvatar from './Avatar';\nimport AvatarGroup from './AvatarGroup';\nconst Avatar = InternalAvatar;\nAvatar.Group = AvatarGroup;\nexport default Avatar;", "map": {"version": 3, "names": ["InternalAvatar", "AvatarGroup", "Avatar", "Group"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/antd/es/avatar/index.js"], "sourcesContent": ["\"use client\";\n\nimport InternalAvatar from './Avatar';\nimport AvatarGroup from './AvatarGroup';\nconst Avatar = InternalAvatar;\nAvatar.Group = AvatarGroup;\nexport default Avatar;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,cAAc,MAAM,UAAU;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,MAAMC,MAAM,GAAGF,cAAc;AAC7BE,MAAM,CAACC,KAAK,GAAGF,WAAW;AAC1B,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}