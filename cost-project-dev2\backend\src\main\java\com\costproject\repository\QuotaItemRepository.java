package com.costproject.repository;

import com.costproject.entity.QuotaItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QuotaItemRepository extends JpaRepository<QuotaItem, Long> {
    List<QuotaItem> findByNameContainingOrCodeContaining(String name, String code);
    List<QuotaItem> findByCodeStartingWith(String codePrefix);
    boolean existsByCode(String code);
}