{"ast": null, "code": "import isArrayLike from './is-array-like';\nvar indexOf = function (arr, obj) {\n  if (!isArrayLike(arr)) {\n    return -1;\n  }\n  var m = Array.prototype.indexOf;\n  if (m) {\n    return m.call(arr, obj);\n  }\n  var index = -1;\n  for (var i = 0; i < arr.length; i++) {\n    if (arr[i] === obj) {\n      index = i;\n      break;\n    }\n  }\n  return index;\n};\nexport default indexOf;", "map": {"version": 3, "names": ["isArrayLike", "indexOf", "arr", "obj", "m", "Array", "prototype", "call", "index", "i", "length"], "sources": ["lodash/index-of.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAOA,WAAW,MAAM,iBAAiB;AAEzC,IAAMC,OAAO,GAAG,SAAAA,CAAaC,GAAQ,EAAEC,GAAM;EAC3C,IAAI,CAACH,WAAW,CAACE,GAAG,CAAC,EAAE;IACrB,OAAO,CAAC,CAAC;EACX;EACA,IAAME,CAAC,GAAGC,KAAK,CAACC,SAAS,CAACL,OAAO;EACjC,IAAIG,CAAC,EAAE;IACL,OAAOA,CAAC,CAACG,IAAI,CAACL,GAAG,EAAEC,GAAG,CAAC;EACzB;EACA,IAAIK,KAAK,GAAG,CAAC,CAAC;EAEd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,GAAG,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAIP,GAAG,CAACO,CAAC,CAAC,KAAKN,GAAG,EAAE;MAClBK,KAAK,GAAGC,CAAC;MACT;IACF;EACF;EACA,OAAOD,KAAK;AACd,CAAC;AAED,eAAeP,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}