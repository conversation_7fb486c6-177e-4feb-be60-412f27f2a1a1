{"ast": null, "code": "var _ = require(\"../lodash\");\nmodule.exports = floyd<PERSON>arshall;\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\nfunction floydWarshall(g, weightFn, edgeFn) {\n  return runFloydWarshall(g, weightFn || DEFAULT_WEIGHT_FUNC, edgeFn || function (v) {\n    return g.outEdges(v);\n  });\n}\nfunction runFloydWarshall(g, weightFn, edgeFn) {\n  var results = {};\n  var nodes = g.nodes();\n  nodes.forEach(function (v) {\n    results[v] = {};\n    results[v][v] = {\n      distance: 0\n    };\n    nodes.forEach(function (w) {\n      if (v !== w) {\n        results[v][w] = {\n          distance: Number.POSITIVE_INFINITY\n        };\n      }\n    });\n    edgeFn(v).forEach(function (edge) {\n      var w = edge.v === v ? edge.w : edge.v;\n      var d = weightFn(edge);\n      results[v][w] = {\n        distance: d,\n        predecessor: v\n      };\n    });\n  });\n  nodes.forEach(function (k) {\n    var rowK = results[k];\n    nodes.forEach(function (i) {\n      var rowI = results[i];\n      nodes.forEach(function (j) {\n        var ik = rowI[k];\n        var kj = rowK[j];\n        var ij = rowI[j];\n        var altDistance = ik.distance + kj.distance;\n        if (altDistance < ij.distance) {\n          ij.distance = altDistance;\n          ij.predecessor = kj.predecessor;\n        }\n      });\n    });\n  });\n  return results;\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "<PERSON>oyd<PERSON><PERSON><PERSON>", "DEFAULT_WEIGHT_FUNC", "constant", "g", "weightFn", "edgeFn", "runFloydWarshall", "v", "outEdges", "results", "nodes", "for<PERSON>ach", "distance", "w", "Number", "POSITIVE_INFINITY", "edge", "d", "predecessor", "k", "rowK", "i", "rowI", "j", "ik", "kj", "ij", "altDistance"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/graphlib/lib/alg/floyd-warshall.js"], "sourcesContent": ["var _ = require(\"../lodash\");\n\nmodule.exports = floyd<PERSON>arshall;\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction floydWarshall(g, weightFn, edgeFn) {\n  return runFloydWarshall(g,\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn || function(v) { return g.outEdges(v); });\n}\n\nfunction runFloydWarshall(g, weightFn, edgeFn) {\n  var results = {};\n  var nodes = g.nodes();\n\n  nodes.forEach(function(v) {\n    results[v] = {};\n    results[v][v] = { distance: 0 };\n    nodes.forEach(function(w) {\n      if (v !== w) {\n        results[v][w] = { distance: Number.POSITIVE_INFINITY };\n      }\n    });\n    edgeFn(v).forEach(function(edge) {\n      var w = edge.v === v ? edge.w : edge.v;\n      var d = weightFn(edge);\n      results[v][w] = { distance: d, predecessor: v };\n    });\n  });\n\n  nodes.forEach(function(k) {\n    var rowK = results[k];\n    nodes.forEach(function(i) {\n      var rowI = results[i];\n      nodes.forEach(function(j) {\n        var ik = rowI[k];\n        var kj = rowK[j];\n        var ij = rowI[j];\n        var altDistance = ik.distance + kj.distance;\n        if (altDistance < ij.distance) {\n          ij.distance = altDistance;\n          ij.predecessor = kj.predecessor;\n        }\n      });\n    });\n  });\n\n  return results;\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAGC,aAAa;AAE9B,IAAIC,mBAAmB,GAAGL,CAAC,CAACM,QAAQ,CAAC,CAAC,CAAC;AAEvC,SAASF,aAAaA,CAACG,CAAC,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAC1C,OAAOC,gBAAgB,CAACH,CAAC,EACvBC,QAAQ,IAAIH,mBAAmB,EAC/BI,MAAM,IAAI,UAASE,CAAC,EAAE;IAAE,OAAOJ,CAAC,CAACK,QAAQ,CAACD,CAAC,CAAC;EAAE,CAAC,CAAC;AACpD;AAEA,SAASD,gBAAgBA,CAACH,CAAC,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAC7C,IAAII,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIC,KAAK,GAAGP,CAAC,CAACO,KAAK,CAAC,CAAC;EAErBA,KAAK,CAACC,OAAO,CAAC,UAASJ,CAAC,EAAE;IACxBE,OAAO,CAACF,CAAC,CAAC,GAAG,CAAC,CAAC;IACfE,OAAO,CAACF,CAAC,CAAC,CAACA,CAAC,CAAC,GAAG;MAAEK,QAAQ,EAAE;IAAE,CAAC;IAC/BF,KAAK,CAACC,OAAO,CAAC,UAASE,CAAC,EAAE;MACxB,IAAIN,CAAC,KAAKM,CAAC,EAAE;QACXJ,OAAO,CAACF,CAAC,CAAC,CAACM,CAAC,CAAC,GAAG;UAAED,QAAQ,EAAEE,MAAM,CAACC;QAAkB,CAAC;MACxD;IACF,CAAC,CAAC;IACFV,MAAM,CAACE,CAAC,CAAC,CAACI,OAAO,CAAC,UAASK,IAAI,EAAE;MAC/B,IAAIH,CAAC,GAAGG,IAAI,CAACT,CAAC,KAAKA,CAAC,GAAGS,IAAI,CAACH,CAAC,GAAGG,IAAI,CAACT,CAAC;MACtC,IAAIU,CAAC,GAAGb,QAAQ,CAACY,IAAI,CAAC;MACtBP,OAAO,CAACF,CAAC,CAAC,CAACM,CAAC,CAAC,GAAG;QAAED,QAAQ,EAAEK,CAAC;QAAEC,WAAW,EAAEX;MAAE,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFG,KAAK,CAACC,OAAO,CAAC,UAASQ,CAAC,EAAE;IACxB,IAAIC,IAAI,GAAGX,OAAO,CAACU,CAAC,CAAC;IACrBT,KAAK,CAACC,OAAO,CAAC,UAASU,CAAC,EAAE;MACxB,IAAIC,IAAI,GAAGb,OAAO,CAACY,CAAC,CAAC;MACrBX,KAAK,CAACC,OAAO,CAAC,UAASY,CAAC,EAAE;QACxB,IAAIC,EAAE,GAAGF,IAAI,CAACH,CAAC,CAAC;QAChB,IAAIM,EAAE,GAAGL,IAAI,CAACG,CAAC,CAAC;QAChB,IAAIG,EAAE,GAAGJ,IAAI,CAACC,CAAC,CAAC;QAChB,IAAII,WAAW,GAAGH,EAAE,CAACZ,QAAQ,GAAGa,EAAE,CAACb,QAAQ;QAC3C,IAAIe,WAAW,GAAGD,EAAE,CAACd,QAAQ,EAAE;UAC7Bc,EAAE,CAACd,QAAQ,GAAGe,WAAW;UACzBD,EAAE,CAACR,WAAW,GAAGO,EAAE,CAACP,WAAW;QACjC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOT,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}