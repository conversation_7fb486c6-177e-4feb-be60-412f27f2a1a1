{"ast": null, "code": "import { isNumber } from '@antv/util';\nexport const clone = target => {\n  if (target === null) {\n    return target;\n  }\n  if (target instanceof Date) {\n    return new Date(target.getTime());\n  }\n  if (target instanceof Array) {\n    const cp = [];\n    target.forEach(v => {\n      cp.push(v);\n    });\n    return cp.map(n => clone(n));\n  }\n  if (typeof target === 'object') {\n    const cp = {};\n    Object.keys(target).forEach(k => {\n      cp[k] = clone(target[k]);\n    });\n    return cp;\n  }\n  return target;\n};\n/**\n * Clone node or edge data and format it\n * @param target node/edge to be cloned\n * @param initRange whether init the x and y in data with the range, which means [xRange, yRange]\n * @returns cloned node/edge\n */\nexport const cloneFormatData = (target, initRange) => {\n  const cloned = clone(target);\n  cloned.data = cloned.data || {};\n  if (initRange) {\n    if (!isNumber(cloned.data.x)) cloned.data.x = Math.random() * initRange[0];\n    if (!isNumber(cloned.data.y)) cloned.data.y = Math.random() * initRange[1];\n  }\n  return cloned;\n};", "map": {"version": 3, "names": ["isNumber", "clone", "target", "Date", "getTime", "Array", "cp", "for<PERSON>ach", "v", "push", "map", "n", "Object", "keys", "k", "cloneFormatData", "initRange", "cloned", "data", "x", "Math", "random", "y"], "sources": ["../../src/util/object.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AAGrC,OAAO,MAAMC,KAAK,GAAOC,MAAS,IAAO;EACvC,IAAIA,MAAM,KAAK,IAAI,EAAE;IACnB,OAAOA,MAAM;;EAEf,IAAIA,MAAM,YAAYC,IAAI,EAAE;IAC1B,OAAO,IAAIA,IAAI,CAACD,MAAM,CAACE,OAAO,EAAE,CAAQ;;EAE1C,IAAIF,MAAM,YAAYG,KAAK,EAAE;IAC3B,MAAMC,EAAE,GAAG,EAAW;IACrBJ,MAAgB,CAACK,OAAO,CAAEC,CAAC,IAAI;MAC9BF,EAAE,CAACG,IAAI,CAACD,CAAC,CAAC;IACZ,CAAC,CAAC;IACF,OAAOF,EAAE,CAACI,GAAG,CAAEC,CAAM,IAAKV,KAAK,CAAMU,CAAC,CAAC,CAAQ;;EAEjD,IAAI,OAAOT,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAMI,EAAE,GAAG,EAA4B;IACvCM,MAAM,CAACC,IAAI,CAACX,MAAM,CAAC,CAACK,OAAO,CAAEO,CAAC,IAAI;MAChCR,EAAE,CAACQ,CAAC,CAAC,GAAGb,KAAK,CAAOC,MAAc,CAACY,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC;IACF,OAAOR,EAAO;;EAEhB,OAAOJ,MAAM;AACf,CAAC;AAED;;;;;;AAMA,OAAO,MAAMa,eAAe,GAAGA,CAC7Bb,MAAS,EACTc,SAA4B,KACvB;EACL,MAAMC,MAAM,GAAGhB,KAAK,CAACC,MAAM,CAAC;EAC5Be,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACC,IAAI,IAAI,EAAE;EAC/B,IAAIF,SAAS,EAAE;IACb,IAAI,CAAChB,QAAQ,CAACiB,MAAM,CAACC,IAAI,CAACC,CAAC,CAAC,EAAEF,MAAM,CAACC,IAAI,CAACC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAGL,SAAS,CAAC,CAAC,CAAC;IAC1E,IAAI,CAAChB,QAAQ,CAACiB,MAAM,CAACC,IAAI,CAACI,CAAC,CAAC,EAAEL,MAAM,CAACC,IAAI,CAACI,CAAC,GAAGF,IAAI,CAACC,MAAM,EAAE,GAAGL,SAAS,CAAC,CAAC,CAAC;;EAE5E,OAAOC,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}