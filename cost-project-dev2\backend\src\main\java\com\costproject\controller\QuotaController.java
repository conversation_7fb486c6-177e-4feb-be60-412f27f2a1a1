package com.costproject.controller;

import com.costproject.entity.QuotaItem;
import com.costproject.service.QuotaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/quotas")
@Api(tags = "项目定额管理")
public class QuotaController {
    
    private static final Logger log = LoggerFactory.getLogger(QuotaController.class);

    private final QuotaService quotaService;
    
    public QuotaController(QuotaService quotaService) {
        this.quotaService = quotaService;
    }

    @PostMapping
    @ApiOperation("创建定额项")
    public ResponseEntity<QuotaItem> createQuotaItem(
            @ApiParam(value = "定额项信息", required = true)
            @Valid @RequestBody QuotaItem quotaItem) {
        return ResponseEntity.ok(quotaService.createQuotaItem(quotaItem));
    }

    @PutMapping("/{id}")
    @ApiOperation("更新定额项")
    public ResponseEntity<QuotaItem> updateQuotaItem(
            @ApiParam(value = "定额项ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "定额项信息", required = true)
            @Valid @RequestBody QuotaItem quotaItem) {
        return ResponseEntity.ok(quotaService.updateQuotaItem(id, quotaItem));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除定额项")
    public ResponseEntity<Void> deleteQuotaItem(
            @ApiParam(value = "定额项ID", required = true)
            @PathVariable Long id) {
        quotaService.deleteQuotaItem(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取定额项详情")
    public ResponseEntity<QuotaItem> getQuotaItem(
            @ApiParam(value = "定额项ID", required = true)
            @PathVariable Long id) {
        return ResponseEntity.ok(quotaService.getQuotaItem(id));
    }

    @GetMapping
    @ApiOperation("查询定额列表")
    public ResponseEntity<List<QuotaItem>> searchQuotaItems(
            @ApiParam(value = "关键词")
            @RequestParam(required = false) String keyword) {
        return ResponseEntity.ok(quotaService.searchQuotaItems(keyword));
    }

    @PostMapping("/import/excel")
    @ApiOperation("从Excel导入定额库")
    public ResponseEntity<List<QuotaItem>> importFromExcel(
            @ApiParam(value = "Excel文件", required = true)
            @RequestParam("file") MultipartFile file) {
        try {
            return ResponseEntity.ok(quotaService.importFromExcel(file));
        } catch (Exception e) {
            log.error("导入Excel失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/import/markdown")
    @ApiOperation("从Markdown导入定额库")
    public ResponseEntity<List<QuotaItem>> importFromMarkdown(
            @ApiParam(value = "Markdown文件", required = true)
            @RequestParam("file") MultipartFile file) {
        try {
            return ResponseEntity.ok(quotaService.importFromMarkdown(file));
        } catch (Exception e) {
            log.error("导入Markdown失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/{id}/calculate-costs")
    @ApiOperation("计算定额成本")
    public ResponseEntity<Void> calculateCosts(
            @ApiParam(value = "定额项ID", required = true)
            @PathVariable Long id) {
        QuotaItem quotaItem = quotaService.getQuotaItem(id);
        quotaService.calculateCosts(quotaItem);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/resource-consumption")
    @ApiOperation("计算资源消耗")
    public ResponseEntity<Map<String, BigDecimal>> calculateResourceConsumption(
            @ApiParam(value = "定额项ID列表", required = true)
            @RequestParam List<Long> ids) {
        return ResponseEntity.ok(quotaService.calculateResourceConsumption(ids));
    }

    @GetMapping("/resource-plan")
    @ApiOperation("生成资源需求计划")
    public ResponseEntity<Map<String, Object>> generateResourcePlan(
            @ApiParam(value = "定额项ID列表", required = true)
            @RequestParam List<Long> ids) {
        return ResponseEntity.ok(quotaService.generateResourcePlan(ids));
    }

    @PostMapping("/{id}/labor-cost")
    @ApiOperation("更新人工费用")
    public ResponseEntity<QuotaItem> updateLaborCost(
            @ApiParam(value = "定额项ID", required = true)
            @PathVariable Long id,
            @RequestBody Map<String, BigDecimal> laborCosts) {
        QuotaItem quotaItem = quotaService.getQuotaItem(id);
        
        // 更新人工费用组成
        if (laborCosts.containsKey("basicSalary")) {
            quotaItem.setBasicSalary(laborCosts.get("basicSalary"));
        }
        if (laborCosts.containsKey("salaryAllowance")) {
            quotaItem.setSalaryAllowance(laborCosts.get("salaryAllowance"));
        }
        if (laborCosts.containsKey("auxiliaryWage")) {
            quotaItem.setAuxiliaryWage(laborCosts.get("auxiliaryWage"));
        }
        if (laborCosts.containsKey("welfareFee")) {
            quotaItem.setWelfareFee(laborCosts.get("welfareFee"));
        }
        if (laborCosts.containsKey("laborProtectionFee")) {
            quotaItem.setLaborProtectionFee(laborCosts.get("laborProtectionFee"));
        }
        
        return ResponseEntity.ok(quotaService.updateQuotaItem(id, quotaItem));
    }

    @PostMapping("/{id}/material-cost")
    @ApiOperation("更新材料费用")
    public ResponseEntity<QuotaItem> updateMaterialCost(
            @ApiParam(value = "定额项ID", required = true)
            @PathVariable Long id,
            @RequestBody Map<String, BigDecimal> materialCosts) {
        QuotaItem quotaItem = quotaService.getQuotaItem(id);
        
        // 更新材料费用组成
        if (materialCosts.containsKey("materialPrice")) {
            quotaItem.setMaterialPrice(materialCosts.get("materialPrice"));
        }
        if (materialCosts.containsKey("transportFee")) {
            quotaItem.setTransportFee(materialCosts.get("transportFee"));
        }
        if (materialCosts.containsKey("lossFee")) {
            quotaItem.setLossFee(materialCosts.get("lossFee"));
        }
        if (materialCosts.containsKey("purchaseAndStorageFee")) {
            quotaItem.setPurchaseAndStorageFee(materialCosts.get("purchaseAndStorageFee"));
        }
        if (materialCosts.containsKey("testingFee")) {
            quotaItem.setTestingFee(materialCosts.get("testingFee"));
        }
        
        return ResponseEntity.ok(quotaService.updateQuotaItem(id, quotaItem));
    }

    @PostMapping("/{id}/machine-cost")
    @ApiOperation("更新机械费用")
    public ResponseEntity<QuotaItem> updateMachineCost(
            @ApiParam(value = "定额项ID", required = true)
            @PathVariable Long id,
            @RequestBody Map<String, BigDecimal> machineCosts) {
        QuotaItem quotaItem = quotaService.getQuotaItem(id);
        
        // 更新机械费用组成
        if (machineCosts.containsKey("depreciationFee")) {
            quotaItem.setDepreciationFee(machineCosts.get("depreciationFee"));
        }
        if (machineCosts.containsKey("majorRepairFee")) {
            quotaItem.setMajorRepairFee(machineCosts.get("majorRepairFee"));
        }
        if (machineCosts.containsKey("routineRepairFee")) {
            quotaItem.setRoutineRepairFee(machineCosts.get("routineRepairFee"));
        }
        if (machineCosts.containsKey("installationAndTransportFee")) {
            quotaItem.setInstallationAndTransportFee(machineCosts.get("installationAndTransportFee"));
        }
        if (machineCosts.containsKey("machineOperatorFee")) {
            quotaItem.setMachineOperatorFee(machineCosts.get("machineOperatorFee"));
        }
        if (machineCosts.containsKey("fuelAndPowerFee")) {
            quotaItem.setFuelAndPowerFee(machineCosts.get("fuelAndPowerFee"));
        }
        if (machineCosts.containsKey("roadFee")) {
            quotaItem.setRoadFee(machineCosts.get("roadFee"));
        }
        
        return ResponseEntity.ok(quotaService.updateQuotaItem(id, quotaItem));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleException(Exception e) {
        log.error("处理请求时发生错误", e);
        return ResponseEntity.badRequest().body(e.getMessage());
    }
}
