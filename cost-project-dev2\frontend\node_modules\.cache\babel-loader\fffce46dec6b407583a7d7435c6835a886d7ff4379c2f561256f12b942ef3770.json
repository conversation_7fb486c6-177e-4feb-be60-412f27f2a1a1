{"ast": null, "code": "const parseInt10 = d => d ? parseInt(d) : 0;\n/**\n * @description Get the element's bounding size.\n * @param container dom element.\n * @returns the element width and height\n */\nexport function getContainerSize(container) {\n  // size = width/height - padding.\n  const style = getComputedStyle(container);\n  const wrapperWidth = container.clientWidth || parseInt10(style.width);\n  const wrapperHeight = container.clientHeight || parseInt10(style.height);\n  const widthPadding = parseInt10(style.paddingLeft) + parseInt10(style.paddingRight);\n  const heightPadding = parseInt10(style.paddingTop) + parseInt10(style.paddingBottom);\n  return {\n    width: wrapperWidth - widthPadding,\n    height: wrapperHeight - heightPadding\n  };\n}\n/**\n * @description Calculate the real canvas size by view options.\n */\nexport function getBBoxSize(options) {\n  const {\n    height,\n    width,\n    padding = 0,\n    paddingLeft = padding,\n    paddingRight = padding,\n    paddingTop = padding,\n    paddingBottom = padding,\n    margin = 0,\n    marginLeft = margin,\n    marginRight = margin,\n    marginTop = margin,\n    marginBottom = margin,\n    inset = 0,\n    insetLeft = inset,\n    insetRight = inset,\n    insetTop = inset,\n    insetBottom = inset\n  } = options;\n  // @todo Add this padding to theme.\n  // 30 is default size for padding, which defined in runtime.\n  const maybeAuto = padding => padding === 'auto' ? 20 : padding;\n  const finalWidth = width - maybeAuto(paddingLeft) - maybeAuto(paddingRight) - marginLeft - marginRight - insetLeft - insetRight;\n  const finalHeight = height - maybeAuto(paddingTop) - maybeAuto(paddingBottom) - marginTop - marginBottom - insetTop - insetBottom;\n  return {\n    width: finalWidth,\n    height: finalHeight\n  };\n}", "map": {"version": 3, "names": ["parseInt10", "d", "parseInt", "getContainerSize", "container", "style", "getComputedStyle", "wrapperWidth", "clientWidth", "width", "wrapperHeight", "clientHeight", "height", "widthPadding", "paddingLeft", "paddingRight", "heightPadding", "paddingTop", "paddingBottom", "getBBoxSize", "options", "padding", "margin", "marginLeft", "marginRight", "marginTop", "marginBottom", "inset", "insetLeft", "insetRight", "insetTop", "insetBottom", "maybeAuto", "finalWidth", "finalHeight"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\g2\\src\\utils\\size.ts"], "sourcesContent": ["import { G2View } from '../runtime';\n\ntype Size = {\n  width: number;\n  height: number;\n  depth?: number;\n};\n\nconst parseInt10 = (d: string) => (d ? parseInt(d) : 0);\n\n/**\n * @description Get the element's bounding size.\n * @param container dom element.\n * @returns the element width and height\n */\nexport function getContainerSize(container: HTMLElement): Size {\n  // size = width/height - padding.\n\n  const style = getComputedStyle(container);\n\n  const wrapperWidth = container.clientWidth || parseInt10(style.width);\n  const wrapperHeight = container.clientHeight || parseInt10(style.height);\n\n  const widthPadding =\n    parseInt10(style.paddingLeft) + parseInt10(style.paddingRight);\n  const heightPadding =\n    parseInt10(style.paddingTop) + parseInt10(style.paddingBottom);\n\n  return {\n    width: wrapperWidth - widthPadding,\n    height: wrapperHeight - heightPadding,\n  };\n}\n\n/**\n * @description Calculate the real canvas size by view options.\n */\nexport function getBBoxSize(options: G2View): Size {\n  const {\n    height,\n    width,\n    padding = 0,\n    paddingLeft = padding,\n    paddingRight = padding,\n    paddingTop = padding,\n    paddingBottom = padding,\n    margin = 0,\n    marginLeft = margin,\n    marginRight = margin,\n    marginTop = margin,\n    marginBottom = margin,\n    inset = 0,\n    insetLeft = inset,\n    insetRight = inset,\n    insetTop = inset,\n    insetBottom = inset,\n  } = options;\n\n  // @todo Add this padding to theme.\n  // 30 is default size for padding, which defined in runtime.\n  const maybeAuto = (padding) => (padding === 'auto' ? 20 : padding);\n\n  const finalWidth =\n    width -\n    maybeAuto(paddingLeft) -\n    maybeAuto(paddingRight) -\n    marginLeft -\n    marginRight -\n    insetLeft -\n    insetRight;\n  const finalHeight =\n    height -\n    maybeAuto(paddingTop) -\n    maybeAuto(paddingBottom) -\n    marginTop -\n    marginBottom -\n    insetTop -\n    insetBottom;\n\n  return { width: finalWidth, height: finalHeight };\n}\n"], "mappings": "AAQA,MAAMA,UAAU,GAAIC,CAAS,IAAMA,CAAC,GAAGC,QAAQ,CAACD,CAAC,CAAC,GAAG,CAAE;AAEvD;;;;;AAKA,OAAM,SAAUE,gBAAgBA,CAACC,SAAsB;EACrD;EAEA,MAAMC,KAAK,GAAGC,gBAAgB,CAACF,SAAS,CAAC;EAEzC,MAAMG,YAAY,GAAGH,SAAS,CAACI,WAAW,IAAIR,UAAU,CAACK,KAAK,CAACI,KAAK,CAAC;EACrE,MAAMC,aAAa,GAAGN,SAAS,CAACO,YAAY,IAAIX,UAAU,CAACK,KAAK,CAACO,MAAM,CAAC;EAExE,MAAMC,YAAY,GAChBb,UAAU,CAACK,KAAK,CAACS,WAAW,CAAC,GAAGd,UAAU,CAACK,KAAK,CAACU,YAAY,CAAC;EAChE,MAAMC,aAAa,GACjBhB,UAAU,CAACK,KAAK,CAACY,UAAU,CAAC,GAAGjB,UAAU,CAACK,KAAK,CAACa,aAAa,CAAC;EAEhE,OAAO;IACLT,KAAK,EAAEF,YAAY,GAAGM,YAAY;IAClCD,MAAM,EAAEF,aAAa,GAAGM;GACzB;AACH;AAEA;;;AAGA,OAAM,SAAUG,WAAWA,CAACC,OAAe;EACzC,MAAM;IACJR,MAAM;IACNH,KAAK;IACLY,OAAO,GAAG,CAAC;IACXP,WAAW,GAAGO,OAAO;IACrBN,YAAY,GAAGM,OAAO;IACtBJ,UAAU,GAAGI,OAAO;IACpBH,aAAa,GAAGG,OAAO;IACvBC,MAAM,GAAG,CAAC;IACVC,UAAU,GAAGD,MAAM;IACnBE,WAAW,GAAGF,MAAM;IACpBG,SAAS,GAAGH,MAAM;IAClBI,YAAY,GAAGJ,MAAM;IACrBK,KAAK,GAAG,CAAC;IACTC,SAAS,GAAGD,KAAK;IACjBE,UAAU,GAAGF,KAAK;IAClBG,QAAQ,GAAGH,KAAK;IAChBI,WAAW,GAAGJ;EAAK,CACpB,GAAGP,OAAO;EAEX;EACA;EACA,MAAMY,SAAS,GAAIX,OAAO,IAAMA,OAAO,KAAK,MAAM,GAAG,EAAE,GAAGA,OAAQ;EAElE,MAAMY,UAAU,GACdxB,KAAK,GACLuB,SAAS,CAAClB,WAAW,CAAC,GACtBkB,SAAS,CAACjB,YAAY,CAAC,GACvBQ,UAAU,GACVC,WAAW,GACXI,SAAS,GACTC,UAAU;EACZ,MAAMK,WAAW,GACftB,MAAM,GACNoB,SAAS,CAACf,UAAU,CAAC,GACrBe,SAAS,CAACd,aAAa,CAAC,GACxBO,SAAS,GACTC,YAAY,GACZI,QAAQ,GACRC,WAAW;EAEb,OAAO;IAAEtB,KAAK,EAAEwB,UAAU;IAAErB,MAAM,EAAEsB;EAAW,CAAE;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}