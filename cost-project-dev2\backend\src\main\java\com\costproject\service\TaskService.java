package com.costproject.service;

import com.costproject.entity.Task;
import com.costproject.repository.TaskRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class TaskService {

    private static final Logger log = LoggerFactory.getLogger(TaskService.class);

    private final TaskRepository taskRepository;

    public TaskService(TaskRepository taskRepository) {
        this.taskRepository = taskRepository;
    }

    /**
     * 创建任务
     */
    @Transactional
    public Task createTask(Task task) {
        validateTask(task);
        return taskRepository.save(task);
    }

    /**
     * 更新任务
     */
    @Transactional
    public Task updateTask(Long id, Task task) {
        Task existing = getTask(id);
        updateTaskFields(existing, task);
        return taskRepository.save(existing);
    }

    /**
     * 删除任务
     */
    @Transactional
    public void deleteTask(Long id) {
        Task task = getTask(id);
        taskRepository.delete(task);
    }

    /**
     * 获取任务详情
     */
    public Task getTask(Long id) {
        return taskRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("任务不存在: " + id));
    }

    /**
     * 获取项目的所有任务
     */
    public List<Task> getTasksByProject(Long projectId) {
        return taskRepository.findByProjectId(projectId);
    }

    // 私有辅助方法

    private void validateTask(Task task) {
        if (task.getName() == null || task.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("任务名称不能为空");
        }
        if (task.getProject() == null) {
            throw new IllegalArgumentException("任务必须关联项目");
        }
        // TODO: 添加更多验证规则
    }

    private void updateTaskFields(Task existing, Task updated) {
        if (updated.getName() != null) {
            existing.setName(updated.getName());
        }
        if (updated.getDescription() != null) {
            existing.setDescription(updated.getDescription());
        }
        if (updated.getStartDate() != null) {
            existing.setStartDate(updated.getStartDate());
        }
        if (updated.getEndDate() != null) {
            existing.setEndDate(updated.getEndDate());
        }
        if (updated.getDuration() != null) {
            existing.setDuration(updated.getDuration());
        }
        if (updated.getStatus() != null) {
            existing.setStatus(updated.getStatus());
        }
        if (updated.getCost() != null) {
            existing.setCost(updated.getCost());
        }
        if (updated.getQuantity() != null) {
            existing.setQuantity(updated.getQuantity());
        }
        if (updated.getParent() != null) {
            existing.setParent(updated.getParent());
        }
    }
} 