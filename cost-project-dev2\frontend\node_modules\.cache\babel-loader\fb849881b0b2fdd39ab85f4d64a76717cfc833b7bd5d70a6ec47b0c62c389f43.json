{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport ReactIcon from \"./IconBase\";\nimport { normalizeTwoToneColors } from \"../utils\";\nexport function setTwoToneColor(twoToneColor) {\n  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),\n    _normalizeTwoToneColo2 = _slicedToArray(_normalizeTwoToneColo, 2),\n    primaryColor = _normalizeTwoToneColo2[0],\n    secondaryColor = _normalizeTwoToneColo2[1];\n  return ReactIcon.setTwoToneColors({\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor\n  });\n}\nexport function getTwoToneColor() {\n  var colors = ReactIcon.getTwoToneColors();\n  if (!colors.calculated) {\n    return colors.primaryColor;\n  }\n  return [colors.primaryColor, colors.secondaryColor];\n}", "map": {"version": 3, "names": ["_slicedToArray", "ReactIcon", "normalizeTwoToneColors", "setTwoToneColor", "twoToneColor", "_normalizeTwoToneColo", "_normalizeTwoToneColo2", "primaryColor", "secondaryColor", "setTwoToneColors", "getTwoToneColor", "colors", "getTwoToneColors", "calculated"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/components/twoTonePrimaryColor.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport ReactIcon from \"./IconBase\";\nimport { normalizeTwoToneColors } from \"../utils\";\nexport function setTwoToneColor(twoToneColor) {\n  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),\n    _normalizeTwoToneColo2 = _slicedToArray(_normalizeTwoToneColo, 2),\n    primaryColor = _normalizeTwoToneColo2[0],\n    secondaryColor = _normalizeTwoToneColo2[1];\n  return ReactIcon.setTwoToneColors({\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor\n  });\n}\nexport function getTwoToneColor() {\n  var colors = ReactIcon.getTwoToneColors();\n  if (!colors.calculated) {\n    return colors.primaryColor;\n  }\n  return [colors.primaryColor, colors.secondaryColor];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,sBAAsB,QAAQ,UAAU;AACjD,OAAO,SAASC,eAAeA,CAACC,YAAY,EAAE;EAC5C,IAAIC,qBAAqB,GAAGH,sBAAsB,CAACE,YAAY,CAAC;IAC9DE,sBAAsB,GAAGN,cAAc,CAACK,qBAAqB,EAAE,CAAC,CAAC;IACjEE,YAAY,GAAGD,sBAAsB,CAAC,CAAC,CAAC;IACxCE,cAAc,GAAGF,sBAAsB,CAAC,CAAC,CAAC;EAC5C,OAAOL,SAAS,CAACQ,gBAAgB,CAAC;IAChCF,YAAY,EAAEA,YAAY;IAC1BC,cAAc,EAAEA;EAClB,CAAC,CAAC;AACJ;AACA,OAAO,SAASE,eAAeA,CAAA,EAAG;EAChC,IAAIC,MAAM,GAAGV,SAAS,CAACW,gBAAgB,CAAC,CAAC;EACzC,IAAI,CAACD,MAAM,CAACE,UAAU,EAAE;IACtB,OAAOF,MAAM,CAACJ,YAAY;EAC5B;EACA,OAAO,CAACI,MAAM,CAACJ,YAAY,EAAEI,MAAM,CAACH,cAAc,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}