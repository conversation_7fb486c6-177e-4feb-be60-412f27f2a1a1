package com.costproject.controller;

import com.costproject.entity.BillItem;
import com.costproject.service.BillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/bills")
@Api(tags = "清单管理接口")
public class BillController {

    private static final Logger log = LoggerFactory.getLogger(BillController.class);
    private final BillService billService;
    
    public BillController(BillService billService) {
        this.billService = billService;
    }

    @PostMapping
    @ApiOperation("创建清单项")
    public ResponseEntity<BillItem> createBillItem(
            @ApiParam(value = "清单项信息", required = true)
            @Valid @RequestBody BillItem billItem) {
        return ResponseEntity.ok(billService.createBillItem(billItem));
    }

    @PutMapping("/{id}")
    @ApiOperation("更新清单项")
    public ResponseEntity<BillItem> updateBillItem(
            @ApiParam(value = "清单项ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "清单项信息", required = true)
            @Valid @RequestBody BillItem billItem) {
        return ResponseEntity.ok(billService.updateBillItem(id, billItem));
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除清单项")
    public ResponseEntity<Void> deleteBillItem(
            @ApiParam(value = "清单项ID", required = true)
            @PathVariable Long id) {
        billService.deleteBillItem(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}")
    @ApiOperation("获取清单项详情")
    public ResponseEntity<BillItem> getBillItem(
            @ApiParam(value = "清单项ID", required = true)
            @PathVariable Long id) {
        return ResponseEntity.ok(billService.getBillItem(id));
    }

    @GetMapping
    @ApiOperation("查询清单列表")
    public ResponseEntity<List<BillItem>> searchBillItems(
            @ApiParam(value = "关键词")
            @RequestParam(required = false) String keyword,
            @ApiParam(value = "章节")
            @RequestParam(required = false) String chapter) {
        return ResponseEntity.ok(billService.searchBillItems(keyword, chapter));
    }

    @PostMapping("/{billItemId}/quotas/{quotaItemId}")
    @ApiOperation("关联定额项")
    public ResponseEntity<Void> linkQuotaItem(
            @ApiParam(value = "清单项ID", required = true)
            @PathVariable Long billItemId,
            @ApiParam(value = "定额项ID", required = true)
            @PathVariable Long quotaItemId) {
        billService.linkQuotaItem(billItemId, quotaItemId);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{billItemId}/quotas/{quotaItemId}")
    @ApiOperation("解除定额关联")
    public ResponseEntity<Void> unlinkQuotaItem(
            @ApiParam(value = "清单项ID", required = true)
            @PathVariable Long billItemId,
            @ApiParam(value = "定额项ID", required = true)
            @PathVariable Long quotaItemId) {
        billService.unlinkQuotaItem(billItemId, quotaItemId);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{billItemId}/tasks/{taskId}")
    @ApiOperation("关联任务")
    public ResponseEntity<Void> linkTask(
            @ApiParam(value = "清单项ID", required = true)
            @PathVariable Long billItemId,
            @ApiParam(value = "任务ID", required = true)
            @PathVariable Long taskId) {
        billService.linkTask(billItemId, taskId);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/import/excel")
    @ApiOperation("从Excel导入清单")
    public ResponseEntity<List<BillItem>> importFromExcel(
            @ApiParam(value = "Excel文件", required = true)
            @RequestParam("file") MultipartFile file) {
        try {
            return ResponseEntity.ok(billService.importFromExcel(file));
        } catch (Exception e) {
            log.error("导入Excel失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/export")
    @ApiOperation("导出清单到Excel")
    public ResponseEntity<byte[]> exportToExcel(
            @ApiParam(value = "清单项ID列表")
            @RequestParam(required = false) List<Long> ids) {
        try {
            List<BillItem> billItems = ids != null && !ids.isEmpty() ?
                billService.searchBillItems(null, null).stream()
                    .filter(item -> ids.contains(item.getId()))
                    .collect(java.util.stream.Collectors.toList()) :
                billService.searchBillItems(null, null);

            java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
            billService.exportToExcel(billItems).write(baos);
            
            return ResponseEntity.ok()
                .header("Content-Type", "application/vnd.ms-excel")
                .header("Content-Disposition", "attachment; filename=bills.xlsx")
                .body(baos.toByteArray());
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/calculate-cost")
    @ApiOperation("计算清单合价")
    public ResponseEntity<Map<String, Object>> calculateTotalCost(
            @ApiParam(value = "清单项ID列表", required = true)
            @RequestParam List<Long> ids) {
        Map<String, java.math.BigDecimal> costBreakdown = billService.calculateTotalCost(ids);
        Map<String, Object> result = new java.util.HashMap<>();
        costBreakdown.forEach((key, value) -> result.put(key, value));
        return ResponseEntity.ok(result);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleException(Exception e) {
        log.error("处理请求时发生错误", e);
        return ResponseEntity.badRequest().body(e.getMessage());
    }
}