package com.costproject.controller;

import com.costproject.entity.Project;
import com.costproject.entity.Task;
import com.costproject.service.ProjectService;
import com.costproject.service.TaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/projects")
@Api(tags = "项目管理")
public class ProjectController {

    private static final Logger log = LoggerFactory.getLogger(ProjectController.class);

    private final ProjectService projectService;
    private final TaskService taskService;

    public ProjectController(ProjectService projectService, TaskService taskService) {
        this.projectService = projectService;
        this.taskService = taskService;
    }

    @PostMapping
    @ApiOperation("创建项目")
    public ResponseEntity<Project> createProject(
            @ApiParam(value = "项目信息", required = true)
            @Valid @RequestBody Project project) {
        try {
            Project createdProject = projectService.createProject(project);
            return ResponseEntity.ok(createdProject);
        } catch (Exception e) {
            log.error("创建项目失败", e);
            throw e;
        }
    }

    @PutMapping("/{id}")
    @ApiOperation("更新项目")
    public ResponseEntity<Project> updateProject(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long id,
            @ApiParam(value = "项目信息", required = true)
            @Valid @RequestBody Project project) {
        try {
            Project updatedProject = projectService.updateProject(id, project);
            return ResponseEntity.ok(updatedProject);
        } catch (Exception e) {
            log.error("更新项目失败", e);
            throw e;
        }
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除项目")
    public ResponseEntity<Void> deleteProject(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long id) {
        try {
            projectService.deleteProject(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("删除项目失败", e);
            throw e;
        }
    }

    @GetMapping("/{id}")
    @ApiOperation("获取项目详情")
    public ResponseEntity<Project> getProject(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long id) {
        try {
            Project project = projectService.getProject(id);
            return ResponseEntity.ok(project);
        } catch (Exception e) {
            log.error("获取项目详情失败", e);
            throw e;
        }
    }

    @GetMapping
    @ApiOperation("获取项目列表")
    public ResponseEntity<Page<Project>> getProjects(
            @ApiParam(value = "页码", defaultValue = "0")
            @RequestParam(defaultValue = "0") int page,
            @ApiParam(value = "每页大小", defaultValue = "10")
            @RequestParam(defaultValue = "10") int size,
            @ApiParam(value = "排序字段", defaultValue = "id")
            @RequestParam(defaultValue = "id") String sortField,
            @ApiParam(value = "排序方向", defaultValue = "asc")
            @RequestParam(defaultValue = "asc") String sortOrder,
            @ApiParam(value = "搜索关键词")
            @RequestParam(required = false) String keyword) {
        try {
            Sort.Direction direction = "desc".equalsIgnoreCase(sortOrder) ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortField));
            
            Page<Project> projects = projectService.getProjects(pageable, keyword);
            return ResponseEntity.ok(projects);
        } catch (Exception e) {
            log.error("获取项目列表失败", e);
            throw e;
        }
    }

    @GetMapping("/{projectId}/tasks")
    @ApiOperation("获取项目任务列表")
    public ResponseEntity<List<Task>> getProjectTasks(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        try {
            List<Task> tasks = taskService.getTasksByProject(projectId);
            return ResponseEntity.ok(tasks);
        } catch (Exception e) {
            log.error("获取项目任务失败", e);
            throw e;
        }
    }

    @PostMapping("/{projectId}/tasks")
    @ApiOperation("创建项目任务")
    public ResponseEntity<Task> createTask(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId,
            @ApiParam(value = "任务信息", required = true)
            @Valid @RequestBody Task task) {
        try {
            task.setProject(projectService.getProject(projectId));
            Task createdTask = taskService.createTask(task);
            return ResponseEntity.ok(createdTask);
        } catch (Exception e) {
            log.error("创建任务失败", e);
            throw e;
        }
    }

    @PutMapping("/{projectId}/tasks/{taskId}")
    @ApiOperation("更新项目任务")
    public ResponseEntity<Task> updateTask(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId,
            @ApiParam(value = "任务ID", required = true)
            @PathVariable Long taskId,
            @ApiParam(value = "任务信息", required = true)
            @Valid @RequestBody Task task) {
        try {
            Task updatedTask = taskService.updateTask(taskId, task);
            return ResponseEntity.ok(updatedTask);
        } catch (Exception e) {
            log.error("更新任务失败", e);
            throw e;
        }
    }

    @DeleteMapping("/{projectId}/tasks/{taskId}")
    @ApiOperation("删除项目任务")
    public ResponseEntity<Void> deleteTask(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId,
            @ApiParam(value = "任务ID", required = true)
            @PathVariable Long taskId) {
        try {
            taskService.deleteTask(taskId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("删除任务失败", e);
            throw e;
        }
    }

    @GetMapping("/statistics")
    @ApiOperation("获取项目统计信息")
    public ResponseEntity<Map<String, Object>> getProjectStatistics() {
        try {
            Map<String, Object> statistics = projectService.getProjectStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取项目统计信息失败", e);
            throw e;
        }
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleException(Exception e) {
        log.error("处理项目请求时发生错误", e);
        return ResponseEntity.badRequest().body(e.getMessage());
    }
} 