{"ast": null, "code": "/**\n * 类似 lodash.flow 的方法\n * @param flows\n */\nexport function flow(...flows) {\n  return param => {\n    return flows.reduce((result, f) => {\n      return f(result);\n    }, param);\n  };\n}", "map": {"version": 3, "names": ["flow", "flows", "param", "reduce", "result", "f"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\g2\\src\\utils\\flow.ts"], "sourcesContent": ["type FlowFunction<P> = (param: P) => P;\n\n/**\n * 类似 lodash.flow 的方法\n * @param flows\n */\nexport function flow<P>(...flows: FlowFunction<P>[]): FlowFunction<P> {\n  return (param: P) => {\n    return flows.reduce((result: P, f: FlowFunction<P>) => {\n      return f(result);\n    }, param);\n  };\n}\n"], "mappings": "AAEA;;;;AAIA,OAAM,SAAUA,IAAIA,CAAI,GAAGC,KAAwB;EACjD,OAAQC,KAAQ,IAAI;IAClB,OAAOD,KAAK,CAACE,MAAM,CAAC,CAACC,MAAS,EAAEC,CAAkB,KAAI;MACpD,OAAOA,CAAC,CAACD,MAAM,CAAC;IAClB,CAAC,EAAEF,KAAK,CAAC;EACX,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}