"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _typeof = require("@babel/runtime/helpers/typeof");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.getCellProps = getCellProps;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _classnames = _interopRequireDefault(require("classnames"));
var React = _interopRequireWildcard(require("react"));
var _Cell = _interopRequireDefault(require("../Cell"));
var _TableContext = require("../context/TableContext");
var _useRenderTimes = _interopRequireDefault(require("../hooks/useRenderTimes"));
var _useRowInfo = _interopRequireDefault(require("../hooks/useRowInfo"));
var _ExpandedRow = _interopRequireDefault(require("./ExpandedRow"));
var _expandUtil = require("../utils/expandUtil");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
// ==================================================================================
// ==                                 getCellProps                                 ==
// ==================================================================================
function getCellProps(rowInfo, column, colIndex, indent, index) {
  var _column$onCell;
  var rowKeys = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : [];
  var expandedRowOffset = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : 0;
  var record = rowInfo.record,
    prefixCls = rowInfo.prefixCls,
    columnsKey = rowInfo.columnsKey,
    fixedInfoList = rowInfo.fixedInfoList,
    expandIconColumnIndex = rowInfo.expandIconColumnIndex,
    nestExpandable = rowInfo.nestExpandable,
    indentSize = rowInfo.indentSize,
    expandIcon = rowInfo.expandIcon,
    expanded = rowInfo.expanded,
    hasNestChildren = rowInfo.hasNestChildren,
    onTriggerExpand = rowInfo.onTriggerExpand,
    expandable = rowInfo.expandable,
    expandedKeys = rowInfo.expandedKeys;
  var key = columnsKey[colIndex];
  var fixedInfo = fixedInfoList[colIndex];

  // ============= Used for nest expandable =============
  var appendCellNode;
  if (colIndex === (expandIconColumnIndex || 0) && nestExpandable) {
    appendCellNode = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("span", {
      style: {
        paddingLeft: "".concat(indentSize * indent, "px")
      },
      className: "".concat(prefixCls, "-row-indent indent-level-").concat(indent)
    }), expandIcon({
      prefixCls: prefixCls,
      expanded: expanded,
      expandable: hasNestChildren,
      record: record,
      onExpand: onTriggerExpand
    }));
  }
  var additionalCellProps = ((_column$onCell = column.onCell) === null || _column$onCell === void 0 ? void 0 : _column$onCell.call(column, record, index)) || {};

  // Expandable row has offset
  if (expandedRowOffset) {
    var _additionalCellProps$ = additionalCellProps.rowSpan,
      rowSpan = _additionalCellProps$ === void 0 ? 1 : _additionalCellProps$;

    // For expandable row with rowSpan,
    // We should increase the rowSpan if the row is expanded
    if (expandable && rowSpan && colIndex < expandedRowOffset) {
      var currentRowSpan = rowSpan;
      for (var i = index; i < index + rowSpan; i += 1) {
        var rowKey = rowKeys[i];
        if (expandedKeys.has(rowKey)) {
          currentRowSpan += 1;
        }
      }
      additionalCellProps.rowSpan = currentRowSpan;
    }
  }
  return {
    key: key,
    fixedInfo: fixedInfo,
    appendCellNode: appendCellNode,
    additionalCellProps: additionalCellProps
  };
}

// ==================================================================================
// ==                                 getCellProps                                 ==
// ==================================================================================
function BodyRow(props) {
  if (process.env.NODE_ENV !== 'production') {
    (0, _useRenderTimes.default)(props);
  }
  var className = props.className,
    style = props.style,
    record = props.record,
    index = props.index,
    renderIndex = props.renderIndex,
    rowKey = props.rowKey,
    rowKeys = props.rowKeys,
    _props$indent = props.indent,
    indent = _props$indent === void 0 ? 0 : _props$indent,
    RowComponent = props.rowComponent,
    cellComponent = props.cellComponent,
    scopeCellComponent = props.scopeCellComponent,
    expandedRowInfo = props.expandedRowInfo;
  var rowInfo = (0, _useRowInfo.default)(record, rowKey, index, indent);
  var prefixCls = rowInfo.prefixCls,
    flattenColumns = rowInfo.flattenColumns,
    expandedRowClassName = rowInfo.expandedRowClassName,
    expandedRowRender = rowInfo.expandedRowRender,
    rowProps = rowInfo.rowProps,
    expanded = rowInfo.expanded,
    rowSupportExpand = rowInfo.rowSupportExpand;

  // Force render expand row if expanded before
  var expandedRef = React.useRef(false);
  expandedRef.current || (expandedRef.current = expanded);
  if (process.env.NODE_ENV !== 'production') {
    (0, _useRenderTimes.default)(props);
  }

  // 若没有 expandedRowRender 参数, 将使用 baseRowNode 渲染 Children
  // 此时如果 level > 1 则说明是 expandedRow, 一样需要附加 computedExpandedRowClassName
  var expandedClsName = (0, _expandUtil.computedExpandedClassName)(expandedRowClassName, record, index, indent);

  // ======================== Base tr row ========================
  var baseRowNode = /*#__PURE__*/React.createElement(RowComponent, (0, _extends2.default)({}, rowProps, {
    "data-row-key": rowKey,
    className: (0, _classnames.default)(className, "".concat(prefixCls, "-row"), "".concat(prefixCls, "-row-level-").concat(indent), rowProps === null || rowProps === void 0 ? void 0 : rowProps.className, (0, _defineProperty2.default)({}, expandedClsName, indent >= 1)),
    style: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, style), rowProps === null || rowProps === void 0 ? void 0 : rowProps.style)
  }), flattenColumns.map(function (column, colIndex) {
    var render = column.render,
      dataIndex = column.dataIndex,
      columnClassName = column.className;
    var _getCellProps = getCellProps(rowInfo, column, colIndex, indent, index, rowKeys, expandedRowInfo === null || expandedRowInfo === void 0 ? void 0 : expandedRowInfo.offset),
      key = _getCellProps.key,
      fixedInfo = _getCellProps.fixedInfo,
      appendCellNode = _getCellProps.appendCellNode,
      additionalCellProps = _getCellProps.additionalCellProps;
    return /*#__PURE__*/React.createElement(_Cell.default, (0, _extends2.default)({
      className: columnClassName,
      ellipsis: column.ellipsis,
      align: column.align,
      scope: column.rowScope,
      component: column.rowScope ? scopeCellComponent : cellComponent,
      prefixCls: prefixCls,
      key: key,
      record: record,
      index: index,
      renderIndex: renderIndex,
      dataIndex: dataIndex,
      render: render,
      shouldCellUpdate: column.shouldCellUpdate
    }, fixedInfo, {
      appendNode: appendCellNode,
      additionalProps: additionalCellProps
    }));
  }));

  // ======================== Expand Row =========================
  var expandRowNode;
  if (rowSupportExpand && (expandedRef.current || expanded)) {
    var expandContent = expandedRowRender(record, index, indent + 1, expanded);
    expandRowNode = /*#__PURE__*/React.createElement(_ExpandedRow.default, {
      expanded: expanded,
      className: (0, _classnames.default)("".concat(prefixCls, "-expanded-row"), "".concat(prefixCls, "-expanded-row-level-").concat(indent + 1), expandedClsName),
      prefixCls: prefixCls,
      component: RowComponent,
      cellComponent: cellComponent,
      colSpan: expandedRowInfo ? expandedRowInfo.colSpan : flattenColumns.length,
      stickyOffset: expandedRowInfo === null || expandedRowInfo === void 0 ? void 0 : expandedRowInfo.sticky,
      isEmpty: false
    }, expandContent);
  }
  return /*#__PURE__*/React.createElement(React.Fragment, null, baseRowNode, expandRowNode);
}
if (process.env.NODE_ENV !== 'production') {
  BodyRow.displayName = 'BodyRow';
}
var _default = exports.default = (0, _TableContext.responseImmutable)(BodyRow);