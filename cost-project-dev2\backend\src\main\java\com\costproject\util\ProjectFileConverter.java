package com.costproject.util;

import lombok.extern.slf4j.Slf4j;
import net.sf.mpxj.*;
import net.sf.mpxj.writer.ProjectWriter;
import net.sf.mpxj.writer.ProjectWriterUtility;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Component
public class ProjectFileConverter {

    /**
     * 验证文件格式
     * @param filename 文件名
     * @return 是否支持该格式
     */
    public boolean isSupportedFormat(String filename) {
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        return switch (extension) {
            case "mpp", "mpx", "xml", "xer", "pmxml", "pp" -> true;
            default -> false;
        };
    }

    /**
     * 转换项目数据为标准格式
     * @param project MPXJ项目对象
     * @return 标准格式的项目数据
     */
    public Map<String, Object> convertToStandardFormat(ProjectFile project) {
        Map<String, Object> standardData = new HashMap<>();
        
        // 项目基本信息
        standardData.put("projectInfo", extractProjectInfo(project));
        
        // 任务信息
        standardData.put("tasks", extractTasks(project));
        
        // 资源信息
        standardData.put("resources", extractResources(project));
        
        // 日历信息
        standardData.put("calendars", extractCalendars(project));
        
        return standardData;
    }

    /**
     * 提取项目基本信息
     */
    private Map<String, Object> extractProjectInfo(ProjectFile project) {
        ProjectProperties props = project.getProjectProperties();
        Map<String, Object> info = new HashMap<>();
        
        info.put("name", props.getName());
        info.put("startDate", props.getStartDate());
        info.put("finishDate", props.getFinishDate());
        info.put("status", props.getProjectTitle());
        info.put("manager", props.getManager());
        info.put("company", props.getCompany());
        info.put("comments", props.getComments());
        
        return info;
    }

    /**
     * 提取任务信息
     */
    private List<Map<String, Object>> extractTasks(ProjectFile project) {
        List<Map<String, Object>> tasks = new ArrayList<>();
        
        for (Task task : project.getTasks()) {
            if (task != null && task.getName() != null) {
                Map<String, Object> taskData = new HashMap<>();
                
                // 基本信息
                taskData.put("id", task.getID());
                taskData.put("name", task.getName());
                taskData.put("wbs", task.getWBS());
                taskData.put("outlineLevel", task.getOutlineLevel());
                taskData.put("outlineNumber", task.getOutlineNumber());
                
                // 时间信息
                taskData.put("start", task.getStart());
                taskData.put("finish", task.getFinish());
                taskData.put("duration", task.getDuration());
                taskData.put("actualStart", task.getActualStart());
                taskData.put("actualFinish", task.getActualFinish());
                taskData.put("actualDuration", task.getActualDuration());
                
                // 进度信息
                taskData.put("percentageComplete", task.getPercentageComplete());
                taskData.put("actualWork", task.getActualWork());
                taskData.put("remainingWork", task.getRemainingWork());
                
                // 成本信息
                taskData.put("cost", task.getCost());
                taskData.put("actualCost", task.getActualCost());
                taskData.put("remainingCost", task.getRemainingCost());
                
                // 约束信息
                taskData.put("constraintType", task.getConstraintType());
                taskData.put("constraintDate", task.getConstraintDate());
                taskData.put("deadline", task.getDeadline());
                
                // 关系信息
                taskData.put("predecessors", extractRelations(task.getPredecessors()));
                taskData.put("successors", extractRelations(task.getSuccessors()));
                
                tasks.add(taskData);
            }
        }
        
        return tasks;
    }

    /**
     * 提取资源信息
     */
    private List<Map<String, Object>> extractResources(ProjectFile project) {
        List<Map<String, Object>> resources = new ArrayList<>();
        
        for (Resource resource : project.getResources()) {
            if (resource != null && resource.getName() != null) {
                Map<String, Object> resourceData = new HashMap<>();
                
                // 基本信息
                resourceData.put("id", resource.getID());
                resourceData.put("name", resource.getName());
                resourceData.put("type", resource.getType());
                resourceData.put("initials", resource.getInitials());
                resourceData.put("group", resource.getGroup());
                
                // 工作信息
                resourceData.put("maxUnits", resource.getMaxUnits());
                resourceData.put("standardRate", resource.getStandardRate());
                resourceData.put("overtimeRate", resource.getOvertimeRate());
                resourceData.put("costPerUse", resource.getCostPerUse());
                
                // 日历信息
                if (resource.getResourceCalendar() != null) {
                    resourceData.put("calendar", resource.getResourceCalendar().getName());
                }
                
                resources.add(resourceData);
            }
        }
        
        return resources;
    }

    /**
     * 提取日历信息
     */
    private List<Map<String, Object>> extractCalendars(ProjectFile project) {
        List<Map<String, Object>> calendars = new ArrayList<>();
        
        for (ProjectCalendar calendar : project.getCalendars()) {
            if (calendar != null && calendar.getName() != null) {
                Map<String, Object> calendarData = new HashMap<>();
                
                // 基本信息
                calendarData.put("name", calendar.getName());
                calendarData.put("isBaseCalendar", calendar.isDerived());
                
                // 工作时间
                List<Map<String, Object>> workingHours = new ArrayList<>();
                for (ProjectCalendarHours hours : calendar.getHours()) {
                    if (hours != null) {
                        Map<String, Object> hoursData = new HashMap<>();
                        hoursData.put("dayType", hours.getDay());
                        hoursData.put("intervals", hours.toString());
                        workingHours.add(hoursData);
                    }
                }
                calendarData.put("workingHours", workingHours);
                
                // 例外日期
                List<Map<String, Object>> exceptions = new ArrayList<>();
                for (ProjectCalendarException exception : calendar.getCalendarExceptions()) {
                    if (exception != null) {
                        Map<String, Object> exceptionData = new HashMap<>();
                        exceptionData.put("fromDate", exception.getFromDate());
                        exceptionData.put("toDate", exception.getToDate());
                        exceptionData.put("working", exception.getWorking());
                        exceptions.add(exceptionData);
                    }
                }
                calendarData.put("exceptions", exceptions);
                
                calendars.add(calendarData);
            }
        }
        
        return calendars;
    }

    /**
     * 提取关系信息
     */
    private List<Map<String, Object>> extractRelations(List<Relation> relations) {
        List<Map<String, Object>> relationList = new ArrayList<>();
        
        for (Relation relation : relations) {
            if (relation != null) {
                Map<String, Object> relationData = new HashMap<>();
                relationData.put("taskId", relation.getTargetTask().getID());
                relationData.put("type", relation.getType());
                relationData.put("lag", relation.getLag());
                relationList.add(relationData);
            }
        }
        
        return relationList;
    }

    /**
     * 转换日期格式
     */
    private LocalDateTime convertDate(Date date) {
        return date != null ? LocalDateTime.ofInstant(date.toInstant(), TimeZone.getDefault().toZoneId()) : null;
    }
}
