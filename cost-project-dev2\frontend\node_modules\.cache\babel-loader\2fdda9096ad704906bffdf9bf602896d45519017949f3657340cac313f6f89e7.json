{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FallOutlinedSvg from \"@ant-design/icons-svg/es/asn/FallOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FallOutlined = function FallOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FallOutlinedSvg\n  }));\n};\n\n/**![fall](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNS45IDgwNGwtMjQtMTk5LjJjLS44LTYuNi04LjktOS40LTEzLjYtNC43TDgyOSA2NTkuNSA1NTcuNyAzODguM2MtNi4zLTYuMi0xNi40LTYuMi0yMi42IDBMNDMzLjMgNDkwIDE1Ni42IDIxMy4zYTguMDMgOC4wMyAwIDAwLTExLjMgMGwtNDUgNDUuMmE4LjAzIDguMDMgMCAwMDAgMTEuM0w0MjIgNTkxLjdjNi4yIDYuMyAxNi40IDYuMyAyMi42IDBMNTQ2LjQgNDkwbDIyNi4xIDIyNi01OS4zIDU5LjNhOC4wMSA4LjAxIDAgMDA0LjcgMTMuNmwxOTkuMiAyNGM1LjEuNyA5LjUtMy43IDguOC04Ljl6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FallOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FallOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "FallOutlinedSvg", "AntdIcon", "FallOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/FallOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FallOutlinedSvg from \"@ant-design/icons-svg/es/asn/FallOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FallOutlined = function FallOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FallOutlinedSvg\n  }));\n};\n\n/**![fall](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNS45IDgwNGwtMjQtMTk5LjJjLS44LTYuNi04LjktOS40LTEzLjYtNC43TDgyOSA2NTkuNSA1NTcuNyAzODguM2MtNi4zLTYuMi0xNi40LTYuMi0yMi42IDBMNDMzLjMgNDkwIDE1Ni42IDIxMy4zYTguMDMgOC4wMyAwIDAwLTExLjMgMGwtNDUgNDUuMmE4LjAzIDguMDMgMCAwMDAgMTEuM0w0MjIgNTkxLjdjNi4yIDYuMyAxNi40IDYuMyAyMi42IDBMNTQ2LjQgNDkwbDIyNi4xIDIyNi01OS4zIDU5LjNhOC4wMSA4LjAxIDAgMDA0LjcgMTMuNmwxOTkuMiAyNGM1LjEuNyA5LjUtMy43IDguOC04Ljl6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FallOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FallOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,YAAY,CAAC;AACzD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,cAAc;AACtC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}