package com.costproject.controller;

import com.costproject.service.CostAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/cost-analysis")
@Api(tags = "成本分析接口")
public class CostAnalysisController {

    private static final Logger log = LoggerFactory.getLogger(CostAnalysisController.class);

    private final CostAnalysisService costAnalysisService;
    
    public CostAnalysisController(CostAnalysisService costAnalysisService) {
        this.costAnalysisService = costAnalysisService;
    }

    @GetMapping("/projects/{projectId}/summary")
    @ApiOperation("生成项目成本汇总报告")
    public ResponseEntity<Map<String, Object>> generateProjectCostSummary(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        return ResponseEntity.ok(costAnalysisService.generateProjectCostSummary(projectId));
    }

    @GetMapping("/projects/{projectId}/trend")
    @ApiOperation("生成成本趋势分析")
    public ResponseEntity<Map<String, Object>> analyzeCostTrend(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId,
            @ApiParam(value = "时间间隔（day/week/month/quarter）", required = true)
            @RequestParam String interval) {
        return ResponseEntity.ok(costAnalysisService.analyzeCostTrend(projectId, interval));
    }

    @GetMapping("/projects/{projectId}/resource-cost")
    @ApiOperation("生成资源成本分析")
    public ResponseEntity<Map<String, Object>> analyzeResourceCost(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        return ResponseEntity.ok(costAnalysisService.analyzeResourceCost(projectId));
    }

    @GetMapping("/projects/{projectId}/alerts")
    @ApiOperation("生成成本预警报告")
    public ResponseEntity<List<Map<String, Object>>> generateCostAlerts(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        return ResponseEntity.ok(costAnalysisService.generateCostAlerts(projectId));
    }

    @GetMapping("/projects/{projectId}/optimization")
    @ApiOperation("生成成本优化建议")
    public ResponseEntity<List<Map<String, Object>>> generateCostOptimizationSuggestions(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        return ResponseEntity.ok(costAnalysisService.generateCostOptimizationSuggestions(projectId));
    }

    @GetMapping("/projects/{projectId}/cost-composition")
    @ApiOperation("分析成本构成")
    public ResponseEntity<Map<String, Object>> analyzeCostComposition(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        Map<String, Object> summary = costAnalysisService.generateProjectCostSummary(projectId);
        return ResponseEntity.ok((Map<String, Object>) summary.get("costComposition"));
    }

    @GetMapping("/projects/{projectId}/key-metrics")
    @ApiOperation("获取关键成本指标")
    public ResponseEntity<Map<String, Object>> getKeyMetrics(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        Map<String, Object> summary = costAnalysisService.generateProjectCostSummary(projectId);
        return ResponseEntity.ok((Map<String, Object>) summary.get("keyMetrics"));
    }

    @GetMapping("/projects/{projectId}/cost-by-type")
    @ApiOperation("按类型统计成本")
    public ResponseEntity<Map<String, Object>> getCostByType(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        Map<String, Object> summary = costAnalysisService.generateProjectCostSummary(projectId);
        return ResponseEntity.ok((Map<String, Object>) summary.get("costByType"));
    }

    @GetMapping("/projects/{projectId}/accumulated-cost")
    @ApiOperation("获取累计成本")
    public ResponseEntity<Map<String, Object>> getAccumulatedCost(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        Map<String, Object> trend = costAnalysisService.analyzeCostTrend(projectId, "month");
        return ResponseEntity.ok((Map<String, Object>) trend.get("accumulatedCost"));
    }

    @GetMapping("/projects/{projectId}/growth-rate")
    @ApiOperation("获取成本增长率")
    public ResponseEntity<Map<String, Object>> getGrowthRate(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        Map<String, Object> trend = costAnalysisService.analyzeCostTrend(projectId, "month");
        return ResponseEntity.ok((Map<String, Object>) trend.get("growthRate"));
    }

    @GetMapping("/projects/{projectId}/resource-usage")
    @ApiOperation("获取资源使用情况")
    public ResponseEntity<Map<String, Object>> getResourceUsage(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        Map<String, Object> analysis = costAnalysisService.analyzeResourceCost(projectId);
        return ResponseEntity.ok((Map<String, Object>) analysis.get("resourceCost"));
    }

    @GetMapping("/projects/{projectId}/cost-percentage")
    @ApiOperation("获取成本占比")
    public ResponseEntity<Map<String, Object>> getCostPercentage(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        Map<String, Object> analysis = costAnalysisService.analyzeResourceCost(projectId);
        return ResponseEntity.ok((Map<String, Object>) analysis.get("costPercentage"));
    }

    @GetMapping("/projects/{projectId}/price-trend")
    @ApiOperation("获取价格趋势")
    public ResponseEntity<Map<String, Object>> getPriceTrend(
            @ApiParam(value = "项目ID", required = true)
            @PathVariable Long projectId) {
        Map<String, Object> analysis = costAnalysisService.analyzeResourceCost(projectId);
        return ResponseEntity.ok((Map<String, Object>) analysis.get("priceTrend"));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleException(Exception e) {
        log.error("处理请求时发生错误", e);
        return ResponseEntity.badRequest().body(e.getMessage());
    }
}
