-- 项目基础信息表
CREATE TABLE project_info (
    id SERIAL PRIMARY KEY,
    project_name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    location VARCHAR(100),
    daily_work_hours INTEGER DEFAULT 8,
    time_zone VARCHAR(50) DEFAULT 'Asia/Shanghai',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 工作日历表
CREATE TABLE work_calendar (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES project_info(id),
    calendar_date DATE NOT NULL,
    is_working_day BOOLEAN DEFAULT true,
    working_hours DECIMAL(4,2),
    note TEXT,
    UNIQUE (project_id, calendar_date)
);

-- 任务计划表
CREATE TABLE task (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES project_info(id),
    wbs_code VARCHAR(20) NOT NULL,
    description TEXT NOT NULL,
    unit VARCHAR(20),
    quantity DECIMAL(10,2),
    daily_output DECIMAL(10,2),
    duration INTEGER GENERATED ALWAYS AS (CEIL(quantity/NULLIF(daily_output,0))) STORED,
    start_date DATE,
    finish_date DATE,
    predecessor_id INTEGER REFERENCES task(id),
    relationship_type VARCHAR(2), -- FS, SS, FF, SF
    lag_days INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (project_id, wbs_code)
);

-- 项目清单表
CREATE TABLE project_list (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES project_info(id),
    chapter VARCHAR(50),
    code VARCHAR(12) NOT NULL,
    description TEXT NOT NULL,
    work_content TEXT,
    unit VARCHAR(20),
    quantity DECIMAL(10,2),
    unit_price DECIMAL(12,2),
    total_price DECIMAL(12,2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (project_id, code)
);

-- 项目定额表
CREATE TABLE project_quota (
    id SERIAL PRIMARY KEY,
    code VARCHAR(20) NOT NULL,
    name VARCHAR(100) NOT NULL,
    work_content TEXT,
    unit VARCHAR(20),
    unit_price DECIMAL(12,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (code)
);

-- 资源信息表
CREATE TABLE resource_info (
    id SERIAL PRIMARY KEY,
    code VARCHAR(20) NOT NULL,
    type VARCHAR(20), -- 人工/材料/机械
    name VARCHAR(100) NOT NULL,
    unit VARCHAR(20),
    base_price DECIMAL(12,2),
    market_price DECIMAL(12,2),
    price_date DATE,
    note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (code)
);

-- 资源成本明细表
CREATE TABLE resource_cost_detail (
    id SERIAL PRIMARY KEY,
    resource_id INTEGER REFERENCES resource_info(id),
    cost_type VARCHAR(50), -- 采购成本/运输成本/仓储成本等
    amount DECIMAL(12,2),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 任务-清单关联表
CREATE TABLE task_list_mapping (
    id SERIAL PRIMARY KEY,
    task_id INTEGER REFERENCES task(id),
    list_id INTEGER REFERENCES project_list(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (task_id, list_id)
);

-- 清单-定额关联表
CREATE TABLE list_quota_mapping (
    id SERIAL PRIMARY KEY,
    list_id INTEGER REFERENCES project_list(id),
    quota_id INTEGER REFERENCES project_quota(id),
    quantity DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (list_id, quota_id)
);

-- 定额-资源关联表
CREATE TABLE quota_resource_mapping (
    id SERIAL PRIMARY KEY,
    quota_id INTEGER REFERENCES project_quota(id),
    resource_id INTEGER REFERENCES resource_info(id),
    consumption DECIMAL(10,4), -- 消耗量
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (quota_id, resource_id)
);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有需要自动更新时间的表创建触发器
CREATE TRIGGER update_project_info_timestamp BEFORE UPDATE ON project_info FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_task_timestamp BEFORE UPDATE ON task FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_project_list_timestamp BEFORE UPDATE ON project_list FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_project_quota_timestamp BEFORE UPDATE ON project_quota FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_resource_info_timestamp BEFORE UPDATE ON resource_info FOR EACH ROW EXECUTE FUNCTION update_timestamp();
