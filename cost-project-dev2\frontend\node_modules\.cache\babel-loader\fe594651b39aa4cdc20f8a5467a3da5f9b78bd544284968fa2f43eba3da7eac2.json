{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { pathLengthFactory } from './path-length-factory';\n/**\n * Returns [x,y] coordinates of a point at a given length of a shape.\n */\nexport function getPointAtLength(pathInput, distance, options) {\n  return pathLengthFactory(pathInput, distance, __assign(__assign({}, options), {\n    bbox: false,\n    length: true\n  })).point;\n}", "map": {"version": 3, "names": ["pathLengthFactory", "getPointAtLength", "pathInput", "distance", "options", "__assign", "bbox", "length", "point"], "sources": ["path/util/get-point-at-length.ts"], "sourcesContent": [null], "mappings": ";AACA,SAASA,iBAAiB,QAAQ,uBAAuB;AAEzD;;;AAGA,OAAM,SAAUC,gBAAgBA,CAC9BC,SAA6B,EAC7BC,QAAgB,EAChBC,OAA2C;EAE3C,OAAOJ,iBAAiB,CAACE,SAAS,EAAEC,QAAQ,EAAAE,QAAA,CAAAA,QAAA,KAAOD,OAAO;IAAEE,IAAI,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAI,GAAG,CAACC,KAAK;AAChG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}