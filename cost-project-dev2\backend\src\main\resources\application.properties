# Server Configuration
server.port=8080
# server.servlet.context-path=/api

# Database Configuration
spring.datasource.url=*********************************************
spring.datasource.username=postgres
spring.datasource.password=postgres123
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Logging Configuration
logging.level.root=INFO
logging.level.com.costproject=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Swagger Configuration
springfox.documentation.swagger-ui.path=/swagger-ui.html
springfox.documentation.swagger.v2.path=/v2/api-docs

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Jackson Configuration
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=Asia/Shanghai

# Custom Application Properties
app.jwt.secret=your-secret-key
app.jwt.expiration=86400000
