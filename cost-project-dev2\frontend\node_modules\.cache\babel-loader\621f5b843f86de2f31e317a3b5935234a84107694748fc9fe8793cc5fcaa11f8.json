{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CalculatorOutlinedSvg from \"@ant-design/icons-svg/es/asn/CalculatorOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CalculatorOutlined = function CalculatorOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CalculatorOutlinedSvg\n  }));\n};\n\n/**![calculator](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI1MS4yIDM4N0gzMjB2NjguOGMwIDEuOCAxLjggMy4yIDQgMy4yaDQ4YzIuMiAwIDQtMS40IDQtMy4zVjM4N2g2OC44YzEuOCAwIDMuMi0xLjggMy4yLTR2LTQ4YzAtMi4yLTEuNC00LTMuMy00SDM3NnYtNjguOGMwLTEuOC0xLjgtMy4yLTQtMy4yaC00OGMtMi4yIDAtNCAxLjQtNCAzLjJWMzMxaC02OC44Yy0xLjggMC0zLjIgMS44LTMuMiA0djQ4YzAgMi4yIDEuNCA0IDMuMiA0em0zMjggMGgxOTMuNmMxLjggMCAzLjItMS44IDMuMi00di00OGMwLTIuMi0xLjQtNC0zLjMtNEg1NzkuMmMtMS44IDAtMy4yIDEuOC0zLjIgNHY0OGMwIDIuMiAxLjQgNCAzLjIgNHptMCAyNjVoMTkzLjZjMS44IDAgMy4yLTEuOCAzLjItNHYtNDhjMC0yLjItMS40LTQtMy4zLTRINTc5LjJjLTEuOCAwLTMuMiAxLjgtMy4yIDR2NDhjMCAyLjIgMS40IDQgMy4yIDR6bTAgMTA0aDE5My42YzEuOCAwIDMuMi0xLjggMy4yLTR2LTQ4YzAtMi4yLTEuNC00LTMuMy00SDU3OS4yYy0xLjggMC0zLjIgMS44LTMuMiA0djQ4YzAgMi4yIDEuNCA0IDMuMiA0em0tMTk1LjctODFsNjEuMi03NC45YzQuMy01LjIuNy0xMy4xLTUuOS0xMy4xSDM4OGMtMi4zIDAtNC41IDEtNS45IDIuOWwtMzQgNDEuNi0zNC00MS42YTcuODUgNy44NSAwIDAwLTUuOS0yLjloLTUwLjljLTYuNiAwLTEwLjIgNy45LTUuOSAxMy4xbDYxLjIgNzQuOS02Mi43IDc2LjhjLTQuNCA1LjItLjggMTMuMSA1LjggMTMuMWg1MC44YzIuMyAwIDQuNS0xIDUuOS0yLjlsMzUuNS00My41IDM1LjUgNDMuNWMxLjUgMS44IDMuNyAyLjkgNS45IDIuOWg1MC44YzYuNiAwIDEwLjItNy45IDUuOS0xMy4xTDM4My41IDY3NXpNODgwIDExMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tMzYgNzMySDE4MFYxODBoNjY0djY2NHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CalculatorOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CalculatorOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "CalculatorOutlinedSvg", "AntdIcon", "CalculatorOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/CalculatorOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CalculatorOutlinedSvg from \"@ant-design/icons-svg/es/asn/CalculatorOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CalculatorOutlined = function CalculatorOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CalculatorOutlinedSvg\n  }));\n};\n\n/**![calculator](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI1MS4yIDM4N0gzMjB2NjguOGMwIDEuOCAxLjggMy4yIDQgMy4yaDQ4YzIuMiAwIDQtMS40IDQtMy4zVjM4N2g2OC44YzEuOCAwIDMuMi0xLjggMy4yLTR2LTQ4YzAtMi4yLTEuNC00LTMuMy00SDM3NnYtNjguOGMwLTEuOC0xLjgtMy4yLTQtMy4yaC00OGMtMi4yIDAtNCAxLjQtNCAzLjJWMzMxaC02OC44Yy0xLjggMC0zLjIgMS44LTMuMiA0djQ4YzAgMi4yIDEuNCA0IDMuMiA0em0zMjggMGgxOTMuNmMxLjggMCAzLjItMS44IDMuMi00di00OGMwLTIuMi0xLjQtNC0zLjMtNEg1NzkuMmMtMS44IDAtMy4yIDEuOC0zLjIgNHY0OGMwIDIuMiAxLjQgNCAzLjIgNHptMCAyNjVoMTkzLjZjMS44IDAgMy4yLTEuOCAzLjItNHYtNDhjMC0yLjItMS40LTQtMy4zLTRINTc5LjJjLTEuOCAwLTMuMiAxLjgtMy4yIDR2NDhjMCAyLjIgMS40IDQgMy4yIDR6bTAgMTA0aDE5My42YzEuOCAwIDMuMi0xLjggMy4yLTR2LTQ4YzAtMi4yLTEuNC00LTMuMy00SDU3OS4yYy0xLjggMC0zLjIgMS44LTMuMiA0djQ4YzAgMi4yIDEuNCA0IDMuMiA0em0tMTk1LjctODFsNjEuMi03NC45YzQuMy01LjIuNy0xMy4xLTUuOS0xMy4xSDM4OGMtMi4zIDAtNC41IDEtNS45IDIuOWwtMzQgNDEuNi0zNC00MS42YTcuODUgNy44NSAwIDAwLTUuOS0yLjloLTUwLjljLTYuNiAwLTEwLjIgNy45LTUuOSAxMy4xbDYxLjIgNzQuOS02Mi43IDc2LjhjLTQuNCA1LjItLjggMTMuMSA1LjggMTMuMWg1MC44YzIuMyAwIDQuNS0xIDUuOS0yLjlsMzUuNS00My41IDM1LjUgNDMuNWMxLjUgMS44IDMuNyAyLjkgNS45IDIuOWg1MC44YzYuNiAwIDEwLjItNy45IDUuOS0xMy4xTDM4My41IDY3NXpNODgwIDExMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tMzYgNzMySDE4MFYxODBoNjY0djY2NHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CalculatorOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CalculatorOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,qBAAqB,MAAM,iDAAiD;AACnF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,kBAAkB,CAAC;AAC/D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,oBAAoB;AAC5C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}