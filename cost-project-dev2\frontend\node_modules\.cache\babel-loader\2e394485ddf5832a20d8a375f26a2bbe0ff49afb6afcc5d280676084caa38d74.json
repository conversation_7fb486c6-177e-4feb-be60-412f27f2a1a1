{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SignatureFilledSvg from \"@ant-design/icons-svg/es/asn/SignatureFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SignatureFilled = function SignatureFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SignatureFilledSvg\n  }));\n};\n\n/**![signature](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQ1LjcxIDc1MmMyIDAgNC0uMiA1Ljk4LS41TDMxOS45IDcyMmMxLjk5LS40IDMuODgtMS4zIDUuMjgtMi44bDQyMy45MS00MjMuODdhOS45MyA5LjkzIDAgMDAwLTE0LjA2TDU4Mi44OCAxMTQuOUM1ODEgMTEzIDU3OC41IDExMiA1NzUuODIgMTEycy01LjE4IDEtNy4wOCAyLjlMMTQ0LjgyIDUzOC43NmMtMS41IDEuNS0yLjQgMy4yOS0yLjggNS4yOGwtMjkuNSAxNjguMTdhMzMuNTkgMzMuNTkgMCAwMDkuMzcgMjkuODFjNi41OCA2LjQ4IDE0Ljk1IDkuOTcgMjMuODIgOS45N200NTMuMTItMTg0LjA3YzI3LjY5LTE0LjgxIDU3LjI5LTIwLjg1IDg1LjU0LTE1LjUyIDMyLjM3IDYuMSA1OS43MiAyNi41MyA3OC45NiA1OS40IDI5Ljk3IDUxLjIyIDIxLjY0IDEwMi4zNC0xOC40OCAxNDQuMjYtMTcuNTggMTguMzYtNDEuMDcgMzUuMDEtNzAgNTAuM2wtLjMuMTUuODYuMjZhMTQ3Ljg4IDE0Ny44OCAwIDAwNDEuNTQgNi4ybDEuMTcuMDFjNjEuMDcgMCAxMDAuOTgtMjIuMSAxMjUuMjgtNjcuODdhMzYgMzYgMCAwMTYzLjYgMzMuNzZDODY5LjcgODQ5LjEgODA0LjkgODg1IDcxOC4xMiA4ODVjLTQ3LjY5IDAtOTEuOTQtMTUuMDMtMTI4LjE5LTQxLjM2bC0xLjA1LS43OC0xLjM2LjQ3Yy00Ni4xOCAxNi05OC43NCAyOS45NS0xNTUuMzcgNDEuOTRsLTIuMjQuNDdhMTkzMS4xIDE5MzEuMSAwIDAxLTEzOS4xNiAyMy45NiAzNiAzNiAwIDExLTkuNS03MS4zOCAxODYwLjEgMTg2MC4xIDAgMDAxMzMuODQtMjMuMDRjNDIuOC05IDgzLTE5LjEzIDExOS4zNS0zMC4zNGwuMjQtLjA4LS40NC0uNjljLTE2LjQ2LTI2LjQ1LTI1Ljg2LTU1LjQzLTI2LjE0LTgzLjI0di0xLjNjMC00OS45IDM5LjU1LTEwNC4zMiA5MC43My0xMzEuN002NzEgNjIzLjE3Yy0xMC43NC0yLjAzLTI0LjEuNy0zOC4yMiA4LjI2LTI5LjU1IDE1LjgtNTIuNyA0Ny42NC01Mi43IDY4LjIgMCAxOC4yIDguOSA0MC4xNCAyNC43MSA1OS43M2wuMjQuMyAxLjIyLS41MmMzOS4xNy0xNi41OCA2OC40OS0zNC4yNyA4NS45My01Mi4xOGwuNjQtLjY3YzE4Ljc0LTE5LjU3IDIxLjM5LTM1Ljg0IDguMzYtNTguMS05LjA2LTE1LjQ3LTE5LjAzLTIyLjkyLTMwLjE4LTI1LjAyIiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SignatureFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SignatureFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "SignatureFilledSvg", "AntdIcon", "SignatureFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/SignatureFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SignatureFilledSvg from \"@ant-design/icons-svg/es/asn/SignatureFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SignatureFilled = function SignatureFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SignatureFilledSvg\n  }));\n};\n\n/**![signature](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQ1LjcxIDc1MmMyIDAgNC0uMiA1Ljk4LS41TDMxOS45IDcyMmMxLjk5LS40IDMuODgtMS4zIDUuMjgtMi44bDQyMy45MS00MjMuODdhOS45MyA5LjkzIDAgMDAwLTE0LjA2TDU4Mi44OCAxMTQuOUM1ODEgMTEzIDU3OC41IDExMiA1NzUuODIgMTEycy01LjE4IDEtNy4wOCAyLjlMMTQ0LjgyIDUzOC43NmMtMS41IDEuNS0yLjQgMy4yOS0yLjggNS4yOGwtMjkuNSAxNjguMTdhMzMuNTkgMzMuNTkgMCAwMDkuMzcgMjkuODFjNi41OCA2LjQ4IDE0Ljk1IDkuOTcgMjMuODIgOS45N200NTMuMTItMTg0LjA3YzI3LjY5LTE0LjgxIDU3LjI5LTIwLjg1IDg1LjU0LTE1LjUyIDMyLjM3IDYuMSA1OS43MiAyNi41MyA3OC45NiA1OS40IDI5Ljk3IDUxLjIyIDIxLjY0IDEwMi4zNC0xOC40OCAxNDQuMjYtMTcuNTggMTguMzYtNDEuMDcgMzUuMDEtNzAgNTAuM2wtLjMuMTUuODYuMjZhMTQ3Ljg4IDE0Ny44OCAwIDAwNDEuNTQgNi4ybDEuMTcuMDFjNjEuMDcgMCAxMDAuOTgtMjIuMSAxMjUuMjgtNjcuODdhMzYgMzYgMCAwMTYzLjYgMzMuNzZDODY5LjcgODQ5LjEgODA0LjkgODg1IDcxOC4xMiA4ODVjLTQ3LjY5IDAtOTEuOTQtMTUuMDMtMTI4LjE5LTQxLjM2bC0xLjA1LS43OC0xLjM2LjQ3Yy00Ni4xOCAxNi05OC43NCAyOS45NS0xNTUuMzcgNDEuOTRsLTIuMjQuNDdhMTkzMS4xIDE5MzEuMSAwIDAxLTEzOS4xNiAyMy45NiAzNiAzNiAwIDExLTkuNS03MS4zOCAxODYwLjEgMTg2MC4xIDAgMDAxMzMuODQtMjMuMDRjNDIuOC05IDgzLTE5LjEzIDExOS4zNS0zMC4zNGwuMjQtLjA4LS40NC0uNjljLTE2LjQ2LTI2LjQ1LTI1Ljg2LTU1LjQzLTI2LjE0LTgzLjI0di0xLjNjMC00OS45IDM5LjU1LTEwNC4zMiA5MC43My0xMzEuN002NzEgNjIzLjE3Yy0xMC43NC0yLjAzLTI0LjEuNy0zOC4yMiA4LjI2LTI5LjU1IDE1LjgtNTIuNyA0Ny42NC01Mi43IDY4LjIgMCAxOC4yIDguOSA0MC4xNCAyNC43MSA1OS43M2wuMjQuMyAxLjIyLS41MmMzOS4xNy0xNi41OCA2OC40OS0zNC4yNyA4NS45My01Mi4xOGwuNjQtLjY3YzE4Ljc0LTE5LjU3IDIxLjM5LTM1Ljg0IDguMzYtNTguMS05LjA2LTE1LjQ3LTE5LjAzLTIyLjkyLTMwLjE4LTI1LjAyIiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SignatureFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SignatureFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,eAAe,CAAC;AAC5D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,iBAAiB;AACzC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}