{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\cost-project-dev2\\\\frontend\\\\src\\\\pages\\\\cost\\\\CostAnalysis.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, Space, Select, Button, Alert, List, Typography, Divider, Table, Tag, Tooltip, Progress, DatePicker } from 'antd';\nimport { DollarOutlined, RiseOutlined, FallOutlined, AlertOutlined, BarChartOutlined, LineChartOutlined, PieChartOutlined, DownloadOutlined, TeamOutlined, ShoppingOutlined, ToolOutlined } from '@ant-design/icons';\nimport { Line, Bar, Pie, DualAxes } from '@ant-design/charts';\nimport axios from 'axios';\nimport moment from 'moment';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\nconst CostAnalysis = () => {\n  _s();\n  // 状态管理\n  const [loading, setLoading] = useState(false);\n  const [projectId, setProjectId] = useState(null);\n  const [dateRange, setDateRange] = useState([moment().subtract(30, 'days'), moment()]);\n  const [summary, setSummary] = useState(null);\n  const [trend, setTrend] = useState(null);\n  const [resourceCost, setResourceCost] = useState(null);\n  const [alerts, setAlerts] = useState([]);\n  const [suggestions, setSuggestions] = useState([]);\n\n  // 初始化加载\n  useEffect(() => {\n    if (projectId) {\n      fetchData();\n    }\n  }, [projectId, dateRange]);\n\n  // 获取所有数据\n  const fetchData = async () => {\n    setLoading(true);\n    try {\n      await Promise.all([fetchSummary(), fetchTrend(), fetchResourceCost(), fetchAlerts(), fetchSuggestions()]);\n    } catch (error) {\n      console.error('获取数据失败：', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取成本汇总\n  const fetchSummary = async () => {\n    const response = await axios.get(`/api/cost-analysis/projects/${projectId}/summary`);\n    setSummary(response.data);\n  };\n\n  // 获取成本趋势\n  const fetchTrend = async () => {\n    const response = await axios.get(`/api/cost-analysis/projects/${projectId}/trend`, {\n      params: {\n        interval: 'day',\n        startDate: dateRange[0].format('YYYY-MM-DD'),\n        endDate: dateRange[1].format('YYYY-MM-DD')\n      }\n    });\n    setTrend(response.data);\n  };\n\n  // 获取资源成本\n  const fetchResourceCost = async () => {\n    const response = await axios.get(`/api/cost-analysis/projects/${projectId}/resource-cost`);\n    setResourceCost(response.data);\n  };\n\n  // 获取预警信息\n  const fetchAlerts = async () => {\n    const response = await axios.get(`/api/cost-analysis/projects/${projectId}/alerts`);\n    setAlerts(response.data);\n  };\n\n  // 获取优化建议\n  const fetchSuggestions = async () => {\n    const response = await axios.get(`/api/cost-analysis/projects/${projectId}/optimization`);\n    setSuggestions(response.data);\n  };\n\n  // 成本总览卡片\n  const SummaryCards = () => /*#__PURE__*/_jsxDEV(Row, {\n    gutter: 16,\n    children: [/*#__PURE__*/_jsxDEV(Col, {\n      span: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u603B\\u6210\\u672C\",\n          value: summary === null || summary === void 0 ? void 0 : summary.totalCost,\n          precision: 2,\n          prefix: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 21\n          }, this),\n          suffix: \"\\u5143\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            type: (summary === null || summary === void 0 ? void 0 : summary.costTrend) > 0 ? 'danger' : 'success',\n            children: [(summary === null || summary === void 0 ? void 0 : summary.costTrend) > 0 ? /*#__PURE__*/_jsxDEV(RiseOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 41\n            }, this) : /*#__PURE__*/_jsxDEV(FallOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 60\n            }, this), Math.abs(summary === null || summary === void 0 ? void 0 : summary.costTrend), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \" \\u8F83\\u4E0A\\u6708\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      span: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u4EBA\\u5DE5\\u6210\\u672C\",\n          value: summary === null || summary === void 0 ? void 0 : summary.laborCost,\n          precision: 2,\n          prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 21\n          }, this),\n          suffix: \"\\u5143\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: summary === null || summary === void 0 ? void 0 : summary.laborCostPercentage,\n          size: \"small\",\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      span: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u6750\\u6599\\u6210\\u672C\",\n          value: summary === null || summary === void 0 ? void 0 : summary.materialCost,\n          precision: 2,\n          prefix: /*#__PURE__*/_jsxDEV(ShoppingOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 21\n          }, this),\n          suffix: \"\\u5143\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: summary === null || summary === void 0 ? void 0 : summary.materialCostPercentage,\n          size: \"small\",\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      span: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(Statistic, {\n          title: \"\\u673A\\u68B0\\u6210\\u672C\",\n          value: summary === null || summary === void 0 ? void 0 : summary.machineCost,\n          precision: 2,\n          prefix: /*#__PURE__*/_jsxDEV(ToolOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 21\n          }, this),\n          suffix: \"\\u5143\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: summary === null || summary === void 0 ? void 0 : summary.machineCostPercentage,\n          size: \"small\",\n          status: \"active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n\n  // 成本趋势图\n  const TrendChart = () => /*#__PURE__*/_jsxDEV(Card, {\n    title: \"\\u6210\\u672C\\u8D8B\\u52BF\\u5206\\u6790\",\n    children: /*#__PURE__*/_jsxDEV(DualAxes, {\n      data: [(trend === null || trend === void 0 ? void 0 : trend.costByPeriod) || [], (trend === null || trend === void 0 ? void 0 : trend.accumulatedCost) || []],\n      xField: \"date\",\n      yField: ['cost', 'accumulated'],\n      geometryOptions: [{\n        geometry: 'column',\n        color: '#5B8FF9',\n        columnWidthRatio: 0.4\n      }, {\n        geometry: 'line',\n        smooth: true,\n        color: '#5AD8A6'\n      }],\n      annotations: [{\n        type: 'text',\n        position: ['50%', '50%'],\n        content: loading ? '加载中...' : '',\n        style: {\n          fill: '#666',\n          fontSize: 14\n        }\n      }]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 5\n  }, this);\n\n  // 成本构成图\n  const CompositionChart = () => /*#__PURE__*/_jsxDEV(Card, {\n    title: \"\\u6210\\u672C\\u6784\\u6210\\u5206\\u6790\",\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Pie, {\n          data: (summary === null || summary === void 0 ? void 0 : summary.costComposition) || [],\n          angleField: \"value\",\n          colorField: \"type\",\n          radius: 0.8,\n          label: {\n            type: 'outer',\n            content: data => `${data.type}: ${(data.percent * 100).toFixed(1)}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(Bar, {\n          data: (resourceCost === null || resourceCost === void 0 ? void 0 : resourceCost.resourceCost) || [],\n          xField: \"value\",\n          yField: \"name\",\n          seriesField: \"type\",\n          isStack: true,\n          label: {\n            position: 'middle'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 220,\n    columnNumber: 5\n  }, this);\n\n  // 预警信息\n  const AlertSection = () => /*#__PURE__*/_jsxDEV(Card, {\n    title: \"\\u6210\\u672C\\u9884\\u8B66\",\n    children: /*#__PURE__*/_jsxDEV(List, {\n      dataSource: alerts,\n      renderItem: alert => /*#__PURE__*/_jsxDEV(List.Item, {\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(AlertOutlined, {\n            style: {\n              color: alert.level === 'high' ? '#f5222d' : '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: alert.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: alert.level === 'high' ? 'red' : 'orange',\n            children: alert.level === 'high' ? '高风险' : '中风险'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n\n  // 优化建议\n  const SuggestionSection = () => /*#__PURE__*/_jsxDEV(Card, {\n    title: \"\\u6210\\u672C\\u4F18\\u5316\\u5EFA\\u8BAE\",\n    children: /*#__PURE__*/_jsxDEV(List, {\n      dataSource: suggestions,\n      renderItem: suggestion => /*#__PURE__*/_jsxDEV(List.Item, {\n        children: [/*#__PURE__*/_jsxDEV(List.Item.Meta, {\n          title: suggestion.title,\n          description: suggestion.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"blue\",\n          children: [suggestion.potentialSaving, \"\\u5143\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Space, {\n    direction: \"vertical\",\n    style: {\n      width: '100%'\n    },\n    size: \"large\",\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Select, {\n          placeholder: \"\\u9009\\u62E9\\u9879\\u76EE\",\n          style: {\n            width: 200\n          },\n          onChange: setProjectId\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n          value: dateRange,\n          onChange: setDateRange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 19\n          }, this),\n          onClick: () => {/* TODO: 实现报表导出 */},\n          children: \"\\u5BFC\\u51FA\\u62A5\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SummaryCards, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TrendChart, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CompositionChart, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(AlertSection, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 12,\n        children: /*#__PURE__*/_jsxDEV(SuggestionSection, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 289,\n    columnNumber: 5\n  }, this);\n};\n_s(CostAnalysis, \"Muep2/UN1cWkcn99aHA9e8yhUu4=\");\n_c = CostAnalysis;\nexport default CostAnalysis;\nvar _c;\n$RefreshReg$(_c, \"CostAnalysis\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "Space", "Select", "<PERSON><PERSON>", "<PERSON><PERSON>", "List", "Typography", "Divider", "Table", "Tag", "<PERSON><PERSON><PERSON>", "Progress", "DatePicker", "DollarOutlined", "RiseOutlined", "FallOutlined", "Alert<PERSON>ut<PERSON>", "BarChartOutlined", "LineChartOutlined", "PieChartOutlined", "DownloadOutlined", "TeamOutlined", "ShoppingOutlined", "ToolOutlined", "Line", "Bar", "Pie", "DualAxes", "axios", "moment", "jsxDEV", "_jsxDEV", "Title", "Text", "RangePicker", "CostAnalysis", "_s", "loading", "setLoading", "projectId", "setProjectId", "date<PERSON><PERSON><PERSON>", "setDateRange", "subtract", "summary", "set<PERSON>ummary", "trend", "setTrend", "resourceCost", "setResourceCost", "alerts", "<PERSON><PERSON><PERSON><PERSON>", "suggestions", "setSuggestions", "fetchData", "Promise", "all", "fetchSummary", "fetchTrend", "fetchResourceCost", "fetch<PERSON><PERSON><PERSON>", "fetchSuggestions", "error", "console", "response", "get", "data", "params", "interval", "startDate", "format", "endDate", "SummaryCards", "gutter", "children", "span", "title", "value", "totalCost", "precision", "prefix", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "suffix", "style", "marginTop", "type", "costTrend", "Math", "abs", "laborCost", "percent", "laborCostPercentage", "size", "status", "materialCost", "materialCostPercentage", "machineCost", "machineCostPercentage", "TrendChart", "costByPeriod", "accumulatedCost", "xField", "yField", "geometryOptions", "geometry", "color", "columnWidthRatio", "smooth", "annotations", "position", "content", "fill", "fontSize", "<PERSON><PERSON><PERSON>", "costComposition", "angleField", "colorField", "radius", "label", "toFixed", "seriesField", "isStack", "AlertSection", "dataSource", "renderItem", "alert", "<PERSON><PERSON>", "level", "message", "SuggestionSection", "suggestion", "Meta", "description", "potentialSaving", "direction", "width", "placeholder", "onChange", "icon", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/src/pages/cost/CostAnalysis.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Card,\r\n  Row,\r\n  Col,\r\n  Statistic,\r\n  Space,\r\n  Select,\r\n  Button,\r\n  Alert,\r\n  List,\r\n  Typography,\r\n  Divider,\r\n  Table,\r\n  Tag,\r\n  Tooltip,\r\n  Progress,\r\n  DatePicker\r\n} from 'antd';\r\nimport {\r\n  DollarOutlined,\r\n  RiseOutlined,\r\n  FallOutlined,\r\n  AlertOutlined,\r\n  <PERSON><PERSON><PERSON>Outlined,\r\n  <PERSON><PERSON>hartOutlined,\r\n  Pie<PERSON>hartOutlined,\r\n  DownloadOutlined,\r\n  TeamOutlined,\r\n  ShoppingOutlined,\r\n  ToolOutlined\r\n} from '@ant-design/icons';\r\nimport { Line, Bar, Pie, DualAxes } from '@ant-design/charts';\r\nimport axios from 'axios';\r\nimport moment from 'moment';\r\n\r\nconst { Title, Text } = Typography;\r\nconst { RangePicker } = DatePicker;\r\n\r\nconst CostAnalysis = () => {\r\n  // 状态管理\r\n  const [loading, setLoading] = useState(false);\r\n  const [projectId, setProjectId] = useState(null);\r\n  const [dateRange, setDateRange] = useState([moment().subtract(30, 'days'), moment()]);\r\n  const [summary, setSummary] = useState(null);\r\n  const [trend, setTrend] = useState(null);\r\n  const [resourceCost, setResourceCost] = useState(null);\r\n  const [alerts, setAlerts] = useState([]);\r\n  const [suggestions, setSuggestions] = useState([]);\r\n\r\n  // 初始化加载\r\n  useEffect(() => {\r\n    if (projectId) {\r\n      fetchData();\r\n    }\r\n  }, [projectId, dateRange]);\r\n\r\n  // 获取所有数据\r\n  const fetchData = async () => {\r\n    setLoading(true);\r\n    try {\r\n      await Promise.all([\r\n        fetchSummary(),\r\n        fetchTrend(),\r\n        fetchResourceCost(),\r\n        fetchAlerts(),\r\n        fetchSuggestions()\r\n      ]);\r\n    } catch (error) {\r\n      console.error('获取数据失败：', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 获取成本汇总\r\n  const fetchSummary = async () => {\r\n    const response = await axios.get(`/api/cost-analysis/projects/${projectId}/summary`);\r\n    setSummary(response.data);\r\n  };\r\n\r\n  // 获取成本趋势\r\n  const fetchTrend = async () => {\r\n    const response = await axios.get(`/api/cost-analysis/projects/${projectId}/trend`, {\r\n      params: {\r\n        interval: 'day',\r\n        startDate: dateRange[0].format('YYYY-MM-DD'),\r\n        endDate: dateRange[1].format('YYYY-MM-DD')\r\n      }\r\n    });\r\n    setTrend(response.data);\r\n  };\r\n\r\n  // 获取资源成本\r\n  const fetchResourceCost = async () => {\r\n    const response = await axios.get(`/api/cost-analysis/projects/${projectId}/resource-cost`);\r\n    setResourceCost(response.data);\r\n  };\r\n\r\n  // 获取预警信息\r\n  const fetchAlerts = async () => {\r\n    const response = await axios.get(`/api/cost-analysis/projects/${projectId}/alerts`);\r\n    setAlerts(response.data);\r\n  };\r\n\r\n  // 获取优化建议\r\n  const fetchSuggestions = async () => {\r\n    const response = await axios.get(`/api/cost-analysis/projects/${projectId}/optimization`);\r\n    setSuggestions(response.data);\r\n  };\r\n\r\n  // 成本总览卡片\r\n  const SummaryCards = () => (\r\n    <Row gutter={16}>\r\n      <Col span={6}>\r\n        <Card>\r\n          <Statistic\r\n            title=\"总成本\"\r\n            value={summary?.totalCost}\r\n            precision={2}\r\n            prefix={<DollarOutlined />}\r\n            suffix=\"元\"\r\n          />\r\n          <div style={{ marginTop: 8 }}>\r\n            <Text type={summary?.costTrend > 0 ? 'danger' : 'success'}>\r\n              {summary?.costTrend > 0 ? <RiseOutlined /> : <FallOutlined />}\r\n              {Math.abs(summary?.costTrend)}%\r\n            </Text>\r\n            <Text type=\"secondary\"> 较上月</Text>\r\n          </div>\r\n        </Card>\r\n      </Col>\r\n      <Col span={6}>\r\n        <Card>\r\n          <Statistic\r\n            title=\"人工成本\"\r\n            value={summary?.laborCost}\r\n            precision={2}\r\n            prefix={<TeamOutlined />}\r\n            suffix=\"元\"\r\n          />\r\n          <Progress\r\n            percent={summary?.laborCostPercentage}\r\n            size=\"small\"\r\n            status=\"active\"\r\n          />\r\n        </Card>\r\n      </Col>\r\n      <Col span={6}>\r\n        <Card>\r\n          <Statistic\r\n            title=\"材料成本\"\r\n            value={summary?.materialCost}\r\n            precision={2}\r\n            prefix={<ShoppingOutlined />}\r\n            suffix=\"元\"\r\n          />\r\n          <Progress\r\n            percent={summary?.materialCostPercentage}\r\n            size=\"small\"\r\n            status=\"active\"\r\n          />\r\n        </Card>\r\n      </Col>\r\n      <Col span={6}>\r\n        <Card>\r\n          <Statistic\r\n            title=\"机械成本\"\r\n            value={summary?.machineCost}\r\n            precision={2}\r\n            prefix={<ToolOutlined />}\r\n            suffix=\"元\"\r\n          />\r\n          <Progress\r\n            percent={summary?.machineCostPercentage}\r\n            size=\"small\"\r\n            status=\"active\"\r\n          />\r\n        </Card>\r\n      </Col>\r\n    </Row>\r\n  );\r\n\r\n  // 成本趋势图\r\n  const TrendChart = () => (\r\n    <Card title=\"成本趋势分析\">\r\n      <DualAxes\r\n        data={[trend?.costByPeriod || [], trend?.accumulatedCost || []]}\r\n        xField=\"date\"\r\n        yField={['cost', 'accumulated']}\r\n        geometryOptions={[\r\n          {\r\n            geometry: 'column',\r\n            color: '#5B8FF9',\r\n            columnWidthRatio: 0.4\r\n          },\r\n          {\r\n            geometry: 'line',\r\n            smooth: true,\r\n            color: '#5AD8A6'\r\n          }\r\n        ]}\r\n        annotations={[\r\n          {\r\n            type: 'text',\r\n            position: ['50%', '50%'],\r\n            content: loading ? '加载中...' : '',\r\n            style: {\r\n              fill: '#666',\r\n              fontSize: 14\r\n            }\r\n          }\r\n        ]}\r\n      />\r\n    </Card>\r\n  );\r\n\r\n  // 成本构成图\r\n  const CompositionChart = () => (\r\n    <Card title=\"成本构成分析\">\r\n      <Row gutter={16}>\r\n        <Col span={12}>\r\n          <Pie\r\n            data={summary?.costComposition || []}\r\n            angleField=\"value\"\r\n            colorField=\"type\"\r\n            radius={0.8}\r\n            label={{\r\n              type: 'outer',\r\n              content: (data) => `${data.type}: ${(data.percent * 100).toFixed(1)}%`\r\n            }}\r\n          />\r\n        </Col>\r\n        <Col span={12}>\r\n          <Bar\r\n            data={resourceCost?.resourceCost || []}\r\n            xField=\"value\"\r\n            yField=\"name\"\r\n            seriesField=\"type\"\r\n            isStack={true}\r\n            label={{\r\n              position: 'middle'\r\n            }}\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Card>\r\n  );\r\n\r\n  // 预警信息\r\n  const AlertSection = () => (\r\n    <Card title=\"成本预警\">\r\n      <List\r\n        dataSource={alerts}\r\n        renderItem={alert => (\r\n          <List.Item>\r\n            <Space>\r\n              <AlertOutlined style={{ color: alert.level === 'high' ? '#f5222d' : '#faad14' }} />\r\n              <Text>{alert.message}</Text>\r\n              <Tag color={alert.level === 'high' ? 'red' : 'orange'}>\r\n                {alert.level === 'high' ? '高风险' : '中风险'}\r\n              </Tag>\r\n            </Space>\r\n          </List.Item>\r\n        )}\r\n      />\r\n    </Card>\r\n  );\r\n\r\n  // 优化建议\r\n  const SuggestionSection = () => (\r\n    <Card title=\"成本优化建议\">\r\n      <List\r\n        dataSource={suggestions}\r\n        renderItem={suggestion => (\r\n          <List.Item>\r\n            <List.Item.Meta\r\n              title={suggestion.title}\r\n              description={suggestion.description}\r\n            />\r\n            <Tag color=\"blue\">{suggestion.potentialSaving}元</Tag>\r\n          </List.Item>\r\n        )}\r\n      />\r\n    </Card>\r\n  );\r\n\r\n  return (\r\n    <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\r\n      {/* 控制面板 */}\r\n      <Card>\r\n        <Space>\r\n          <Select\r\n            placeholder=\"选择项目\"\r\n            style={{ width: 200 }}\r\n            onChange={setProjectId}\r\n          >\r\n            {/* TODO: 添加项目选项 */}\r\n          </Select>\r\n          <RangePicker\r\n            value={dateRange}\r\n            onChange={setDateRange}\r\n          />\r\n          <Button\r\n            type=\"primary\"\r\n            icon={<DownloadOutlined />}\r\n            onClick={() => {/* TODO: 实现报表导出 */}}\r\n          >\r\n            导出报表\r\n          </Button>\r\n        </Space>\r\n      </Card>\r\n\r\n      {/* 成本总览 */}\r\n      <SummaryCards />\r\n\r\n      {/* 成本趋势 */}\r\n      <TrendChart />\r\n\r\n      {/* 成本构成 */}\r\n      <CompositionChart />\r\n\r\n      <Row gutter={16}>\r\n        <Col span={12}>\r\n          {/* 预警信息 */}\r\n          <AlertSection />\r\n        </Col>\r\n        <Col span={12}>\r\n          {/* 优化建议 */}\r\n          <SuggestionSection />\r\n        </Col>\r\n      </Row>\r\n    </Space>\r\n  );\r\n};\r\n\r\nexport default CostAnalysis;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,UAAU,QACL,MAAM;AACb,SACEC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,gBAAgB,EAChBC,iBAAiB,EACjBC,gBAAgB,EAChBC,gBAAgB,EAChBC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,QACP,mBAAmB;AAC1B,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,oBAAoB;AAC7D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG3B,UAAU;AAClC,MAAM;EAAE4B;AAAY,CAAC,GAAGtB,UAAU;AAElC,MAAMuB,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,CAACkC,MAAM,CAAC,CAAC,CAACc,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAEd,MAAM,CAAC,CAAC,CAAC,CAAC;EACrF,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuD,MAAM,EAAEC,SAAS,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,IAAI2C,SAAS,EAAE;MACbe,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACf,SAAS,EAAEE,SAAS,CAAC,CAAC;;EAE1B;EACA,MAAMa,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BhB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMiB,OAAO,CAACC,GAAG,CAAC,CAChBC,YAAY,CAAC,CAAC,EACdC,UAAU,CAAC,CAAC,EACZC,iBAAiB,CAAC,CAAC,EACnBC,WAAW,CAAC,CAAC,EACbC,gBAAgB,CAAC,CAAC,CACnB,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMO,QAAQ,GAAG,MAAMpC,KAAK,CAACqC,GAAG,CAAC,+BAA+B1B,SAAS,UAAU,CAAC;IACpFM,UAAU,CAACmB,QAAQ,CAACE,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMR,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMM,QAAQ,GAAG,MAAMpC,KAAK,CAACqC,GAAG,CAAC,+BAA+B1B,SAAS,QAAQ,EAAE;MACjF4B,MAAM,EAAE;QACNC,QAAQ,EAAE,KAAK;QACfC,SAAS,EAAE5B,SAAS,CAAC,CAAC,CAAC,CAAC6B,MAAM,CAAC,YAAY,CAAC;QAC5CC,OAAO,EAAE9B,SAAS,CAAC,CAAC,CAAC,CAAC6B,MAAM,CAAC,YAAY;MAC3C;IACF,CAAC,CAAC;IACFvB,QAAQ,CAACiB,QAAQ,CAACE,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMP,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAMK,QAAQ,GAAG,MAAMpC,KAAK,CAACqC,GAAG,CAAC,+BAA+B1B,SAAS,gBAAgB,CAAC;IAC1FU,eAAe,CAACe,QAAQ,CAACE,IAAI,CAAC;EAChC,CAAC;;EAED;EACA,MAAMN,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,MAAMI,QAAQ,GAAG,MAAMpC,KAAK,CAACqC,GAAG,CAAC,+BAA+B1B,SAAS,SAAS,CAAC;IACnFY,SAAS,CAACa,QAAQ,CAACE,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAML,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,MAAMG,QAAQ,GAAG,MAAMpC,KAAK,CAACqC,GAAG,CAAC,+BAA+B1B,SAAS,eAAe,CAAC;IACzFc,cAAc,CAACW,QAAQ,CAACE,IAAI,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMM,YAAY,GAAGA,CAAA,kBACnBzC,OAAA,CAACjC,GAAG;IAAC2E,MAAM,EAAE,EAAG;IAAAC,QAAA,gBACd3C,OAAA,CAAChC,GAAG;MAAC4E,IAAI,EAAE,CAAE;MAAAD,QAAA,eACX3C,OAAA,CAAClC,IAAI;QAAA6E,QAAA,gBACH3C,OAAA,CAAC/B,SAAS;UACR4E,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAEjC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkC,SAAU;UAC1BC,SAAS,EAAE,CAAE;UACbC,MAAM,eAAEjD,OAAA,CAAClB,cAAc;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BC,MAAM,EAAC;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACFrD,OAAA;UAAKuD,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAE,CAAE;UAAAb,QAAA,gBAC3B3C,OAAA,CAACE,IAAI;YAACuD,IAAI,EAAE,CAAA5C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6C,SAAS,IAAG,CAAC,GAAG,QAAQ,GAAG,SAAU;YAAAf,QAAA,GACvD,CAAA9B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6C,SAAS,IAAG,CAAC,gBAAG1D,OAAA,CAACjB,YAAY;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGrD,OAAA,CAAChB,YAAY;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC5DM,IAAI,CAACC,GAAG,CAAC/C,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6C,SAAS,CAAC,EAAC,GAChC;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPrD,OAAA,CAACE,IAAI;YAACuD,IAAI,EAAC,WAAW;YAAAd,QAAA,EAAC;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNrD,OAAA,CAAChC,GAAG;MAAC4E,IAAI,EAAE,CAAE;MAAAD,QAAA,eACX3C,OAAA,CAAClC,IAAI;QAAA6E,QAAA,gBACH3C,OAAA,CAAC/B,SAAS;UACR4E,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAEjC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgD,SAAU;UAC1Bb,SAAS,EAAE,CAAE;UACbC,MAAM,eAAEjD,OAAA,CAACV,YAAY;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBC,MAAM,EAAC;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACFrD,OAAA,CAACpB,QAAQ;UACPkF,OAAO,EAAEjD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkD,mBAAoB;UACtCC,IAAI,EAAC,OAAO;UACZC,MAAM,EAAC;QAAQ;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNrD,OAAA,CAAChC,GAAG;MAAC4E,IAAI,EAAE,CAAE;MAAAD,QAAA,eACX3C,OAAA,CAAClC,IAAI;QAAA6E,QAAA,gBACH3C,OAAA,CAAC/B,SAAS;UACR4E,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAEjC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqD,YAAa;UAC7BlB,SAAS,EAAE,CAAE;UACbC,MAAM,eAAEjD,OAAA,CAACT,gBAAgB;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BC,MAAM,EAAC;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACFrD,OAAA,CAACpB,QAAQ;UACPkF,OAAO,EAAEjD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsD,sBAAuB;UACzCH,IAAI,EAAC,OAAO;UACZC,MAAM,EAAC;QAAQ;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNrD,OAAA,CAAChC,GAAG;MAAC4E,IAAI,EAAE,CAAE;MAAAD,QAAA,eACX3C,OAAA,CAAClC,IAAI;QAAA6E,QAAA,gBACH3C,OAAA,CAAC/B,SAAS;UACR4E,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAEjC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuD,WAAY;UAC5BpB,SAAS,EAAE,CAAE;UACbC,MAAM,eAAEjD,OAAA,CAACR,YAAY;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBC,MAAM,EAAC;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACFrD,OAAA,CAACpB,QAAQ;UACPkF,OAAO,EAAEjD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwD,qBAAsB;UACxCL,IAAI,EAAC,OAAO;UACZC,MAAM,EAAC;QAAQ;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMiB,UAAU,GAAGA,CAAA,kBACjBtE,OAAA,CAAClC,IAAI;IAAC+E,KAAK,EAAC,sCAAQ;IAAAF,QAAA,eAClB3C,OAAA,CAACJ,QAAQ;MACPuC,IAAI,EAAE,CAAC,CAAApB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwD,YAAY,KAAI,EAAE,EAAE,CAAAxD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyD,eAAe,KAAI,EAAE,CAAE;MAChEC,MAAM,EAAC,MAAM;MACbC,MAAM,EAAE,CAAC,MAAM,EAAE,aAAa,CAAE;MAChCC,eAAe,EAAE,CACf;QACEC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE,SAAS;QAChBC,gBAAgB,EAAE;MACpB,CAAC,EACD;QACEF,QAAQ,EAAE,MAAM;QAChBG,MAAM,EAAE,IAAI;QACZF,KAAK,EAAE;MACT,CAAC,CACD;MACFG,WAAW,EAAE,CACX;QACEvB,IAAI,EAAE,MAAM;QACZwB,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QACxBC,OAAO,EAAE5E,OAAO,GAAG,QAAQ,GAAG,EAAE;QAChCiD,KAAK,EAAE;UACL4B,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE;QACZ;MACF,CAAC;IACD;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACP;;EAED;EACA,MAAMgC,gBAAgB,GAAGA,CAAA,kBACvBrF,OAAA,CAAClC,IAAI;IAAC+E,KAAK,EAAC,sCAAQ;IAAAF,QAAA,eAClB3C,OAAA,CAACjC,GAAG;MAAC2E,MAAM,EAAE,EAAG;MAAAC,QAAA,gBACd3C,OAAA,CAAChC,GAAG;QAAC4E,IAAI,EAAE,EAAG;QAAAD,QAAA,eACZ3C,OAAA,CAACL,GAAG;UACFwC,IAAI,EAAE,CAAAtB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyE,eAAe,KAAI,EAAG;UACrCC,UAAU,EAAC,OAAO;UAClBC,UAAU,EAAC,MAAM;UACjBC,MAAM,EAAE,GAAI;UACZC,KAAK,EAAE;YACLjC,IAAI,EAAE,OAAO;YACbyB,OAAO,EAAG/C,IAAI,IAAK,GAAGA,IAAI,CAACsB,IAAI,KAAK,CAACtB,IAAI,CAAC2B,OAAO,GAAG,GAAG,EAAE6B,OAAO,CAAC,CAAC,CAAC;UACrE;QAAE;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNrD,OAAA,CAAChC,GAAG;QAAC4E,IAAI,EAAE,EAAG;QAAAD,QAAA,eACZ3C,OAAA,CAACN,GAAG;UACFyC,IAAI,EAAE,CAAAlB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEA,YAAY,KAAI,EAAG;UACvCwD,MAAM,EAAC,OAAO;UACdC,MAAM,EAAC,MAAM;UACbkB,WAAW,EAAC,MAAM;UAClBC,OAAO,EAAE,IAAK;UACdH,KAAK,EAAE;YACLT,QAAQ,EAAE;UACZ;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CACP;;EAED;EACA,MAAMyC,YAAY,GAAGA,CAAA,kBACnB9F,OAAA,CAAClC,IAAI;IAAC+E,KAAK,EAAC,0BAAM;IAAAF,QAAA,eAChB3C,OAAA,CAAC1B,IAAI;MACHyH,UAAU,EAAE5E,MAAO;MACnB6E,UAAU,EAAEC,KAAK,iBACfjG,OAAA,CAAC1B,IAAI,CAAC4H,IAAI;QAAAvD,QAAA,eACR3C,OAAA,CAAC9B,KAAK;UAAAyE,QAAA,gBACJ3C,OAAA,CAACf,aAAa;YAACsE,KAAK,EAAE;cAAEsB,KAAK,EAAEoB,KAAK,CAACE,KAAK,KAAK,MAAM,GAAG,SAAS,GAAG;YAAU;UAAE;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnFrD,OAAA,CAACE,IAAI;YAAAyC,QAAA,EAAEsD,KAAK,CAACG;UAAO;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5BrD,OAAA,CAACtB,GAAG;YAACmG,KAAK,EAAEoB,KAAK,CAACE,KAAK,KAAK,MAAM,GAAG,KAAK,GAAG,QAAS;YAAAxD,QAAA,EACnDsD,KAAK,CAACE,KAAK,KAAK,MAAM,GAAG,KAAK,GAAG;UAAK;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACX;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACP;;EAED;EACA,MAAMgD,iBAAiB,GAAGA,CAAA,kBACxBrG,OAAA,CAAClC,IAAI;IAAC+E,KAAK,EAAC,sCAAQ;IAAAF,QAAA,eAClB3C,OAAA,CAAC1B,IAAI;MACHyH,UAAU,EAAE1E,WAAY;MACxB2E,UAAU,EAAEM,UAAU,iBACpBtG,OAAA,CAAC1B,IAAI,CAAC4H,IAAI;QAAAvD,QAAA,gBACR3C,OAAA,CAAC1B,IAAI,CAAC4H,IAAI,CAACK,IAAI;UACb1D,KAAK,EAAEyD,UAAU,CAACzD,KAAM;UACxB2D,WAAW,EAAEF,UAAU,CAACE;QAAY;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACFrD,OAAA,CAACtB,GAAG;UAACmG,KAAK,EAAC,MAAM;UAAAlC,QAAA,GAAE2D,UAAU,CAACG,eAAe,EAAC,QAAC;QAAA;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C;IACX;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACP;EAED,oBACErD,OAAA,CAAC9B,KAAK;IAACwI,SAAS,EAAC,UAAU;IAACnD,KAAK,EAAE;MAAEoD,KAAK,EAAE;IAAO,CAAE;IAAC3C,IAAI,EAAC,OAAO;IAAArB,QAAA,gBAEhE3C,OAAA,CAAClC,IAAI;MAAA6E,QAAA,eACH3C,OAAA,CAAC9B,KAAK;QAAAyE,QAAA,gBACJ3C,OAAA,CAAC7B,MAAM;UACLyI,WAAW,EAAC,0BAAM;UAClBrD,KAAK,EAAE;YAAEoD,KAAK,EAAE;UAAI,CAAE;UACtBE,QAAQ,EAAEpG;QAAa;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGjB,CAAC,eACTrD,OAAA,CAACG,WAAW;UACV2C,KAAK,EAAEpC,SAAU;UACjBmG,QAAQ,EAAElG;QAAa;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACFrD,OAAA,CAAC5B,MAAM;UACLqF,IAAI,EAAC,SAAS;UACdqD,IAAI,eAAE9G,OAAA,CAACX,gBAAgB;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3B0D,OAAO,EAAEA,CAAA,KAAM,CAAC,mBAAoB;UAAApE,QAAA,EACrC;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGPrD,OAAA,CAACyC,YAAY;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGhBrD,OAAA,CAACsE,UAAU;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGdrD,OAAA,CAACqF,gBAAgB;MAAAnC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEpBrD,OAAA,CAACjC,GAAG;MAAC2E,MAAM,EAAE,EAAG;MAAAC,QAAA,gBACd3C,OAAA,CAAChC,GAAG;QAAC4E,IAAI,EAAE,EAAG;QAAAD,QAAA,eAEZ3C,OAAA,CAAC8F,YAAY;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACNrD,OAAA,CAAChC,GAAG;QAAC4E,IAAI,EAAE,EAAG;QAAAD,QAAA,eAEZ3C,OAAA,CAACqG,iBAAiB;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAAChD,EAAA,CAvSID,YAAY;AAAA4G,EAAA,GAAZ5G,YAAY;AAySlB,eAAeA,YAAY;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}