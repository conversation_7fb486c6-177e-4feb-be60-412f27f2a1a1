package com.costproject.repository;

import com.costproject.entity.Project;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ProjectRepository extends JpaRepository<Project, Long> {
    
    /**
     * 根据名称或描述搜索项目（分页）
     */
    Page<Project> findByNameContainingOrDescriptionContaining(
        String name, String description, Pageable pageable);
    
    /**
     * 根据状态统计项目数量
     */
    @Query("SELECT COUNT(p) FROM Project p WHERE p.status = :status")
    long countByStatus(@Param("status") String status);
    
    /**
     * 根据名称搜索项目
     */
    Page<Project> findByNameContaining(String name, Pageable pageable);
    
    /**
     * 根据状态查找项目
     */
    Page<Project> findByStatus(String status, Pageable pageable);
    
    /**
     * 检查项目名称是否已存在
     */
    boolean existsByName(String name);
} 