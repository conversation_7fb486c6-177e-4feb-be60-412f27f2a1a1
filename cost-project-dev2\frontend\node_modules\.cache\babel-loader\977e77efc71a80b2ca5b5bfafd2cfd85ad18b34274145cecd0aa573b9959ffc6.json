{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\cost-project-dev2\\\\frontend\\\\src\\\\pages\\\\cost\\\\QuotaManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Card, Button, Space, Upload, message, Modal, Form, Input, InputNumber, Tabs, Tooltip, Tag, Drawer, Statistic, Row, Col, Collapse } from 'antd';\nimport { UploadOutlined, DownloadOutlined, PlusOutlined, CalculatorOutlined, DeleteOutlined, EditOutlined, SearchOutlined, FileExcelOutlined, BarChartOutlined, TeamOutlined, ShoppingOutlined, ToolOutlined } from '@ant-design/icons';\nimport { Line, Bar, Pie } from '@ant-design/charts';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Panel\n} = Collapse;\nconst QuotaManagement = () => {\n  _s();\n  // 状态管理\n  const [loading, setLoading] = useState(false);\n  const [quotaItems, setQuotaItems] = useState([]);\n  const [selectedRows, setSelectedRows] = useState([]);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [drawerVisible, setDrawerVisible] = useState(false);\n  const [activeTab, setActiveTab] = useState('labor');\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n  const [costAnalysis, setCostAnalysis] = useState(null);\n  const [chartData, setChartData] = useState([]);\n\n  // 初始化加载\n  useEffect(() => {\n    fetchQuotaItems();\n  }, []);\n\n  // 获取定额列表\n  const fetchQuotaItems = async (params = {}) => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/quotas', {\n        params\n      });\n      setQuotaItems(response.data);\n    } catch (error) {\n      message.error('获取定额列表失败：' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '编号',\n    dataIndex: 'code',\n    key: 'code',\n    width: 120,\n    fixed: 'left'\n  }, {\n    title: '名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: 200\n  }, {\n    title: '描述',\n    dataIndex: 'description',\n    key: 'description',\n    width: 300,\n    ellipsis: true\n  }, {\n    title: '单位',\n    dataIndex: 'unit',\n    key: 'unit',\n    width: 80\n  }, {\n    title: '人工费',\n    dataIndex: 'laborCost',\n    key: 'laborCost',\n    width: 100,\n    align: 'right',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '材料费',\n    dataIndex: 'materialCost',\n    key: 'materialCost',\n    width: 100,\n    align: 'right',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '机械费',\n    dataIndex: 'machineCost',\n    key: 'machineCost',\n    width: 100,\n    align: 'right',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '综合单价',\n    dataIndex: 'unitPrice',\n    key: 'unitPrice',\n    width: 120,\n    align: 'right',\n    render: value => value === null || value === void 0 ? void 0 : value.toFixed(2)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5220\\u9664\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleDelete(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 处理创建\n  const handleCreate = () => {\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 处理编辑\n  const handleEdit = record => {\n    form.setFieldsValue(record);\n    setModalVisible(true);\n  };\n\n  // 处理删除\n  const handleDelete = async record => {\n    try {\n      await axios.delete(`/api/quotas/${record.id}`);\n      message.success('删除成功');\n      fetchQuotaItems();\n    } catch (error) {\n      message.error('删除失败：' + error.message);\n    }\n  };\n\n  // 处理表单提交\n  const handleSubmit = async values => {\n    try {\n      if (values.id) {\n        await axios.put(`/api/quotas/${values.id}`, values);\n      } else {\n        await axios.post('/api/quotas', values);\n      }\n      message.success('保存成功');\n      setModalVisible(false);\n      fetchQuotaItems();\n    } catch (error) {\n      message.error('保存失败：' + error.message);\n    }\n  };\n\n  // 处理导入\n  const handleImport = async file => {\n    const formData = new FormData();\n    formData.append('file', file);\n    try {\n      await axios.post('/api/quotas/import/excel', formData);\n      message.success('导入成功');\n      fetchQuotaItems();\n      return false;\n    } catch (error) {\n      message.error('导入失败：' + error.message);\n      return false;\n    }\n  };\n\n  // 处理成本分析\n  const handleCostAnalysis = async () => {\n    try {\n      const response = await axios.get('/api/quotas/resource-consumption', {\n        params: {\n          ids: selectedRows.map(row => row.id)\n        }\n      });\n      setCostAnalysis(response.data);\n      setDrawerVisible(true);\n\n      // 生成图表数据\n      const data = selectedRows.map(row => [{\n        type: '人工费',\n        value: row.laborCost,\n        name: row.name\n      }, {\n        type: '材料费',\n        value: row.materialCost,\n        name: row.name\n      }, {\n        type: '机械费',\n        value: row.machineCost,\n        name: row.name\n      }]).flat();\n      setChartData(data);\n    } catch (error) {\n      message.error('成本分析失败：' + error.message);\n    }\n  };\n\n  // 搜索表单\n  const searchFormItems = /*#__PURE__*/_jsxDEV(Form, {\n    form: searchForm,\n    layout: \"inline\",\n    children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n      name: \"keyword\",\n      label: \"\\u5173\\u952E\\u8BCD\",\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        placeholder: \"\\u7F16\\u53F7/\\u540D\\u79F0/\\u63CF\\u8FF0\",\n        allowClear: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 38\n        }, this),\n        onClick: () => {\n          const values = searchForm.getFieldsValue();\n          fetchQuotaItems(values);\n        },\n        children: \"\\u641C\\u7D22\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 237,\n    columnNumber: 5\n  }, this);\n\n  // 工具栏\n  const toolBar = /*#__PURE__*/_jsxDEV(Space, {\n    children: [/*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 36\n      }, this),\n      onClick: handleCreate,\n      children: \"\\u65B0\\u5EFA\\u5B9A\\u989D\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Upload, {\n      accept: \".xlsx,.xls,.md\",\n      showUploadList: false,\n      beforeUpload: handleImport,\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(UploadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 23\n        }, this),\n        children: \"\\u5BFC\\u5165\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      icon: /*#__PURE__*/_jsxDEV(CalculatorOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 15\n      }, this),\n      disabled: selectedRows.length === 0,\n      onClick: handleCostAnalysis,\n      children: \"\\u6210\\u672C\\u5206\\u6790\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 254,\n    columnNumber: 5\n  }, this);\n\n  // 成本表单\n  const CostForm = ({\n    prefix,\n    disabled\n  }) => /*#__PURE__*/_jsxDEV(Row, {\n    gutter: 16,\n    children: [/*#__PURE__*/_jsxDEV(Col, {\n      span: 8,\n      children: /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: `${prefix}BasicCost`,\n        label: \"\\u57FA\\u672C\\u8D39\\u7528\",\n        rules: [{\n          required: true,\n          message: '请输入基本费用'\n        }],\n        children: /*#__PURE__*/_jsxDEV(InputNumber, {\n          disabled: disabled,\n          style: {\n            width: '100%'\n          },\n          min: 0,\n          precision: 2\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      span: 8,\n      children: /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: `${prefix}AdditionalCost`,\n        label: \"\\u9644\\u52A0\\u8D39\\u7528\",\n        rules: [{\n          required: true,\n          message: '请输入附加费用'\n        }],\n        children: /*#__PURE__*/_jsxDEV(InputNumber, {\n          disabled: disabled,\n          style: {\n            width: '100%'\n          },\n          min: 0,\n          precision: 2\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      span: 8,\n      children: /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: `${prefix}OtherCost`,\n        label: \"\\u5176\\u4ED6\\u8D39\\u7528\",\n        children: /*#__PURE__*/_jsxDEV(InputNumber, {\n          disabled: disabled,\n          style: {\n            width: '100%'\n          },\n          min: 0,\n          precision: 2\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 277,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      style: {\n        width: '100%'\n      },\n      size: \"large\",\n      children: [searchFormItems, toolBar, /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: quotaItems,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1500\n        },\n        rowSelection: {\n          selectedRowKeys: selectedRows.map(row => row.id),\n          onChange: (_, rows) => setSelectedRows(rows)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: form.getFieldValue('id') ? '编辑定额' : '新建定额',\n      visible: modalVisible,\n      onOk: () => form.submit(),\n      onCancel: () => setModalVisible(false),\n      width: 1000,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"id\",\n          hidden: true,\n          children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"code\",\n              label: \"\\u7F16\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入编号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7F16\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"unit\",\n              label: \"\\u5355\\u4F4D\",\n              rules: [{\n                required: true,\n                message: '请输入单位'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5355\\u4F4D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n          activeKey: activeTab,\n          onChange: setActiveTab,\n          children: [/*#__PURE__*/_jsxDEV(TabPane, {\n            tab: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 26\n              }, this), \"\\u4EBA\\u5DE5\\u8D39\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 20\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(CostForm, {\n              prefix: \"labor\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this)\n          }, \"labor\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(ShoppingOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 26\n              }, this), \"\\u6750\\u6599\\u8D39\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 20\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(CostForm, {\n              prefix: \"material\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)\n          }, \"material\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(ToolOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 26\n              }, this), \"\\u673A\\u68B0\\u8D39\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 20\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(CostForm, {\n              prefix: \"machine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this)\n          }, \"machine\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      title: \"\\u6210\\u672C\\u5206\\u6790\",\n      placement: \"right\",\n      width: 800,\n      visible: drawerVisible,\n      onClose: () => setDrawerVisible(false),\n      children: costAnalysis && /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        size: \"large\",\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u4EBA\\u5DE5\\u8D39\\u5408\\u8BA1\",\n              value: costAnalysis.totalLaborCost,\n              precision: 2,\n              prefix: \"\\xA5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u6750\\u6599\\u8D39\\u5408\\u8BA1\",\n              value: costAnalysis.totalMaterialCost,\n              precision: 2,\n              prefix: \"\\xA5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u673A\\u68B0\\u8D39\\u5408\\u8BA1\",\n              value: costAnalysis.totalMachineCost,\n              precision: 2,\n              prefix: \"\\xA5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6210\\u672C\\u6784\\u6210\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Pie, {\n            data: chartData,\n            angleField: \"value\",\n            colorField: \"type\",\n            radius: 0.8,\n            label: {\n              type: 'outer',\n              content: data => `${data.type}: ${(data.percent * 100).toFixed(1)}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6210\\u672C\\u660E\\u7EC6\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Bar, {\n            data: chartData,\n            xField: \"value\",\n            yField: \"name\",\n            seriesField: \"type\",\n            isStack: true,\n            label: {\n              position: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n          children: /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u8D44\\u6E90\\u6D88\\u8017\\u660E\\u7EC6\",\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              dataSource: costAnalysis.resourceDetails,\n              columns: [{\n                title: '资源名称',\n                dataIndex: 'name'\n              }, {\n                title: '单位',\n                dataIndex: 'unit'\n              }, {\n                title: '数量',\n                dataIndex: 'quantity'\n              }, {\n                title: '单价',\n                dataIndex: 'price'\n              }, {\n                title: '合价',\n                dataIndex: 'total'\n              }],\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this)\n          }, \"1\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 323,\n    columnNumber: 5\n  }, this);\n};\n_s(QuotaManagement, \"4eMJ84LJcEb7Y1I9Rq54Qyq9/9c=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = QuotaManagement;\nexport default QuotaManagement;\nvar _c;\n$RefreshReg$(_c, \"QuotaManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "Card", "<PERSON><PERSON>", "Space", "Upload", "message", "Modal", "Form", "Input", "InputNumber", "Tabs", "<PERSON><PERSON><PERSON>", "Tag", "Drawer", "Statistic", "Row", "Col", "Collapse", "UploadOutlined", "DownloadOutlined", "PlusOutlined", "CalculatorOutlined", "DeleteOutlined", "EditOutlined", "SearchOutlined", "FileExcelOutlined", "BarChartOutlined", "TeamOutlined", "ShoppingOutlined", "ToolOutlined", "Line", "Bar", "Pie", "axios", "jsxDEV", "_jsxDEV", "TabPane", "Panel", "QuotaManagement", "_s", "loading", "setLoading", "quotaItems", "setQuotaItems", "selectedRows", "setSelectedRows", "modalVisible", "setModalVisible", "drawerVisible", "setDrawerVisible", "activeTab", "setActiveTab", "form", "useForm", "searchForm", "costAnalysis", "setCostAnalysis", "chartData", "setChartData", "fetchQuotaItems", "params", "response", "get", "data", "error", "columns", "title", "dataIndex", "key", "width", "fixed", "ellipsis", "align", "render", "value", "toFixed", "_", "record", "children", "type", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "handleEdit", "danger", "handleDelete", "handleCreate", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delete", "id", "success", "handleSubmit", "values", "put", "post", "handleImport", "file", "formData", "FormData", "append", "handleCostAnalysis", "ids", "map", "row", "laborCost", "name", "materialCost", "machineCost", "flat", "searchFormItems", "layout", "<PERSON><PERSON>", "label", "placeholder", "allowClear", "getFieldsValue", "toolBar", "accept", "showUploadList", "beforeUpload", "disabled", "length", "CostForm", "prefix", "gutter", "span", "rules", "required", "style", "min", "precision", "direction", "size", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "rowSelection", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onChange", "rows", "getFieldValue", "visible", "onOk", "submit", "onCancel", "onFinish", "hidden", "TextArea", "active<PERSON><PERSON>", "tab", "placement", "onClose", "totalLaborCost", "totalMaterialCost", "totalMachineCost", "angleField", "colorField", "radius", "content", "percent", "xField", "yField", "seriesField", "isStack", "position", "header", "resourceDetails", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/src/pages/cost/QuotaManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Table,\r\n  Card,\r\n  Button,\r\n  Space,\r\n  Upload,\r\n  message,\r\n  Modal,\r\n  Form,\r\n  Input,\r\n  InputNumber,\r\n  Tabs,\r\n  Tooltip,\r\n  Tag,\r\n  Drawer,\r\n  Statistic,\r\n  Row,\r\n  Col,\r\n  Collapse\r\n} from 'antd';\r\nimport {\r\n  UploadOutlined,\r\n  DownloadOutlined,\r\n  PlusOutlined,\r\n  CalculatorOutlined,\r\n  DeleteOutlined,\r\n  EditOutlined,\r\n  SearchOutlined,\r\n  FileExcelOutlined,\r\n  BarChartOutlined,\r\n  TeamOutlined,\r\n  ShoppingOutlined,\r\n  ToolOutlined\r\n} from '@ant-design/icons';\r\nimport { Line, Bar, Pie } from '@ant-design/charts';\r\nimport axios from 'axios';\r\n\r\nconst { TabPane } = Tabs;\r\nconst { Panel } = Collapse;\r\n\r\nconst QuotaManagement = () => {\r\n  // 状态管理\r\n  const [loading, setLoading] = useState(false);\r\n  const [quotaItems, setQuotaItems] = useState([]);\r\n  const [selectedRows, setSelectedRows] = useState([]);\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [drawerVisible, setDrawerVisible] = useState(false);\r\n  const [activeTab, setActiveTab] = useState('labor');\r\n  const [form] = Form.useForm();\r\n  const [searchForm] = Form.useForm();\r\n  const [costAnalysis, setCostAnalysis] = useState(null);\r\n  const [chartData, setChartData] = useState([]);\r\n\r\n  // 初始化加载\r\n  useEffect(() => {\r\n    fetchQuotaItems();\r\n  }, []);\r\n\r\n  // 获取定额列表\r\n  const fetchQuotaItems = async (params = {}) => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await axios.get('/api/quotas', { params });\r\n      setQuotaItems(response.data);\r\n    } catch (error) {\r\n      message.error('获取定额列表失败：' + error.message);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 表格列定义\r\n  const columns = [\r\n    {\r\n      title: '编号',\r\n      dataIndex: 'code',\r\n      key: 'code',\r\n      width: 120,\r\n      fixed: 'left'\r\n    },\r\n    {\r\n      title: '名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n      width: 200\r\n    },\r\n    {\r\n      title: '描述',\r\n      dataIndex: 'description',\r\n      key: 'description',\r\n      width: 300,\r\n      ellipsis: true\r\n    },\r\n    {\r\n      title: '单位',\r\n      dataIndex: 'unit',\r\n      key: 'unit',\r\n      width: 80\r\n    },\r\n    {\r\n      title: '人工费',\r\n      dataIndex: 'laborCost',\r\n      key: 'laborCost',\r\n      width: 100,\r\n      align: 'right',\r\n      render: (value) => value?.toFixed(2)\r\n    },\r\n    {\r\n      title: '材料费',\r\n      dataIndex: 'materialCost',\r\n      key: 'materialCost',\r\n      width: 100,\r\n      align: 'right',\r\n      render: (value) => value?.toFixed(2)\r\n    },\r\n    {\r\n      title: '机械费',\r\n      dataIndex: 'machineCost',\r\n      key: 'machineCost',\r\n      width: 100,\r\n      align: 'right',\r\n      render: (value) => value?.toFixed(2)\r\n    },\r\n    {\r\n      title: '综合单价',\r\n      dataIndex: 'unitPrice',\r\n      key: 'unitPrice',\r\n      width: 120,\r\n      align: 'right',\r\n      render: (value) => value?.toFixed(2)\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      width: 150,\r\n      fixed: 'right',\r\n      render: (_, record) => (\r\n        <Space>\r\n          <Tooltip title=\"编辑\">\r\n            <Button\r\n              type=\"link\"\r\n              icon={<EditOutlined />}\r\n              onClick={() => handleEdit(record)}\r\n            />\r\n          </Tooltip>\r\n          <Tooltip title=\"删除\">\r\n            <Button\r\n              type=\"link\"\r\n              danger\r\n              icon={<DeleteOutlined />}\r\n              onClick={() => handleDelete(record)}\r\n            />\r\n          </Tooltip>\r\n        </Space>\r\n      )\r\n    }\r\n  ];\r\n\r\n  // 处理创建\r\n  const handleCreate = () => {\r\n    form.resetFields();\r\n    setModalVisible(true);\r\n  };\r\n\r\n  // 处理编辑\r\n  const handleEdit = (record) => {\r\n    form.setFieldsValue(record);\r\n    setModalVisible(true);\r\n  };\r\n\r\n  // 处理删除\r\n  const handleDelete = async (record) => {\r\n    try {\r\n      await axios.delete(`/api/quotas/${record.id}`);\r\n      message.success('删除成功');\r\n      fetchQuotaItems();\r\n    } catch (error) {\r\n      message.error('删除失败：' + error.message);\r\n    }\r\n  };\r\n\r\n  // 处理表单提交\r\n  const handleSubmit = async (values) => {\r\n    try {\r\n      if (values.id) {\r\n        await axios.put(`/api/quotas/${values.id}`, values);\r\n      } else {\r\n        await axios.post('/api/quotas', values);\r\n      }\r\n      message.success('保存成功');\r\n      setModalVisible(false);\r\n      fetchQuotaItems();\r\n    } catch (error) {\r\n      message.error('保存失败：' + error.message);\r\n    }\r\n  };\r\n\r\n  // 处理导入\r\n  const handleImport = async (file) => {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    try {\r\n      await axios.post('/api/quotas/import/excel', formData);\r\n      message.success('导入成功');\r\n      fetchQuotaItems();\r\n      return false;\r\n    } catch (error) {\r\n      message.error('导入失败：' + error.message);\r\n      return false;\r\n    }\r\n  };\r\n\r\n  // 处理成本分析\r\n  const handleCostAnalysis = async () => {\r\n    try {\r\n      const response = await axios.get('/api/quotas/resource-consumption', {\r\n        params: { ids: selectedRows.map(row => row.id) }\r\n      });\r\n      setCostAnalysis(response.data);\r\n      setDrawerVisible(true);\r\n\r\n      // 生成图表数据\r\n      const data = selectedRows.map(row => [\r\n        { type: '人工费', value: row.laborCost, name: row.name },\r\n        { type: '材料费', value: row.materialCost, name: row.name },\r\n        { type: '机械费', value: row.machineCost, name: row.name }\r\n      ]).flat();\r\n      setChartData(data);\r\n    } catch (error) {\r\n      message.error('成本分析失败：' + error.message);\r\n    }\r\n  };\r\n\r\n  // 搜索表单\r\n  const searchFormItems = (\r\n    <Form form={searchForm} layout=\"inline\">\r\n      <Form.Item name=\"keyword\" label=\"关键词\">\r\n        <Input placeholder=\"编号/名称/描述\" allowClear />\r\n      </Form.Item>\r\n      <Form.Item>\r\n        <Button type=\"primary\" icon={<SearchOutlined />} onClick={() => {\r\n          const values = searchForm.getFieldsValue();\r\n          fetchQuotaItems(values);\r\n        }}>\r\n          搜索\r\n        </Button>\r\n      </Form.Item>\r\n    </Form>\r\n  );\r\n\r\n  // 工具栏\r\n  const toolBar = (\r\n    <Space>\r\n      <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleCreate}>\r\n        新建定额\r\n      </Button>\r\n      <Upload\r\n        accept=\".xlsx,.xls,.md\"\r\n        showUploadList={false}\r\n        beforeUpload={handleImport}\r\n      >\r\n        <Button icon={<UploadOutlined />}>导入</Button>\r\n      </Upload>\r\n      <Button\r\n        icon={<CalculatorOutlined />}\r\n        disabled={selectedRows.length === 0}\r\n        onClick={handleCostAnalysis}\r\n      >\r\n        成本分析\r\n      </Button>\r\n    </Space>\r\n  );\r\n\r\n  // 成本表单\r\n  const CostForm = ({ prefix, disabled }) => (\r\n    <Row gutter={16}>\r\n      <Col span={8}>\r\n        <Form.Item\r\n          name={`${prefix}BasicCost`}\r\n          label=\"基本费用\"\r\n          rules={[{ required: true, message: '请输入基本费用' }]}\r\n        >\r\n          <InputNumber\r\n            disabled={disabled}\r\n            style={{ width: '100%' }}\r\n            min={0}\r\n            precision={2}\r\n          />\r\n        </Form.Item>\r\n      </Col>\r\n      <Col span={8}>\r\n        <Form.Item\r\n          name={`${prefix}AdditionalCost`}\r\n          label=\"附加费用\"\r\n          rules={[{ required: true, message: '请输入附加费用' }]}\r\n        >\r\n          <InputNumber\r\n            disabled={disabled}\r\n            style={{ width: '100%' }}\r\n            min={0}\r\n            precision={2}\r\n          />\r\n        </Form.Item>\r\n      </Col>\r\n      <Col span={8}>\r\n        <Form.Item\r\n          name={`${prefix}OtherCost`}\r\n          label=\"其他费用\"\r\n        >\r\n          <InputNumber\r\n            disabled={disabled}\r\n            style={{ width: '100%' }}\r\n            min={0}\r\n            precision={2}\r\n          />\r\n        </Form.Item>\r\n      </Col>\r\n    </Row>\r\n  );\r\n\r\n  return (\r\n    <Card>\r\n      <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\r\n        {searchFormItems}\r\n        {toolBar}\r\n        <Table\r\n          columns={columns}\r\n          dataSource={quotaItems}\r\n          rowKey=\"id\"\r\n          loading={loading}\r\n          scroll={{ x: 1500 }}\r\n          rowSelection={{\r\n            selectedRowKeys: selectedRows.map(row => row.id),\r\n            onChange: (_, rows) => setSelectedRows(rows)\r\n          }}\r\n        />\r\n      </Space>\r\n\r\n      {/* 编辑表单 */}\r\n      <Modal\r\n        title={form.getFieldValue('id') ? '编辑定额' : '新建定额'}\r\n        visible={modalVisible}\r\n        onOk={() => form.submit()}\r\n        onCancel={() => setModalVisible(false)}\r\n        width={1000}\r\n      >\r\n        <Form\r\n          form={form}\r\n          layout=\"vertical\"\r\n          onFinish={handleSubmit}\r\n        >\r\n          <Form.Item name=\"id\" hidden>\r\n            <Input />\r\n          </Form.Item>\r\n          <Row gutter={16}>\r\n            <Col span={8}>\r\n              <Form.Item\r\n                name=\"code\"\r\n                label=\"编号\"\r\n                rules={[{ required: true, message: '请输入编号' }]}\r\n              >\r\n                <Input placeholder=\"请输入编号\" />\r\n              </Form.Item>\r\n            </Col>\r\n            <Col span={8}>\r\n              <Form.Item\r\n                name=\"name\"\r\n                label=\"名称\"\r\n                rules={[{ required: true, message: '请输入名称' }]}\r\n              >\r\n                <Input placeholder=\"请输入名称\" />\r\n              </Form.Item>\r\n            </Col>\r\n            <Col span={8}>\r\n              <Form.Item\r\n                name=\"unit\"\r\n                label=\"单位\"\r\n                rules={[{ required: true, message: '请输入单位' }]}\r\n              >\r\n                <Input placeholder=\"请输入单位\" />\r\n              </Form.Item>\r\n            </Col>\r\n          </Row>\r\n\r\n          <Form.Item\r\n            name=\"description\"\r\n            label=\"描述\"\r\n          >\r\n            <Input.TextArea rows={4} placeholder=\"请输入描述\" />\r\n          </Form.Item>\r\n\r\n          <Tabs activeKey={activeTab} onChange={setActiveTab}>\r\n            <TabPane\r\n              tab={<span><TeamOutlined />人工费</span>}\r\n              key=\"labor\"\r\n            >\r\n              <CostForm prefix=\"labor\" />\r\n            </TabPane>\r\n            <TabPane\r\n              tab={<span><ShoppingOutlined />材料费</span>}\r\n              key=\"material\"\r\n            >\r\n              <CostForm prefix=\"material\" />\r\n            </TabPane>\r\n            <TabPane\r\n              tab={<span><ToolOutlined />机械费</span>}\r\n              key=\"machine\"\r\n            >\r\n              <CostForm prefix=\"machine\" />\r\n            </TabPane>\r\n          </Tabs>\r\n        </Form>\r\n      </Modal>\r\n\r\n      {/* 成本分析抽屉 */}\r\n      <Drawer\r\n        title=\"成本分析\"\r\n        placement=\"right\"\r\n        width={800}\r\n        visible={drawerVisible}\r\n        onClose={() => setDrawerVisible(false)}\r\n      >\r\n        {costAnalysis && (\r\n          <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\r\n            <Row gutter={16}>\r\n              <Col span={8}>\r\n                <Statistic\r\n                  title=\"人工费合计\"\r\n                  value={costAnalysis.totalLaborCost}\r\n                  precision={2}\r\n                  prefix=\"¥\"\r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <Statistic\r\n                  title=\"材料费合计\"\r\n                  value={costAnalysis.totalMaterialCost}\r\n                  precision={2}\r\n                  prefix=\"¥\"\r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <Statistic\r\n                  title=\"机械费合计\"\r\n                  value={costAnalysis.totalMachineCost}\r\n                  precision={2}\r\n                  prefix=\"¥\"\r\n                />\r\n              </Col>\r\n            </Row>\r\n\r\n            <Card title=\"成本构成\" size=\"small\">\r\n              <Pie\r\n                data={chartData}\r\n                angleField=\"value\"\r\n                colorField=\"type\"\r\n                radius={0.8}\r\n                label={{\r\n                  type: 'outer',\r\n                  content: (data) => `${data.type}: ${(data.percent * 100).toFixed(1)}%`\r\n                }}\r\n              />\r\n            </Card>\r\n\r\n            <Card title=\"成本明细\" size=\"small\">\r\n              <Bar\r\n                data={chartData}\r\n                xField=\"value\"\r\n                yField=\"name\"\r\n                seriesField=\"type\"\r\n                isStack={true}\r\n                label={{\r\n                  position: 'middle'\r\n                }}\r\n              />\r\n            </Card>\r\n\r\n            <Collapse>\r\n              <Panel header=\"资源消耗明细\" key=\"1\">\r\n                <Table\r\n                  dataSource={costAnalysis.resourceDetails}\r\n                  columns={[\r\n                    { title: '资源名称', dataIndex: 'name' },\r\n                    { title: '单位', dataIndex: 'unit' },\r\n                    { title: '数量', dataIndex: 'quantity' },\r\n                    { title: '单价', dataIndex: 'price' },\r\n                    { title: '合价', dataIndex: 'total' }\r\n                  ]}\r\n                  size=\"small\"\r\n                />\r\n              </Panel>\r\n            </Collapse>\r\n          </Space>\r\n        )}\r\n      </Drawer>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default QuotaManagement;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,IAAI,EACJC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,GAAG,EACHC,QAAQ,QACH,MAAM;AACb,SACEC,cAAc,EACdC,gBAAgB,EAChBC,YAAY,EACZC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,iBAAiB,EACjBC,gBAAgB,EAChBC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,QACP,mBAAmB;AAC1B,SAASC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,oBAAoB;AACnD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC;AAAQ,CAAC,GAAG1B,IAAI;AACxB,MAAM;EAAE2B;AAAM,CAAC,GAAGpB,QAAQ;AAE1B,MAAMqB,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAACsD,IAAI,CAAC,GAAG7C,IAAI,CAAC8C,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,CAAC,GAAG/C,IAAI,CAAC8C,OAAO,CAAC,CAAC;EACnC,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd4D,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,eAAe,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC7C,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMoB,QAAQ,GAAG,MAAM5B,KAAK,CAAC6B,GAAG,CAAC,aAAa,EAAE;QAAEF;MAAO,CAAC,CAAC;MAC3DjB,aAAa,CAACkB,QAAQ,CAACE,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3D,OAAO,CAAC2D,KAAK,CAAC,WAAW,GAAGA,KAAK,CAAC3D,OAAO,CAAC;IAC5C,CAAC,SAAS;MACRoC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwB,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVE,QAAQ,EAAE;EACZ,CAAC,EACD;IACEL,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVG,KAAK,EAAE,OAAO;IACdC,MAAM,EAAGC,KAAK,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EACrC,CAAC,EACD;IACET,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVG,KAAK,EAAE,OAAO;IACdC,MAAM,EAAGC,KAAK,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EACrC,CAAC,EACD;IACET,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVG,KAAK,EAAE,OAAO;IACdC,MAAM,EAAGC,KAAK,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EACrC,CAAC,EACD;IACET,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVG,KAAK,EAAE,OAAO;IACdC,MAAM,EAAGC,KAAK,IAAKA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,OAAO,CAAC,CAAC;EACrC,CAAC,EACD;IACET,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,OAAO;IACdG,MAAM,EAAEA,CAACG,CAAC,EAAEC,MAAM,kBAChB1C,OAAA,CAAChC,KAAK;MAAA2E,QAAA,gBACJ3C,OAAA,CAACxB,OAAO;QAACuD,KAAK,EAAC,cAAI;QAAAY,QAAA,eACjB3C,OAAA,CAACjC,MAAM;UACL6E,IAAI,EAAC,MAAM;UACXC,IAAI,eAAE7C,OAAA,CAACZ,YAAY;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,OAAO,EAAEA,CAAA,KAAMC,UAAU,CAACT,MAAM;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVjD,OAAA,CAACxB,OAAO;QAACuD,KAAK,EAAC,cAAI;QAAAY,QAAA,eACjB3C,OAAA,CAACjC,MAAM;UACL6E,IAAI,EAAC,MAAM;UACXQ,MAAM;UACNP,IAAI,eAAE7C,OAAA,CAACb,cAAc;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBC,OAAO,EAAEA,CAAA,KAAMG,YAAY,CAACX,MAAM;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;;EAED;EACA,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzBrC,IAAI,CAACsC,WAAW,CAAC,CAAC;IAClB3C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuC,UAAU,GAAIT,MAAM,IAAK;IAC7BzB,IAAI,CAACuC,cAAc,CAACd,MAAM,CAAC;IAC3B9B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMyC,YAAY,GAAG,MAAOX,MAAM,IAAK;IACrC,IAAI;MACF,MAAM5C,KAAK,CAAC2D,MAAM,CAAC,eAAef,MAAM,CAACgB,EAAE,EAAE,CAAC;MAC9CxF,OAAO,CAACyF,OAAO,CAAC,MAAM,CAAC;MACvBnC,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd3D,OAAO,CAAC2D,KAAK,CAAC,OAAO,GAAGA,KAAK,CAAC3D,OAAO,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAM0F,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MACF,IAAIA,MAAM,CAACH,EAAE,EAAE;QACb,MAAM5D,KAAK,CAACgE,GAAG,CAAC,eAAeD,MAAM,CAACH,EAAE,EAAE,EAAEG,MAAM,CAAC;MACrD,CAAC,MAAM;QACL,MAAM/D,KAAK,CAACiE,IAAI,CAAC,aAAa,EAAEF,MAAM,CAAC;MACzC;MACA3F,OAAO,CAACyF,OAAO,CAAC,MAAM,CAAC;MACvB/C,eAAe,CAAC,KAAK,CAAC;MACtBY,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd3D,OAAO,CAAC2D,KAAK,CAAC,OAAO,GAAGA,KAAK,CAAC3D,OAAO,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAM8F,YAAY,GAAG,MAAOC,IAAI,IAAK;IACnC,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;IAC7B,IAAI;MACF,MAAMnE,KAAK,CAACiE,IAAI,CAAC,0BAA0B,EAAEG,QAAQ,CAAC;MACtDhG,OAAO,CAACyF,OAAO,CAAC,MAAM,CAAC;MACvBnC,eAAe,CAAC,CAAC;MACjB,OAAO,KAAK;IACd,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd3D,OAAO,CAAC2D,KAAK,CAAC,OAAO,GAAGA,KAAK,CAAC3D,OAAO,CAAC;MACtC,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAMmG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAM3C,QAAQ,GAAG,MAAM5B,KAAK,CAAC6B,GAAG,CAAC,kCAAkC,EAAE;QACnEF,MAAM,EAAE;UAAE6C,GAAG,EAAE7D,YAAY,CAAC8D,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACd,EAAE;QAAE;MACjD,CAAC,CAAC;MACFrC,eAAe,CAACK,QAAQ,CAACE,IAAI,CAAC;MAC9Bd,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAMc,IAAI,GAAGnB,YAAY,CAAC8D,GAAG,CAACC,GAAG,IAAI,CACnC;QAAE5B,IAAI,EAAE,KAAK;QAAEL,KAAK,EAAEiC,GAAG,CAACC,SAAS;QAAEC,IAAI,EAAEF,GAAG,CAACE;MAAK,CAAC,EACrD;QAAE9B,IAAI,EAAE,KAAK;QAAEL,KAAK,EAAEiC,GAAG,CAACG,YAAY;QAAED,IAAI,EAAEF,GAAG,CAACE;MAAK,CAAC,EACxD;QAAE9B,IAAI,EAAE,KAAK;QAAEL,KAAK,EAAEiC,GAAG,CAACI,WAAW;QAAEF,IAAI,EAAEF,GAAG,CAACE;MAAK,CAAC,CACxD,CAAC,CAACG,IAAI,CAAC,CAAC;MACTtD,YAAY,CAACK,IAAI,CAAC;IACpB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3D,OAAO,CAAC2D,KAAK,CAAC,SAAS,GAAGA,KAAK,CAAC3D,OAAO,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAM4G,eAAe,gBACnB9E,OAAA,CAAC5B,IAAI;IAAC6C,IAAI,EAAEE,UAAW;IAAC4D,MAAM,EAAC,QAAQ;IAAApC,QAAA,gBACrC3C,OAAA,CAAC5B,IAAI,CAAC4G,IAAI;MAACN,IAAI,EAAC,SAAS;MAACO,KAAK,EAAC,oBAAK;MAAAtC,QAAA,eACnC3C,OAAA,CAAC3B,KAAK;QAAC6G,WAAW,EAAC,wCAAU;QAACC,UAAU;MAAA;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,eACZjD,OAAA,CAAC5B,IAAI,CAAC4G,IAAI;MAAArC,QAAA,eACR3C,OAAA,CAACjC,MAAM;QAAC6E,IAAI,EAAC,SAAS;QAACC,IAAI,eAAE7C,OAAA,CAACX,cAAc;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACC,OAAO,EAAEA,CAAA,KAAM;UAC9D,MAAMW,MAAM,GAAG1C,UAAU,CAACiE,cAAc,CAAC,CAAC;UAC1C5D,eAAe,CAACqC,MAAM,CAAC;QACzB,CAAE;QAAAlB,QAAA,EAAC;MAEH;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CACP;;EAED;EACA,MAAMoC,OAAO,gBACXrF,OAAA,CAAChC,KAAK;IAAA2E,QAAA,gBACJ3C,OAAA,CAACjC,MAAM;MAAC6E,IAAI,EAAC,SAAS;MAACC,IAAI,eAAE7C,OAAA,CAACf,YAAY;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAACC,OAAO,EAAEI,YAAa;MAAAX,QAAA,EAAC;IAEtE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACTjD,OAAA,CAAC/B,MAAM;MACLqH,MAAM,EAAC,gBAAgB;MACvBC,cAAc,EAAE,KAAM;MACtBC,YAAY,EAAExB,YAAa;MAAArB,QAAA,eAE3B3C,OAAA,CAACjC,MAAM;QAAC8E,IAAI,eAAE7C,OAAA,CAACjB,cAAc;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAN,QAAA,EAAC;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eACTjD,OAAA,CAACjC,MAAM;MACL8E,IAAI,eAAE7C,OAAA,CAACd,kBAAkB;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC7BwC,QAAQ,EAAEhF,YAAY,CAACiF,MAAM,KAAK,CAAE;MACpCxC,OAAO,EAAEmB,kBAAmB;MAAA1B,QAAA,EAC7B;IAED;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACR;;EAED;EACA,MAAM0C,QAAQ,GAAGA,CAAC;IAAEC,MAAM;IAAEH;EAAS,CAAC,kBACpCzF,OAAA,CAACpB,GAAG;IAACiH,MAAM,EAAE,EAAG;IAAAlD,QAAA,gBACd3C,OAAA,CAACnB,GAAG;MAACiH,IAAI,EAAE,CAAE;MAAAnD,QAAA,eACX3C,OAAA,CAAC5B,IAAI,CAAC4G,IAAI;QACRN,IAAI,EAAE,GAAGkB,MAAM,WAAY;QAC3BX,KAAK,EAAC,0BAAM;QACZc,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAE9H,OAAO,EAAE;QAAU,CAAC,CAAE;QAAAyE,QAAA,eAEhD3C,OAAA,CAAC1B,WAAW;UACVmH,QAAQ,EAAEA,QAAS;UACnBQ,KAAK,EAAE;YAAE/D,KAAK,EAAE;UAAO,CAAE;UACzBgE,GAAG,EAAE,CAAE;UACPC,SAAS,EAAE;QAAE;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eACNjD,OAAA,CAACnB,GAAG;MAACiH,IAAI,EAAE,CAAE;MAAAnD,QAAA,eACX3C,OAAA,CAAC5B,IAAI,CAAC4G,IAAI;QACRN,IAAI,EAAE,GAAGkB,MAAM,gBAAiB;QAChCX,KAAK,EAAC,0BAAM;QACZc,KAAK,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAE9H,OAAO,EAAE;QAAU,CAAC,CAAE;QAAAyE,QAAA,eAEhD3C,OAAA,CAAC1B,WAAW;UACVmH,QAAQ,EAAEA,QAAS;UACnBQ,KAAK,EAAE;YAAE/D,KAAK,EAAE;UAAO,CAAE;UACzBgE,GAAG,EAAE,CAAE;UACPC,SAAS,EAAE;QAAE;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eACNjD,OAAA,CAACnB,GAAG;MAACiH,IAAI,EAAE,CAAE;MAAAnD,QAAA,eACX3C,OAAA,CAAC5B,IAAI,CAAC4G,IAAI;QACRN,IAAI,EAAE,GAAGkB,MAAM,WAAY;QAC3BX,KAAK,EAAC,0BAAM;QAAAtC,QAAA,eAEZ3C,OAAA,CAAC1B,WAAW;UACVmH,QAAQ,EAAEA,QAAS;UACnBQ,KAAK,EAAE;YAAE/D,KAAK,EAAE;UAAO,CAAE;UACzBgE,GAAG,EAAE,CAAE;UACPC,SAAS,EAAE;QAAE;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEjD,OAAA,CAAClC,IAAI;IAAA6E,QAAA,gBACH3C,OAAA,CAAChC,KAAK;MAACoI,SAAS,EAAC,UAAU;MAACH,KAAK,EAAE;QAAE/D,KAAK,EAAE;MAAO,CAAE;MAACmE,IAAI,EAAC,OAAO;MAAA1D,QAAA,GAC/DmC,eAAe,EACfO,OAAO,eACRrF,OAAA,CAACnC,KAAK;QACJiE,OAAO,EAAEA,OAAQ;QACjBwE,UAAU,EAAE/F,UAAW;QACvBgG,MAAM,EAAC,IAAI;QACXlG,OAAO,EAAEA,OAAQ;QACjBmG,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,YAAY,EAAE;UACZC,eAAe,EAAElG,YAAY,CAAC8D,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACd,EAAE,CAAC;UAChDkD,QAAQ,EAAEA,CAACnE,CAAC,EAAEoE,IAAI,KAAKnG,eAAe,CAACmG,IAAI;QAC7C;MAAE;QAAA/D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGRjD,OAAA,CAAC7B,KAAK;MACJ4D,KAAK,EAAEd,IAAI,CAAC6F,aAAa,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,MAAO;MAClDC,OAAO,EAAEpG,YAAa;MACtBqG,IAAI,EAAEA,CAAA,KAAM/F,IAAI,CAACgG,MAAM,CAAC,CAAE;MAC1BC,QAAQ,EAAEA,CAAA,KAAMtG,eAAe,CAAC,KAAK,CAAE;MACvCsB,KAAK,EAAE,IAAK;MAAAS,QAAA,eAEZ3C,OAAA,CAAC5B,IAAI;QACH6C,IAAI,EAAEA,IAAK;QACX8D,MAAM,EAAC,UAAU;QACjBoC,QAAQ,EAAEvD,YAAa;QAAAjB,QAAA,gBAEvB3C,OAAA,CAAC5B,IAAI,CAAC4G,IAAI;UAACN,IAAI,EAAC,IAAI;UAAC0C,MAAM;UAAAzE,QAAA,eACzB3C,OAAA,CAAC3B,KAAK;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZjD,OAAA,CAACpB,GAAG;UAACiH,MAAM,EAAE,EAAG;UAAAlD,QAAA,gBACd3C,OAAA,CAACnB,GAAG;YAACiH,IAAI,EAAE,CAAE;YAAAnD,QAAA,eACX3C,OAAA,CAAC5B,IAAI,CAAC4G,IAAI;cACRN,IAAI,EAAC,MAAM;cACXO,KAAK,EAAC,cAAI;cACVc,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9H,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAyE,QAAA,eAE9C3C,OAAA,CAAC3B,KAAK;gBAAC6G,WAAW,EAAC;cAAO;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjD,OAAA,CAACnB,GAAG;YAACiH,IAAI,EAAE,CAAE;YAAAnD,QAAA,eACX3C,OAAA,CAAC5B,IAAI,CAAC4G,IAAI;cACRN,IAAI,EAAC,MAAM;cACXO,KAAK,EAAC,cAAI;cACVc,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9H,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAyE,QAAA,eAE9C3C,OAAA,CAAC3B,KAAK;gBAAC6G,WAAW,EAAC;cAAO;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjD,OAAA,CAACnB,GAAG;YAACiH,IAAI,EAAE,CAAE;YAAAnD,QAAA,eACX3C,OAAA,CAAC5B,IAAI,CAAC4G,IAAI;cACRN,IAAI,EAAC,MAAM;cACXO,KAAK,EAAC,cAAI;cACVc,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9H,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAyE,QAAA,eAE9C3C,OAAA,CAAC3B,KAAK;gBAAC6G,WAAW,EAAC;cAAO;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjD,OAAA,CAAC5B,IAAI,CAAC4G,IAAI;UACRN,IAAI,EAAC,aAAa;UAClBO,KAAK,EAAC,cAAI;UAAAtC,QAAA,eAEV3C,OAAA,CAAC3B,KAAK,CAACgJ,QAAQ;YAACR,IAAI,EAAE,CAAE;YAAC3B,WAAW,EAAC;UAAO;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eAEZjD,OAAA,CAACzB,IAAI;UAAC+I,SAAS,EAAEvG,SAAU;UAAC6F,QAAQ,EAAE5F,YAAa;UAAA2B,QAAA,gBACjD3C,OAAA,CAACC,OAAO;YACNsH,GAAG,eAAEvH,OAAA;cAAA2C,QAAA,gBAAM3C,OAAA,CAACR,YAAY;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAAG;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YAAAN,QAAA,eAGtC3C,OAAA,CAAC2F,QAAQ;cAACC,MAAM,EAAC;YAAO;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GAFvB,OAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGJ,CAAC,eACVjD,OAAA,CAACC,OAAO;YACNsH,GAAG,eAAEvH,OAAA;cAAA2C,QAAA,gBAAM3C,OAAA,CAACP,gBAAgB;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAAG;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YAAAN,QAAA,eAG1C3C,OAAA,CAAC2F,QAAQ;cAACC,MAAM,EAAC;YAAU;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GAF1B,UAAU;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGP,CAAC,eACVjD,OAAA,CAACC,OAAO;YACNsH,GAAG,eAAEvH,OAAA;cAAA2C,QAAA,gBAAM3C,OAAA,CAACN,YAAY;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAAG;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YAAAN,QAAA,eAGtC3C,OAAA,CAAC2F,QAAQ;cAACC,MAAM,EAAC;YAAS;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GAFzB,SAAS;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRjD,OAAA,CAACtB,MAAM;MACLqD,KAAK,EAAC,0BAAM;MACZyF,SAAS,EAAC,OAAO;MACjBtF,KAAK,EAAE,GAAI;MACX6E,OAAO,EAAElG,aAAc;MACvB4G,OAAO,EAAEA,CAAA,KAAM3G,gBAAgB,CAAC,KAAK,CAAE;MAAA6B,QAAA,EAEtCvB,YAAY,iBACXpB,OAAA,CAAChC,KAAK;QAACoI,SAAS,EAAC,UAAU;QAACH,KAAK,EAAE;UAAE/D,KAAK,EAAE;QAAO,CAAE;QAACmE,IAAI,EAAC,OAAO;QAAA1D,QAAA,gBAChE3C,OAAA,CAACpB,GAAG;UAACiH,MAAM,EAAE,EAAG;UAAAlD,QAAA,gBACd3C,OAAA,CAACnB,GAAG;YAACiH,IAAI,EAAE,CAAE;YAAAnD,QAAA,eACX3C,OAAA,CAACrB,SAAS;cACRoD,KAAK,EAAC,gCAAO;cACbQ,KAAK,EAAEnB,YAAY,CAACsG,cAAe;cACnCvB,SAAS,EAAE,CAAE;cACbP,MAAM,EAAC;YAAG;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNjD,OAAA,CAACnB,GAAG;YAACiH,IAAI,EAAE,CAAE;YAAAnD,QAAA,eACX3C,OAAA,CAACrB,SAAS;cACRoD,KAAK,EAAC,gCAAO;cACbQ,KAAK,EAAEnB,YAAY,CAACuG,iBAAkB;cACtCxB,SAAS,EAAE,CAAE;cACbP,MAAM,EAAC;YAAG;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNjD,OAAA,CAACnB,GAAG;YAACiH,IAAI,EAAE,CAAE;YAAAnD,QAAA,eACX3C,OAAA,CAACrB,SAAS;cACRoD,KAAK,EAAC,gCAAO;cACbQ,KAAK,EAAEnB,YAAY,CAACwG,gBAAiB;cACrCzB,SAAS,EAAE,CAAE;cACbP,MAAM,EAAC;YAAG;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjD,OAAA,CAAClC,IAAI;UAACiE,KAAK,EAAC,0BAAM;UAACsE,IAAI,EAAC,OAAO;UAAA1D,QAAA,eAC7B3C,OAAA,CAACH,GAAG;YACF+B,IAAI,EAAEN,SAAU;YAChBuG,UAAU,EAAC,OAAO;YAClBC,UAAU,EAAC,MAAM;YACjBC,MAAM,EAAE,GAAI;YACZ9C,KAAK,EAAE;cACLrC,IAAI,EAAE,OAAO;cACboF,OAAO,EAAGpG,IAAI,IAAK,GAAGA,IAAI,CAACgB,IAAI,KAAK,CAAChB,IAAI,CAACqG,OAAO,GAAG,GAAG,EAAEzF,OAAO,CAAC,CAAC,CAAC;YACrE;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPjD,OAAA,CAAClC,IAAI;UAACiE,KAAK,EAAC,0BAAM;UAACsE,IAAI,EAAC,OAAO;UAAA1D,QAAA,eAC7B3C,OAAA,CAACJ,GAAG;YACFgC,IAAI,EAAEN,SAAU;YAChB4G,MAAM,EAAC,OAAO;YACdC,MAAM,EAAC,MAAM;YACbC,WAAW,EAAC,MAAM;YAClBC,OAAO,EAAE,IAAK;YACdpD,KAAK,EAAE;cACLqD,QAAQ,EAAE;YACZ;UAAE;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPjD,OAAA,CAAClB,QAAQ;UAAA6D,QAAA,eACP3C,OAAA,CAACE,KAAK;YAACqI,MAAM,EAAC,sCAAQ;YAAA5F,QAAA,eACpB3C,OAAA,CAACnC,KAAK;cACJyI,UAAU,EAAElF,YAAY,CAACoH,eAAgB;cACzC1G,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,SAAS,EAAE;cAAO,CAAC,EACpC;gBAAED,KAAK,EAAE,IAAI;gBAAEC,SAAS,EAAE;cAAO,CAAC,EAClC;gBAAED,KAAK,EAAE,IAAI;gBAAEC,SAAS,EAAE;cAAW,CAAC,EACtC;gBAAED,KAAK,EAAE,IAAI;gBAAEC,SAAS,EAAE;cAAQ,CAAC,EACnC;gBAAED,KAAK,EAAE,IAAI;gBAAEC,SAAS,EAAE;cAAQ,CAAC,CACnC;cACFqE,IAAI,EAAC;YAAO;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC,GAXuB,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX,CAAC;AAAC7C,EAAA,CAzcID,eAAe;EAAA,QAQJ/B,IAAI,CAAC8C,OAAO,EACN9C,IAAI,CAAC8C,OAAO;AAAA;AAAAuH,EAAA,GAT7BtI,eAAe;AA2crB,eAAeA,eAAe;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}