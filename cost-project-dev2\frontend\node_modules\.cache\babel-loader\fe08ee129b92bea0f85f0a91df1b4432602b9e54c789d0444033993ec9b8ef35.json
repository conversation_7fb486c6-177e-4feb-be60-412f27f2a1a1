{"ast": null, "code": "import { Text as GText } from '@antv/g';\nimport { BaseShape } from './base-shape';\nimport { Image as GImage } from './image';\n/**\n * <zh/> 图标\n *\n * <en/> Icon\n * @remarks\n * <zh/> 图标是一种特殊的图形，可以是图片或者文字。传入 src 属性时，会渲染图片；传入 text 属性时，会渲染文字。\n *\n * <en/> Icon is a special shape, which can be an image or text. When the src attribute is passed in, an image will be rendered; when the text attribute is passed in, text will be rendered.\n */\nexport class Icon extends BaseShape {\n  constructor(options) {\n    super(options);\n  }\n  isImage() {\n    const {\n      src\n    } = this.attributes;\n    return !!src;\n  }\n  getIconStyle(attributes = this.attributes) {\n    const {\n      width = 0,\n      height = 0\n    } = attributes;\n    const style = this.getGraphicStyle(attributes);\n    if (this.isImage()) {\n      return Object.assign({\n        x: -width / 2,\n        y: -height / 2\n      }, style);\n    }\n    return Object.assign({\n      textBaseline: 'middle',\n      textAlign: 'center'\n    }, style);\n  }\n  render(attributes = this.attributes, container = this) {\n    this.upsert('icon', this.isImage() ? GImage : GText, this.getIconStyle(attributes), container);\n  }\n}", "map": {"version": 3, "names": ["Text", "GText", "BaseShape", "Image", "GImage", "Icon", "constructor", "options", "isImage", "src", "attributes", "getIconStyle", "width", "height", "style", "getGraphicStyle", "Object", "assign", "x", "y", "textBaseline", "textAlign", "render", "container", "upsert"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\g6\\src\\elements\\shapes\\icon.ts"], "sourcesContent": ["import { DisplayObjectConfig, Text as GText, Group, TextStyleProps } from '@antv/g';\nimport type { BaseShapeStyleProps } from './base-shape';\nimport { BaseShape } from './base-shape';\nimport type { ImageStyleProps } from './image';\nimport { Image as GImage } from './image';\n\n/**\n * <zh/> 图标样式\n *\n * <en/> Icon style\n */\nexport interface IconStyleProps extends BaseShapeStyleProps, Partial<TextStyleProps>, Omit<ImageStyleProps, 'z'> {}\n\n/**\n * <zh/> 图标\n *\n * <en/> Icon\n * @remarks\n * <zh/> 图标是一种特殊的图形，可以是图片或者文字。传入 src 属性时，会渲染图片；传入 text 属性时，会渲染文字。\n *\n * <en/> Icon is a special shape, which can be an image or text. When the src attribute is passed in, an image will be rendered; when the text attribute is passed in, text will be rendered.\n */\nexport class Icon extends BaseShape<IconStyleProps> {\n  constructor(options: DisplayObjectConfig<IconStyleProps>) {\n    super(options);\n  }\n\n  private isImage() {\n    const { src } = this.attributes;\n    return !!src;\n  }\n\n  protected getIconStyle(attributes: IconStyleProps = this.attributes): IconStyleProps {\n    const { width = 0, height = 0 } = attributes;\n    const style = this.getGraphicStyle(attributes);\n    if (this.isImage()) {\n      return {\n        x: -width / 2,\n        y: -height / 2,\n        ...style,\n      };\n    }\n    return {\n      textBaseline: 'middle',\n      textAlign: 'center',\n      ...style,\n    };\n  }\n\n  public render(attributes = this.attributes, container: Group = this): void {\n    this.upsert('icon', (this.isImage() ? GImage : GText) as any, this.getIconStyle(attributes), container);\n  }\n}\n"], "mappings": "AAAA,SAA8BA,IAAI,IAAIC,KAAK,QAA+B,SAAS;AAEnF,SAASC,SAAS,QAAQ,cAAc;AAExC,SAASC,KAAK,IAAIC,MAAM,QAAQ,SAAS;AASzC;;;;;;;;;AASA,OAAM,MAAOC,IAAK,SAAQH,SAAyB;EACjDI,YAAYC,OAA4C;IACtD,KAAK,CAACA,OAAO,CAAC;EAChB;EAEQC,OAAOA,CAAA;IACb,MAAM;MAAEC;IAAG,CAAE,GAAG,IAAI,CAACC,UAAU;IAC/B,OAAO,CAAC,CAACD,GAAG;EACd;EAEUE,YAAYA,CAACD,UAAA,GAA6B,IAAI,CAACA,UAAU;IACjE,MAAM;MAAEE,KAAK,GAAG,CAAC;MAAEC,MAAM,GAAG;IAAC,CAAE,GAAGH,UAAU;IAC5C,MAAMI,KAAK,GAAG,IAAI,CAACC,eAAe,CAACL,UAAU,CAAC;IAC9C,IAAI,IAAI,CAACF,OAAO,EAAE,EAAE;MAClB,OAAAQ,MAAA,CAAAC,MAAA;QACEC,CAAC,EAAE,CAACN,KAAK,GAAG,CAAC;QACbO,CAAC,EAAE,CAACN,MAAM,GAAG;MAAC,GACXC,KAAK;IAEZ;IACA,OAAAE,MAAA,CAAAC,MAAA;MACEG,YAAY,EAAE,QAAQ;MACtBC,SAAS,EAAE;IAAQ,GAChBP,KAAK;EAEZ;EAEOQ,MAAMA,CAACZ,UAAU,GAAG,IAAI,CAACA,UAAU,EAAEa,SAAA,GAAmB,IAAI;IACjE,IAAI,CAACC,MAAM,CAAC,MAAM,EAAG,IAAI,CAAChB,OAAO,EAAE,GAAGJ,MAAM,GAAGH,KAAK,EAAU,IAAI,CAACU,YAAY,CAACD,UAAU,CAAC,EAAEa,SAAS,CAAC;EACzG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}