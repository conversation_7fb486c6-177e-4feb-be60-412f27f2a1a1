# Stagewise工具条使用指南

## 概述

Stagewise工具条是一个AI辅助开发工具，专为Cost-Project项目定制，提供智能上下文感知和开发辅助功能。该工具条只在开发模式下可用，为开发者提供便捷的调试和分析工具。

## 安装配置

### 1. 依赖安装
```bash
cd frontend
npm install -D @stagewise/toolbar
```

### 2. 项目集成
工具条已集成到项目中，包含以下文件：
- `src/utils/stagewise.js` - 工具条配置和核心功能
- `src/components/StagewiseToolbar.js` - 可视化工具条组件
- `src/App.js` - 主应用集成

### 3. 自动初始化
工具条在应用启动时自动初始化，无需手动配置。

## 功能特性

### 🎯 智能上下文感知
工具条能够自动识别当前页面类型并提供相关的上下文信息：

- **首页**: 系统概览和快速导航
- **项目管理**: 项目创建、编辑、甘特图查看
- **成本分析**: 成本报告、趋势分析
- **清单管理**: 工程量清单操作
- **定额管理**: 项目定额设置
- **资源管理**: 人力、材料、设备管理

### 🔧 开发调试工具

#### 调试模式
- **启用**: 页面边框变红，控制台输出增强
- **功能**: 性能监控、错误追踪、状态检查
- **切换**: 点击调试按钮或使用工具条面板

#### 数据导出
- **页面数据**: 导出当前页面的元数据
- **甘特图数据**: 导出任务和时间线信息
- **成本报告**: 生成项目成本分析报告

### 📊 专业工具

#### 甘特图工具（项目管理页面）
- 检查甘特图组件状态
- 导出甘特图任务数据
- 截图功能（需要html2canvas库）

#### 成本分析工具（成本模块页面）
- 生成成本分析报告
- 成本预警检查
- 预算对比分析

## 使用方法

### 1. 访问工具条
在开发模式下，页面右侧会显示浮动工具按钮：
- 🔧 **主工具条按钮**: 打开工具条面板
- 🐛 **调试模式按钮**: 快速切换调试模式
- 📤 **导出按钮**: 快速导出当前页面数据

### 2. 工具条面板
点击主工具条按钮打开右侧抽屉面板，包含：

#### 状态信息
- 工具条初始化状态
- 当前页面路径
- 系统运行状态

#### 快速操作
- 调试模式切换
- 数据导出
- 项目信息查看

#### 页面信息
- 当前URL
- 视口尺寸
- 用户代理信息

#### 专业工具
根据当前页面动态显示相关工具

### 3. AI辅助功能
工具条提供以下AI辅助信息：

```javascript
// 页面上下文示例
当前页面: 项目管理
功能描述: 项目创建、编辑、删除和甘特图查看
可用操作: 创建项目, 编辑项目, 查看甘特图, 项目统计
```

## 配置说明

### 插件配置
工具条包含三个主要插件：

#### 1. cost-project-context
- **功能**: 提供页面上下文信息
- **操作**: 查看项目信息、导出数据、切换调试模式

#### 2. gantt-helper
- **功能**: 甘特图组件辅助
- **操作**: 甘特图截图、数据导出

#### 3. cost-analysis
- **功能**: 成本分析工具
- **操作**: 生成成本报告、预警检查

### 自定义配置
可以在 `src/utils/stagewise.js` 中修改配置：

```javascript
const stagewiseConfig = {
  plugins: [
    {
      name: 'custom-plugin',
      description: '自定义插件描述',
      shortInfoForPrompt: () => {
        return "自定义上下文信息";
      },
      actions: [
        {
          name: '自定义操作',
          description: '操作描述',
          execute: () => {
            // 自定义逻辑
          },
        },
      ],
    },
  ],
};
```

## 最佳实践

### 1. 开发流程
1. 启动开发服务器
2. 打开浏览器开发者工具
3. 使用工具条进行页面分析
4. 根据AI提示优化代码

### 2. 调试技巧
- 启用调试模式查看详细信息
- 使用数据导出功能保存状态
- 结合浏览器开发者工具使用

### 3. 性能优化
- 工具条仅在开发模式下加载
- 生产环境自动禁用
- 最小化对应用性能的影响

## 故障排除

### 常见问题

#### 1. 工具条未显示
- 确认当前为开发模式 (`NODE_ENV=development`)
- 检查控制台是否有初始化错误
- 确认依赖已正确安装

#### 2. 功能异常
- 检查浏览器控制台错误信息
- 确认页面元素选择器正确
- 验证localStorage权限

#### 3. AI功能不可用
- 确认网络连接正常
- 检查Cursor扩展是否已安装
- 验证工具条配置正确

### 调试命令
```bash
# 检查依赖
npm list @stagewise/toolbar

# 重新安装
npm install -D @stagewise/toolbar

# 清理缓存
npm start -- --reset-cache
```

## 更新日志

### v1.0.0 (2024-01-01)
- ✅ 初始版本发布
- ✅ 基础工具条功能
- ✅ Cost-Project专用插件
- ✅ 智能上下文感知
- ✅ 调试模式支持

## 技术支持

如遇问题，请：
1. 查看浏览器控制台错误信息
2. 检查本文档的故障排除部分
3. 提交Issue到项目仓库
4. 联系开发团队

---

**注意**: 该工具条仅用于开发环境，生产环境会自动禁用以确保性能和安全性。 