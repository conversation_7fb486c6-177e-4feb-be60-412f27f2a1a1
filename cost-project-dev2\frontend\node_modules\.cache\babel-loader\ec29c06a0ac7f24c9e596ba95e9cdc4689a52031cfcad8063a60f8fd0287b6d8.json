{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport GoldenFilledSvg from \"@ant-design/icons-svg/es/asn/GoldenFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar GoldenFilled = function GoldenFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: GoldenFilledSvg\n  }));\n};\n\n/**![golden](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwNS45IDgwNi43bC00MC4yLTI0OGMtLjYtMy45LTQtNi43LTcuOS02LjdINTk2LjJjLTMuOSAwLTcuMyAyLjgtNy45IDYuN2wtNDAuMiAyNDhjLS4xLjQtLjEuOS0uMSAxLjMgMCA0LjQgMy42IDggOCA4aDM0MmMuNCAwIC45IDAgMS4zLS4xIDQuMy0uNyA3LjMtNC44IDYuNi05LjJ6bS00NzAuMi0yNDhjLS42LTMuOS00LTYuNy03LjktNi43SDE2Ni4yYy0zLjkgMC03LjMgMi44LTcuOSA2LjdsLTQwLjIgMjQ4Yy0uMS40LS4xLjktLjEgMS4zIDAgNC40IDMuNiA4IDggOGgzNDJjLjQgMCAuOSAwIDEuMy0uMSA0LjQtLjcgNy4zLTQuOCA2LjYtOS4ybC00MC4yLTI0OHpNMzQyIDQ3MmgzNDJjLjQgMCAuOSAwIDEuMy0uMSA0LjQtLjcgNy4zLTQuOCA2LjYtOS4ybC00MC4yLTI0OGMtLjYtMy45LTQtNi43LTcuOS02LjdIMzgyLjJjLTMuOSAwLTcuMyAyLjgtNy45IDYuN2wtNDAuMiAyNDhjLS4xLjQtLjEuOS0uMSAxLjMgMCA0LjQgMy42IDggOCA4eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(GoldenFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'GoldenFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "GoldenFilledSvg", "AntdIcon", "GoldenFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/GoldenFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport GoldenFilledSvg from \"@ant-design/icons-svg/es/asn/GoldenFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar GoldenFilled = function GoldenFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: GoldenFilledSvg\n  }));\n};\n\n/**![golden](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwNS45IDgwNi43bC00MC4yLTI0OGMtLjYtMy45LTQtNi43LTcuOS02LjdINTk2LjJjLTMuOSAwLTcuMyAyLjgtNy45IDYuN2wtNDAuMiAyNDhjLS4xLjQtLjEuOS0uMSAxLjMgMCA0LjQgMy42IDggOCA4aDM0MmMuNCAwIC45IDAgMS4zLS4xIDQuMy0uNyA3LjMtNC44IDYuNi05LjJ6bS00NzAuMi0yNDhjLS42LTMuOS00LTYuNy03LjktNi43SDE2Ni4yYy0zLjkgMC03LjMgMi44LTcuOSA2LjdsLTQwLjIgMjQ4Yy0uMS40LS4xLjktLjEgMS4zIDAgNC40IDMuNiA4IDggOGgzNDJjLjQgMCAuOSAwIDEuMy0uMSA0LjQtLjcgNy4zLTQuOCA2LjYtOS4ybC00MC4yLTI0OHpNMzQyIDQ3MmgzNDJjLjQgMCAuOSAwIDEuMy0uMSA0LjQtLjcgNy4zLTQuOCA2LjYtOS4ybC00MC4yLTI0OGMtLjYtMy45LTQtNi43LTcuOS02LjdIMzgyLjJjLTMuOSAwLTcuMyAyLjgtNy45IDYuN2wtNDAuMiAyNDhjLS4xLjQtLjEuOS0uMSAxLjMgMCA0LjQgMy42IDggOCA4eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(GoldenFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'GoldenFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,YAAY,CAAC;AACzD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,cAAc;AACtC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}