{"ast": null, "code": "import { isVector2 } from './is';\nconst VECTOR_ZERO = [0, 0, 0];\n/**\n * <zh/> 两个向量求和\n *\n * <en/> Adds two vectors\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns <zh/> 两个向量的和 | <en/> The sum of the two vectors\n */\nexport function add(a, b) {\n  return a.map((v, i) => v + b[i]);\n}\n/**\n * <zh/> 两个向量求差\n *\n * <en/> Subtracts two vectors\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns <zh/> 两个向量的差 | <en/> The difference of the two vectors\n */\nexport function subtract(a, b) {\n  return a.map((v, i) => v - b[i]);\n}\n/**\n * <zh/> 两个向量求积或者向量和标量求积\n *\n * <en/> Multiplies two vectors or a vector and a scalar\n * @param a - <zh/> 向量 | <en/> The vector\n * @param b - <zh/> 向量或者标量 | <en/> The vector or scalar\n * @returns <zh/> 两个向量的积或者向量和标量的积 | <en/> The product of the two vectors or the product of the vector and scalar\n */\nexport function multiply(a, b) {\n  if (typeof b === 'number') return a.map(v => v * b);\n  return a.map((v, i) => v * b[i]);\n}\n/**\n * <zh/> 两个向量求商或者向量和标量求商\n *\n * <en/> Divides two vectors or a vector and a scalar\n * @param a - <zh/> 向量 | <en/> The vector\n * @param b - <zh/> 向量或者标量 | <en/> The vector or scalar\n * @returns <zh/> 两个向量的商或者向量和标量的商 | <en/> The quotient of the two vectors or the quotient of the vector and scalar\n */\nexport function divide(a, b) {\n  if (typeof b === 'number') return a.map(v => v / b);\n  return a.map((v, i) => v / b[i]);\n}\n/**\n * <zh/> 两个向量求点积\n *\n * <en/> Calculates the dot product of two vectors\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns <zh/> 两个向量的点积 | <en/> The dot product of the two vectors\n */\nexport function dot(a, b) {\n  return a.reduce((sum, v, i) => sum + v * b[i], 0);\n}\n/**\n * <zh/> 两个二维向量求叉积\n *\n * <en/> Calculates the cross product of two vectors in three-dimensional Euclidean space\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns <zh/> 两个向量的叉积 | <en/> The cross product of the two vectors\n */\nexport function cross(a, b) {\n  const a2 = toVector3(a);\n  const b2 = toVector3(b);\n  return [a2[1] * b2[2] - a2[2] * b2[1], a2[2] * b2[0] - a2[0] * b2[2], a2[0] * b2[1] - a2[1] * b2[0]];\n}\n/**\n * <zh/> 向量缩放\n *\n * <en/> Scales a vector by a scalar number\n * @param a  - <zh/> 向量 | <en/> The vector to scale\n * @param s - <zh/> 缩放系数 | <en/> Scale factor\n * @returns <zh/> 缩放后的向量 | <en/> The scaled vector\n */\nexport function scale(a, s) {\n  return a.map(v => v * s);\n}\n/**\n * <zh/> 计算两个向量间的欧几里得距离\n *\n * <en/> Calculates the Euclidean distance between two vectors\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns <zh/> 两个向量间的距离 | <en/> The distance between the two vectors\n */\nexport function distance(a, b) {\n  return Math.sqrt(a.reduce((sum, v, i) => sum + Math.pow(v - b[i] || 0, 2), 0));\n}\n/**\n * <zh/> 计算两个向量间的曼哈顿距离\n *\n * <en/> Calculates the Manhattan distance between two vectors\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns <zh/> 两个向量间的距离 | <en/> The distance between the two vectors\n */\nexport function manhattanDistance(a, b) {\n  return a.reduce((sum, v, i) => sum + Math.abs(v - b[i]), 0);\n}\n/**\n * <zh/> 标准化向量（使长度为 1）\n *\n * <en/> Normalizes a vector (making its length 1)\n * @param a - <zh/> 要标准化的向量 | <en/> The vector to normalize\n * @returns <zh/> 标准化后的向量 | <en/> The normalized vector\n */\nexport function normalize(a) {\n  const length = a.reduce((sum, v) => sum + Math.pow(v, 2), 0);\n  return a.map(v => v / Math.sqrt(length));\n}\n/**\n * <zh/> 计算两个向量间的夹角，输出为锐角余弦值\n *\n * <en/> Get the angle between two vectors\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @param clockwise - <zh/> 是否顺时针 | <en/> Whether to calculate the angle in a clockwise direction\n * @returns  <zh/> 弧度值 | <en/> The angle in radians\n */\nexport function angle(a, b, clockwise = false) {\n  const determinant = a[0] * b[1] - a[1] * b[0];\n  let angle = Math.acos(multiply(a, b).reduce((sum, v) => sum + v, 0) / (distance(a, VECTOR_ZERO) * distance(b, VECTOR_ZERO)));\n  // If clockwise is true and determinant is negative, adjust the angle\n  if (clockwise && determinant < 0) {\n    angle = 2 * Math.PI - angle;\n  }\n  return angle;\n}\n/**\n * <zh/> 判断两个向量是否完全相等（使用 === 比较）\n *\n * <en/> Returns whether or not the vectors exactly have the same elements in the same position (when compared with ===)\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns  - <zh/> 是否相等 | <en/> Whether or not the vectors are equal\n */\nexport function exactEquals(a, b) {\n  return a.every((v, i) => v === b[i]);\n}\n/**\n * <zh/> 计算向量的垂直向量\n *\n * <en/> Calculates the perpendicular vector to a given vector\n * @param a - <zh/> 原始向量 | <en/> The original vector\n * @param clockwise - <zh/> 是否顺时针 | <en/> Whether to calculate the perpendicular vector in a clockwise direction\n * @returns <zh/> 原始向量的垂直向量 | <en/> The perpendicular vector to the original vector\n */\nexport function perpendicular(a, clockwise = true) {\n  return clockwise ? [-a[1], a[0]] : [a[1], -a[0]];\n}\n/**\n * <zh/> 计算向量的模\n *\n * <en/> Calculates the modulus of a vector\n * @param a - <zh/> 原始向量 | <en/> The original vector\n * @param b - <zh/> 模 | <en/> The modulus\n * @returns - <zh/> 向量的模 | <en/> The modulus of the vector\n */\nexport function mod(a, b) {\n  return a.map(v => v % b);\n}\n/**\n * <zh/> 向量强制转换为二维向量\n *\n * <en/> Force vector to be two-dimensional\n * @param a - <zh/> 原始向量 | <en/> The original vector\n * @returns <zh/> 二维向量 | <en/> Two-dimensional vector\n */\nexport function toVector2(a) {\n  return [a[0], a[1]];\n}\n/**\n * <zh/> 向量强制转换为三维向量\n *\n * <en/> Force vector to be three-dimensional\n * @param a - <zh/> 原始向量 | <en/> The original vector\n * @returns  - <zh/> 三维向量 | <en/> Three-dimensional vector\n */\nexport function toVector3(a) {\n  return isVector2(a) ? [a[0], a[1], 0] : a;\n}\n/**\n * <zh/> 计算向量与 x 轴正方向的夹角（弧度制）\n *\n * <en/> The angle between the vector and the positive direction of the x-axis (radians)\n * @param a - <zh/> 向量 | <en/> The vector\n * @returns <zh/> 弧度值 | <en/> The angle in radians\n */\nexport function rad(a) {\n  const [x, y] = a;\n  if (!x && !y) return 0;\n  return Math.atan2(y, x);\n}", "map": {"version": 3, "names": ["isVector2", "VECTOR_ZERO", "add", "a", "b", "map", "v", "i", "subtract", "multiply", "divide", "dot", "reduce", "sum", "cross", "a2", "toVector3", "b2", "scale", "s", "distance", "Math", "sqrt", "pow", "manhattanDistance", "abs", "normalize", "length", "angle", "clockwise", "determinant", "acos", "PI", "exactEquals", "every", "perpendicular", "mod", "toVector2", "rad", "x", "y", "atan2"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\g6\\src\\utils\\vector.ts"], "sourcesContent": ["import type { Vector2, Vector3 } from '../types';\nimport { isVector2 } from './is';\n\nconst VECTOR_ZERO: Vector3 = [0, 0, 0];\n\n/**\n * <zh/> 两个向量求和\n *\n * <en/> Adds two vectors\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns <zh/> 两个向量的和 | <en/> The sum of the two vectors\n */\nexport function add(a: Vector2 | Vector3, b: Vector2 | Vector3): Vector2 | Vector3 {\n  return a.map((v, i) => v + b[i]) as Vector2 | Vector3;\n}\n\n/**\n * <zh/> 两个向量求差\n *\n * <en/> Subtracts two vectors\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns <zh/> 两个向量的差 | <en/> The difference of the two vectors\n */\nexport function subtract(a: Vector2 | Vector3, b: Vector2 | Vector3): Vector2 | Vector3 {\n  return a.map((v, i) => v - b[i]) as Vector2 | Vector3;\n}\n\n/**\n * <zh/> 两个向量求积或者向量和标量求积\n *\n * <en/> Multiplies two vectors or a vector and a scalar\n * @param a - <zh/> 向量 | <en/> The vector\n * @param b - <zh/> 向量或者标量 | <en/> The vector or scalar\n * @returns <zh/> 两个向量的积或者向量和标量的积 | <en/> The product of the two vectors or the product of the vector and scalar\n */\nexport function multiply(a: Vector2 | Vector3, b: number | Vector2 | Vector3): Vector2 | Vector3 {\n  if (typeof b === 'number') return a.map((v) => v * b) as Vector2 | Vector3;\n  return a.map((v, i) => v * b[i]) as Vector2 | Vector3;\n}\n\n/**\n * <zh/> 两个向量求商或者向量和标量求商\n *\n * <en/> Divides two vectors or a vector and a scalar\n * @param a - <zh/> 向量 | <en/> The vector\n * @param b - <zh/> 向量或者标量 | <en/> The vector or scalar\n * @returns <zh/> 两个向量的商或者向量和标量的商 | <en/> The quotient of the two vectors or the quotient of the vector and scalar\n */\nexport function divide(a: Vector2 | Vector3, b: number | Vector2 | Vector3): Vector2 | Vector3 {\n  if (typeof b === 'number') return a.map((v) => v / b) as Vector2 | Vector3;\n  return a.map((v, i) => v / b[i]) as Vector2 | Vector3;\n}\n\n/**\n * <zh/> 两个向量求点积\n *\n * <en/> Calculates the dot product of two vectors\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns <zh/> 两个向量的点积 | <en/> The dot product of the two vectors\n */\nexport function dot(a: Vector2 | Vector3, b: Vector2 | Vector3): number {\n  return (a as number[]).reduce((sum, v, i) => sum + v * b[i], 0);\n}\n\n/**\n * <zh/> 两个二维向量求叉积\n *\n * <en/> Calculates the cross product of two vectors in three-dimensional Euclidean space\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns <zh/> 两个向量的叉积 | <en/> The cross product of the two vectors\n */\nexport function cross(a: Vector2 | Vector3, b: Vector2 | Vector3): Vector3 {\n  const a2 = toVector3(a);\n  const b2 = toVector3(b);\n  return [a2[1] * b2[2] - a2[2] * b2[1], a2[2] * b2[0] - a2[0] * b2[2], a2[0] * b2[1] - a2[1] * b2[0]];\n}\n\n/**\n * <zh/> 向量缩放\n *\n * <en/> Scales a vector by a scalar number\n * @param a  - <zh/> 向量 | <en/> The vector to scale\n * @param s - <zh/> 缩放系数 | <en/> Scale factor\n * @returns <zh/> 缩放后的向量 | <en/> The scaled vector\n */\nexport function scale(a: Vector2 | Vector3, s: number): Vector2 | Vector3 {\n  return a.map((v) => v * s) as Vector2 | Vector3;\n}\n\n/**\n * <zh/> 计算两个向量间的欧几里得距离\n *\n * <en/> Calculates the Euclidean distance between two vectors\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns <zh/> 两个向量间的距离 | <en/> The distance between the two vectors\n */\nexport function distance(a: Vector2 | Vector3, b: Vector2 | Vector3): number {\n  return Math.sqrt((a as number[]).reduce((sum, v, i) => sum + (v - b[i] || 0) ** 2, 0));\n}\n\n/**\n * <zh/> 计算两个向量间的曼哈顿距离\n *\n * <en/> Calculates the Manhattan distance between two vectors\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns <zh/> 两个向量间的距离 | <en/> The distance between the two vectors\n */\nexport function manhattanDistance(a: Vector2 | Vector3, b: Vector2 | Vector3): number {\n  return (a as number[]).reduce((sum, v, i) => sum + Math.abs(v - b[i]), 0);\n}\n\n/**\n * <zh/> 标准化向量（使长度为 1）\n *\n * <en/> Normalizes a vector (making its length 1)\n * @param a - <zh/> 要标准化的向量 | <en/> The vector to normalize\n * @returns <zh/> 标准化后的向量 | <en/> The normalized vector\n */\nexport function normalize(a: Vector2 | Vector3): Vector2 | Vector3 {\n  const length = (a as number[]).reduce((sum, v) => sum + v ** 2, 0);\n  return a.map((v) => v / Math.sqrt(length)) as Vector2 | Vector3;\n}\n\n/**\n * <zh/> 计算两个向量间的夹角，输出为锐角余弦值\n *\n * <en/> Get the angle between two vectors\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @param clockwise - <zh/> 是否顺时针 | <en/> Whether to calculate the angle in a clockwise direction\n * @returns  <zh/> 弧度值 | <en/> The angle in radians\n */\nexport function angle(a: Vector2 | Vector3, b: Vector2 | Vector3, clockwise = false): number {\n  const determinant = a[0] * b[1] - a[1] * b[0];\n  let angle = Math.acos(\n    (multiply(a, b) as number[]).reduce((sum: number, v: number) => sum + v, 0) /\n      (distance(a, VECTOR_ZERO) * distance(b, VECTOR_ZERO)),\n  );\n  // If clockwise is true and determinant is negative, adjust the angle\n  if (clockwise && determinant < 0) {\n    angle = 2 * Math.PI - angle;\n  }\n  return angle;\n}\n\n/**\n * <zh/> 判断两个向量是否完全相等（使用 === 比较）\n *\n * <en/> Returns whether or not the vectors exactly have the same elements in the same position (when compared with ===)\n * @param a - <zh/> 第一个向量 | <en/> The first vector\n * @param b - <zh/> 第二个向量 | <en/> The second vector\n * @returns  - <zh/> 是否相等 | <en/> Whether or not the vectors are equal\n */\nexport function exactEquals(a: Vector2 | Vector3, b: Vector2 | Vector3): boolean {\n  return (a as number[]).every((v, i) => v === b[i]);\n}\n\n/**\n * <zh/> 计算向量的垂直向量\n *\n * <en/> Calculates the perpendicular vector to a given vector\n * @param a - <zh/> 原始向量 | <en/> The original vector\n * @param clockwise - <zh/> 是否顺时针 | <en/> Whether to calculate the perpendicular vector in a clockwise direction\n * @returns <zh/> 原始向量的垂直向量 | <en/> The perpendicular vector to the original vector\n */\nexport function perpendicular(a: Vector2, clockwise = true): Vector2 {\n  return clockwise ? [-a[1], a[0]] : [a[1], -a[0]];\n}\n\n/**\n * <zh/> 计算向量的模\n *\n * <en/> Calculates the modulus of a vector\n * @param a - <zh/> 原始向量 | <en/> The original vector\n * @param b - <zh/> 模 | <en/> The modulus\n * @returns - <zh/> 向量的模 | <en/> The modulus of the vector\n */\nexport function mod(a: Vector2 | Vector3, b: number): Vector2 | Vector3 {\n  return a.map((v) => v % b) as Vector2 | Vector3;\n}\n\n/**\n * <zh/> 向量强制转换为二维向量\n *\n * <en/> Force vector to be two-dimensional\n * @param a - <zh/> 原始向量 | <en/> The original vector\n * @returns <zh/> 二维向量 | <en/> Two-dimensional vector\n */\nexport function toVector2(a: Vector2 | Vector3): Vector2 {\n  return [a[0], a[1]];\n}\n\n/**\n * <zh/> 向量强制转换为三维向量\n *\n * <en/> Force vector to be three-dimensional\n * @param a - <zh/> 原始向量 | <en/> The original vector\n * @returns  - <zh/> 三维向量 | <en/> Three-dimensional vector\n */\nexport function toVector3(a: Vector2 | Vector3): Vector3 {\n  return isVector2(a) ? [a[0], a[1], 0] : a;\n}\n\n/**\n * <zh/> 计算向量与 x 轴正方向的夹角（弧度制）\n *\n * <en/> The angle between the vector and the positive direction of the x-axis (radians)\n * @param a - <zh/> 向量 | <en/> The vector\n * @returns <zh/> 弧度值 | <en/> The angle in radians\n */\nexport function rad(a: Vector2 | Vector3): number {\n  const [x, y] = a;\n  if (!x && !y) return 0;\n  return Math.atan2(y, x);\n}\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,MAAM;AAEhC,MAAMC,WAAW,GAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAEtC;;;;;;;;AAQA,OAAM,SAAUC,GAAGA,CAACC,CAAoB,EAAEC,CAAoB;EAC5D,OAAOD,CAAC,CAACE,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGF,CAAC,CAACG,CAAC,CAAC,CAAsB;AACvD;AAEA;;;;;;;;AAQA,OAAM,SAAUC,QAAQA,CAACL,CAAoB,EAAEC,CAAoB;EACjE,OAAOD,CAAC,CAACE,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGF,CAAC,CAACG,CAAC,CAAC,CAAsB;AACvD;AAEA;;;;;;;;AAQA,OAAM,SAAUE,QAAQA,CAACN,CAAoB,EAAEC,CAA6B;EAC1E,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOD,CAAC,CAACE,GAAG,CAAEC,CAAC,IAAKA,CAAC,GAAGF,CAAC,CAAsB;EAC1E,OAAOD,CAAC,CAACE,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGF,CAAC,CAACG,CAAC,CAAC,CAAsB;AACvD;AAEA;;;;;;;;AAQA,OAAM,SAAUG,MAAMA,CAACP,CAAoB,EAAEC,CAA6B;EACxE,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOD,CAAC,CAACE,GAAG,CAAEC,CAAC,IAAKA,CAAC,GAAGF,CAAC,CAAsB;EAC1E,OAAOD,CAAC,CAACE,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGF,CAAC,CAACG,CAAC,CAAC,CAAsB;AACvD;AAEA;;;;;;;;AAQA,OAAM,SAAUI,GAAGA,CAACR,CAAoB,EAAEC,CAAoB;EAC5D,OAAQD,CAAc,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEP,CAAC,EAAEC,CAAC,KAAKM,GAAG,GAAGP,CAAC,GAAGF,CAAC,CAACG,CAAC,CAAC,EAAE,CAAC,CAAC;AACjE;AAEA;;;;;;;;AAQA,OAAM,SAAUO,KAAKA,CAACX,CAAoB,EAAEC,CAAoB;EAC9D,MAAMW,EAAE,GAAGC,SAAS,CAACb,CAAC,CAAC;EACvB,MAAMc,EAAE,GAAGD,SAAS,CAACZ,CAAC,CAAC;EACvB,OAAO,CAACW,EAAE,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,EAAEF,EAAE,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,EAAEF,EAAE,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,CAAC;AACtG;AAEA;;;;;;;;AAQA,OAAM,SAAUC,KAAKA,CAACf,CAAoB,EAAEgB,CAAS;EACnD,OAAOhB,CAAC,CAACE,GAAG,CAAEC,CAAC,IAAKA,CAAC,GAAGa,CAAC,CAAsB;AACjD;AAEA;;;;;;;;AAQA,OAAM,SAAUC,QAAQA,CAACjB,CAAoB,EAAEC,CAAoB;EACjE,OAAOiB,IAAI,CAACC,IAAI,CAAEnB,CAAc,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEP,CAAC,EAAEC,CAAC,KAAKM,GAAG,GAAGQ,IAAA,CAAAE,GAAA,CAACjB,CAAC,GAAGF,CAAC,CAACG,CAAC,CAAC,IAAI,CAAC,EAAK,CAAC,GAAE,CAAC,CAAC,CAAC;AACxF;AAEA;;;;;;;;AAQA,OAAM,SAAUiB,iBAAiBA,CAACrB,CAAoB,EAAEC,CAAoB;EAC1E,OAAQD,CAAc,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEP,CAAC,EAAEC,CAAC,KAAKM,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACnB,CAAC,GAAGF,CAAC,CAACG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC3E;AAEA;;;;;;;AAOA,OAAM,SAAUmB,SAASA,CAACvB,CAAoB;EAC5C,MAAMwB,MAAM,GAAIxB,CAAc,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEP,CAAC,KAAKO,GAAG,GAAGQ,IAAA,CAAAE,GAAA,CAAAjB,CAAC,EAAI,CAAC,GAAE,CAAC,CAAC;EAClE,OAAOH,CAAC,CAACE,GAAG,CAAEC,CAAC,IAAKA,CAAC,GAAGe,IAAI,CAACC,IAAI,CAACK,MAAM,CAAC,CAAsB;AACjE;AAEA;;;;;;;;;AASA,OAAM,SAAUC,KAAKA,CAACzB,CAAoB,EAAEC,CAAoB,EAAEyB,SAAS,GAAG,KAAK;EACjF,MAAMC,WAAW,GAAG3B,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAIwB,KAAK,GAAGP,IAAI,CAACU,IAAI,CAClBtB,QAAQ,CAACN,CAAC,EAAEC,CAAC,CAAc,CAACQ,MAAM,CAAC,CAACC,GAAW,EAAEP,CAAS,KAAKO,GAAG,GAAGP,CAAC,EAAE,CAAC,CAAC,IACxEc,QAAQ,CAACjB,CAAC,EAAEF,WAAW,CAAC,GAAGmB,QAAQ,CAAChB,CAAC,EAAEH,WAAW,CAAC,CAAC,CACxD;EACD;EACA,IAAI4B,SAAS,IAAIC,WAAW,GAAG,CAAC,EAAE;IAChCF,KAAK,GAAG,CAAC,GAAGP,IAAI,CAACW,EAAE,GAAGJ,KAAK;EAC7B;EACA,OAAOA,KAAK;AACd;AAEA;;;;;;;;AAQA,OAAM,SAAUK,WAAWA,CAAC9B,CAAoB,EAAEC,CAAoB;EACpE,OAAQD,CAAc,CAAC+B,KAAK,CAAC,CAAC5B,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKF,CAAC,CAACG,CAAC,CAAC,CAAC;AACpD;AAEA;;;;;;;;AAQA,OAAM,SAAU4B,aAAaA,CAAChC,CAAU,EAAE0B,SAAS,GAAG,IAAI;EACxD,OAAOA,SAAS,GAAG,CAAC,CAAC1B,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD;AAEA;;;;;;;;AAQA,OAAM,SAAUiC,GAAGA,CAACjC,CAAoB,EAAEC,CAAS;EACjD,OAAOD,CAAC,CAACE,GAAG,CAAEC,CAAC,IAAKA,CAAC,GAAGF,CAAC,CAAsB;AACjD;AAEA;;;;;;;AAOA,OAAM,SAAUiC,SAASA,CAAClC,CAAoB;EAC5C,OAAO,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB;AAEA;;;;;;;AAOA,OAAM,SAAUa,SAASA,CAACb,CAAoB;EAC5C,OAAOH,SAAS,CAACG,CAAC,CAAC,GAAG,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC;AAC3C;AAEA;;;;;;;AAOA,OAAM,SAAUmC,GAAGA,CAACnC,CAAoB;EACtC,MAAM,CAACoC,CAAC,EAAEC,CAAC,CAAC,GAAGrC,CAAC;EAChB,IAAI,CAACoC,CAAC,IAAI,CAACC,CAAC,EAAE,OAAO,CAAC;EACtB,OAAOnB,IAAI,CAACoB,KAAK,CAACD,CAAC,EAAED,CAAC,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}