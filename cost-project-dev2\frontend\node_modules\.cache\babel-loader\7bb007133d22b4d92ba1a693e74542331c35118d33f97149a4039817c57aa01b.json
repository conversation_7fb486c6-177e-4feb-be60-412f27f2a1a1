{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dev\\\\cost-project-dev2\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport 'antd/dist/reset.css';\nimport MainLayout from './components/layout/MainLayout';\nimport HomePage from './pages/HomePage';\nimport ProjectList from './pages/project/ProjectList';\nimport ProjectDetail from './pages/project/ProjectDetail';\nimport CostAnalysis from './pages/cost/CostAnalysis';\nimport BillManagement from './pages/cost/BillManagement';\nimport QuotaManagement from './pages/cost/QuotaManagement';\nimport ResourceManagement from './pages/cost/ResourceManagement';\nimport CalendarPage from './pages/calendar/CalendarPage';\nimport GanttPage from './pages/gantt/GanttPage';\nimport StagewiseToolbar from './components/StagewiseToolbar';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport { setupStagewise } from './utils/stagewise';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  useEffect(() => {\n    // 初始化Stagewise工具条\n    setupStagewise();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    theme: {\n      token: {\n        colorPrimary: '#1890ff'\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n        children: [/*#__PURE__*/_jsxDEV(MainLayout, {\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/projects\",\n              element: /*#__PURE__*/_jsxDEV(ProjectList, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/projects/:id\",\n              element: /*#__PURE__*/_jsxDEV(ProjectDetail, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/cost/analysis\",\n              element: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n                children: /*#__PURE__*/_jsxDEV(CostAnalysis, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 68\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/cost/bills\",\n              element: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n                children: /*#__PURE__*/_jsxDEV(BillManagement, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 44,\n                  columnNumber: 65\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/cost/quotas\",\n              element: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n                children: /*#__PURE__*/_jsxDEV(QuotaManagement, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 66\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/cost/resources\",\n              element: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n                children: /*#__PURE__*/_jsxDEV(ResourceManagement, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 46,\n                  columnNumber: 69\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 54\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/calendar\",\n              element: /*#__PURE__*/_jsxDEV(CalendarPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/gantt\",\n              element: /*#__PURE__*/_jsxDEV(GanttPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StagewiseToolbar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "MainLayout", "HomePage", "ProjectList", "ProjectDetail", "CostAnalysis", "BillManagement", "QuotaManagement", "ResourceManagement", "CalendarPage", "GanttPage", "StagewiseToolbar", "Error<PERSON>ou<PERSON><PERSON>", "setupStagewise", "jsxDEV", "_jsxDEV", "App", "_s", "locale", "theme", "token", "colorPrimary", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/src/App.js"], "sourcesContent": ["import React, { useEffect } from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\r\nimport { ConfigProvider } from 'antd';\r\nimport zhCN from 'antd/locale/zh_CN';\r\nimport 'antd/dist/reset.css';\r\n\r\nimport MainLayout from './components/layout/MainLayout';\r\nimport HomePage from './pages/HomePage';\r\nimport ProjectList from './pages/project/ProjectList';\r\nimport ProjectDetail from './pages/project/ProjectDetail';\r\nimport CostAnalysis from './pages/cost/CostAnalysis';\r\nimport BillManagement from './pages/cost/BillManagement';\r\nimport QuotaManagement from './pages/cost/QuotaManagement';\r\nimport ResourceManagement from './pages/cost/ResourceManagement';\r\nimport CalendarPage from './pages/calendar/CalendarPage';\r\nimport GanttPage from './pages/gantt/GanttPage';\r\nimport StagewiseToolbar from './components/StagewiseToolbar';\r\nimport ErrorBoundary from './components/ErrorBoundary';\r\nimport { setupStagewise } from './utils/stagewise';\r\n\r\nfunction App() {\r\n  useEffect(() => {\r\n    // 初始化Stagewise工具条\r\n    setupStagewise();\r\n  }, []);\r\n\r\n  return (\r\n    <ConfigProvider \r\n      locale={zhCN}\r\n      theme={{\r\n        token: {\r\n          colorPrimary: '#1890ff',\r\n        },\r\n      }}\r\n    >\r\n      <Router>\r\n        <ErrorBoundary>\r\n          <MainLayout>\r\n            <Routes>\r\n              <Route path=\"/\" element={<HomePage />} />\r\n              <Route path=\"/projects\" element={<ProjectList />} />\r\n              <Route path=\"/projects/:id\" element={<ProjectDetail />} />\r\n              <Route path=\"/cost/analysis\" element={<ErrorBoundary><CostAnalysis /></ErrorBoundary>} />\r\n              <Route path=\"/cost/bills\" element={<ErrorBoundary><BillManagement /></ErrorBoundary>} />\r\n              <Route path=\"/cost/quotas\" element={<ErrorBoundary><QuotaManagement /></ErrorBoundary>} />\r\n              <Route path=\"/cost/resources\" element={<ErrorBoundary><ResourceManagement /></ErrorBoundary>} />\r\n              <Route path=\"/calendar\" element={<CalendarPage />} />\r\n              <Route path=\"/gantt\" element={<GanttPage />} />\r\n              <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\r\n            </Routes>\r\n          </MainLayout>\r\n          <StagewiseToolbar />\r\n        </ErrorBoundary>\r\n      </Router>\r\n    </ConfigProvider>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAO,qBAAqB;AAE5B,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,aAAa,MAAM,4BAA4B;AACtD,SAASC,cAAc,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACbxB,SAAS,CAAC,MAAM;IACd;IACAoB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEE,OAAA,CAAChB,cAAc;IACbmB,MAAM,EAAElB,IAAK;IACbmB,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,YAAY,EAAE;MAChB;IACF,CAAE;IAAAC,QAAA,eAEFP,OAAA,CAACpB,MAAM;MAAA2B,QAAA,eACLP,OAAA,CAACH,aAAa;QAAAU,QAAA,gBACZP,OAAA,CAACd,UAAU;UAAAqB,QAAA,eACTP,OAAA,CAACnB,MAAM;YAAA0B,QAAA,gBACLP,OAAA,CAAClB,KAAK;cAAC0B,IAAI,EAAC,GAAG;cAACC,OAAO,eAAET,OAAA,CAACb,QAAQ;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCb,OAAA,CAAClB,KAAK;cAAC0B,IAAI,EAAC,WAAW;cAACC,OAAO,eAAET,OAAA,CAACZ,WAAW;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDb,OAAA,CAAClB,KAAK;cAAC0B,IAAI,EAAC,eAAe;cAACC,OAAO,eAAET,OAAA,CAACX,aAAa;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1Db,OAAA,CAAClB,KAAK;cAAC0B,IAAI,EAAC,gBAAgB;cAACC,OAAO,eAAET,OAAA,CAACH,aAAa;gBAAAU,QAAA,eAACP,OAAA,CAACV,YAAY;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzFb,OAAA,CAAClB,KAAK;cAAC0B,IAAI,EAAC,aAAa;cAACC,OAAO,eAAET,OAAA,CAACH,aAAa;gBAAAU,QAAA,eAACP,OAAA,CAACT,cAAc;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxFb,OAAA,CAAClB,KAAK;cAAC0B,IAAI,EAAC,cAAc;cAACC,OAAO,eAAET,OAAA,CAACH,aAAa;gBAAAU,QAAA,eAACP,OAAA,CAACR,eAAe;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1Fb,OAAA,CAAClB,KAAK;cAAC0B,IAAI,EAAC,iBAAiB;cAACC,OAAO,eAAET,OAAA,CAACH,aAAa;gBAAAU,QAAA,eAACP,OAAA,CAACP,kBAAkB;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChGb,OAAA,CAAClB,KAAK;cAAC0B,IAAI,EAAC,WAAW;cAACC,OAAO,eAAET,OAAA,CAACN,YAAY;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDb,OAAA,CAAClB,KAAK;cAAC0B,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAET,OAAA,CAACL,SAAS;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/Cb,OAAA,CAAClB,KAAK;cAAC0B,IAAI,EAAC,GAAG;cAACC,OAAO,eAAET,OAAA,CAACjB,QAAQ;gBAAC+B,EAAE,EAAC,GAAG;gBAACC,OAAO;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACbb,OAAA,CAACJ,gBAAgB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB;AAACX,EAAA,CApCQD,GAAG;AAAAe,EAAA,GAAHf,GAAG;AAsCZ,eAAeA,GAAG;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}