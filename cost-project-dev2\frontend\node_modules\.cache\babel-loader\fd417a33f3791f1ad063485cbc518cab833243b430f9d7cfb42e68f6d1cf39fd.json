{"ast": null, "code": "// represents a body(a point mass) and its position\nexport default class Body {\n  constructor(params) {\n    /**\n     * the id of this body, the same with the node id\n     * @type  {number}\n     */\n    this.id = params.id || 0;\n    /**\n     * the position of this body\n     * @type  {number}\n     */\n    this.rx = params.rx;\n    /**\n     * the position of this body\n     * @type  {number}\n     */\n    this.ry = params.ry;\n    /**\n     * the force acting on this body\n     * @type  {number}\n     */\n    this.fx = 0;\n    /**\n     * the force acting on this body\n     * @type  {number}\n     */\n    this.fy = 0;\n    /**\n     * the mass of this body, =1 for a node\n     * @type  {number}\n     */\n    this.mass = params.mass;\n    /**\n     * the degree of the node represented by this body\n     * @type  {number}\n     */\n    this.degree = params.degree;\n    /**\n     * the parameter for repulsive force, = kr\n     * @type  {number}\n     */\n    this.g = params.g || 0;\n  }\n  // returns the euclidean distance\n  distanceTo(bo) {\n    const dx = this.rx - bo.rx;\n    const dy = this.ry - bo.ry;\n    return Math.hypot(dx, dy);\n  }\n  setPos(x, y) {\n    this.rx = x;\n    this.ry = y;\n  }\n  // resets the forces\n  resetForce() {\n    this.fx = 0;\n    this.fy = 0;\n  }\n  addForce(b) {\n    const dx = b.rx - this.rx;\n    const dy = b.ry - this.ry;\n    let dist = Math.hypot(dx, dy);\n    dist = dist < 0.0001 ? 0.0001 : dist;\n    // the repulsive defined by force atlas 2\n    const F = this.g * (this.degree + 1) * (b.degree + 1) / dist;\n    this.fx += F * dx / dist;\n    this.fy += F * dy / dist;\n  }\n  // if quad contains this body\n  in(quad) {\n    return quad.contains(this.rx, this.ry);\n  }\n  // returns a new body\n  add(bo) {\n    const nenwMass = this.mass + bo.mass;\n    const x = (this.rx * this.mass + bo.rx * bo.mass) / nenwMass;\n    const y = (this.ry * this.mass + bo.ry * bo.mass) / nenwMass;\n    const dg = this.degree + bo.degree;\n    const params = {\n      rx: x,\n      ry: y,\n      mass: nenwMass,\n      degree: dg\n    };\n    return new Body(params);\n  }\n}", "map": {"version": 3, "names": ["Body", "constructor", "params", "id", "rx", "ry", "fx", "fy", "mass", "degree", "g", "distanceTo", "bo", "dx", "dy", "Math", "hypot", "setPos", "x", "y", "resetForce", "addForce", "b", "dist", "F", "in", "quad", "contains", "add", "nenwMass", "dg"], "sources": ["../../src/force-atlas2/body.ts"], "sourcesContent": [null], "mappings": "AAkBA;AACA,eAAc,MAAOA,IAAI;EAUvBC,YAAYC,MAAiB;IAC3B;;;;IAIA,IAAI,CAACC,EAAE,GAAGD,MAAM,CAACC,EAAE,IAAI,CAAC;IACxB;;;;IAIA,IAAI,CAACC,EAAE,GAAGF,MAAM,CAACE,EAAE;IACnB;;;;IAIA,IAAI,CAACC,EAAE,GAAGH,MAAM,CAACG,EAAE;IACnB;;;;IAIA,IAAI,CAACC,EAAE,GAAG,CAAC;IACX;;;;IAIA,IAAI,CAACC,EAAE,GAAG,CAAC;IACX;;;;IAIA,IAAI,CAACC,IAAI,GAAGN,MAAM,CAACM,IAAI;IACvB;;;;IAIA,IAAI,CAACC,MAAM,GAAGP,MAAM,CAACO,MAAM;IAC3B;;;;IAIA,IAAI,CAACC,CAAC,GAAGR,MAAM,CAACQ,CAAC,IAAI,CAAC;EACxB;EACA;EACAC,UAAUA,CAACC,EAAQ;IACjB,MAAMC,EAAE,GAAG,IAAI,CAACT,EAAE,GAAGQ,EAAE,CAACR,EAAE;IAC1B,MAAMU,EAAE,GAAG,IAAI,CAACT,EAAE,GAAGO,EAAE,CAACP,EAAE;IAC1B,OAAOU,IAAI,CAACC,KAAK,CAACH,EAAE,EAAEC,EAAE,CAAC;EAC3B;EACAG,MAAMA,CAACC,CAAS,EAAEC,CAAS;IACzB,IAAI,CAACf,EAAE,GAAGc,CAAC;IACX,IAAI,CAACb,EAAE,GAAGc,CAAC;EACb;EACA;EACAC,UAAUA,CAAA;IACR,IAAI,CAACd,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;EACb;EACAc,QAAQA,CAACC,CAAO;IACd,MAAMT,EAAE,GAAGS,CAAC,CAAClB,EAAE,GAAG,IAAI,CAACA,EAAE;IACzB,MAAMU,EAAE,GAAGQ,CAAC,CAACjB,EAAE,GAAG,IAAI,CAACA,EAAE;IACzB,IAAIkB,IAAI,GAAGR,IAAI,CAACC,KAAK,CAACH,EAAE,EAAEC,EAAE,CAAC;IAC7BS,IAAI,GAAGA,IAAI,GAAG,MAAM,GAAG,MAAM,GAAGA,IAAI;IACpC;IACA,MAAMC,CAAC,GAAI,IAAI,CAACd,CAAC,IAAI,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC,IAAIa,CAAC,CAACb,MAAM,GAAG,CAAC,CAAC,GAAIc,IAAI;IAC9D,IAAI,CAACjB,EAAE,IAAKkB,CAAC,GAAGX,EAAE,GAAIU,IAAI;IAC1B,IAAI,CAAChB,EAAE,IAAKiB,CAAC,GAAGV,EAAE,GAAIS,IAAI;EAC5B;EACA;EACAE,EAAEA,CAACC,IAAU;IACX,OAAOA,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACvB,EAAE,EAAE,IAAI,CAACC,EAAE,CAAC;EACxC;EACA;EACAuB,GAAGA,CAAChB,EAAQ;IACV,MAAMiB,QAAQ,GAAG,IAAI,CAACrB,IAAI,GAAGI,EAAE,CAACJ,IAAI;IACpC,MAAMU,CAAC,GAAG,CAAC,IAAI,CAACd,EAAE,GAAG,IAAI,CAACI,IAAI,GAAGI,EAAE,CAACR,EAAE,GAAGQ,EAAE,CAACJ,IAAI,IAAIqB,QAAQ;IAC5D,MAAMV,CAAC,GAAG,CAAC,IAAI,CAACd,EAAE,GAAG,IAAI,CAACG,IAAI,GAAGI,EAAE,CAACP,EAAE,GAAGO,EAAE,CAACJ,IAAI,IAAIqB,QAAQ;IAC5D,MAAMC,EAAE,GAAG,IAAI,CAACrB,MAAM,GAAGG,EAAE,CAACH,MAAM;IAClC,MAAMP,MAAM,GAAc;MACxBE,EAAE,EAAEc,CAAC;MACLb,EAAE,EAAEc,CAAC;MACLX,IAAI,EAAEqB,QAAQ;MACdpB,MAAM,EAAEqB;KACT;IACD,OAAO,IAAI9B,IAAI,CAACE,MAAM,CAAC;EACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}