# Cost-Project 启动指南

## 快速启动步骤

### 1. 数据库配置

由于PostgreSQL数据库需要单独安装和配置，我们提供了两种方式：

#### 方式一：使用PostgreSQL（生产环境推荐）
1. 安装PostgreSQL 12+
2. 创建数据库：
```sql
CREATE DATABASE cost_project;
```
3. 执行初始化脚本：
```bash
psql -U postgres -d cost_project -f database/init.sql
```
4. 修改`backend/src/main/resources/application.properties`中的数据库连接信息

#### 方式二：使用H2内存数据库（开发环境）
已经配置好了开发环境配置文件`application-dev.properties`，使用H2内存数据库，无需额外配置。

### 2. 启动后端服务

```bash
cd backend
# 使用开发配置启动（H2数据库）
mvn spring-boot:run -s settings.xml -Dspring.profiles.active=dev

# 或使用批处理文件
start-dev.bat
```

后端服务将在 http://localhost:8080 启动

### 3. 启动前端服务

```bash
cd frontend
# 安装依赖（首次运行）
npm install
# 启动开发服务器
npm start
```

前端应用将在 http://localhost:3000 启动

### 4. 访问应用

- **前端应用**: http://localhost:3000
- **Swagger API文档**: http://localhost:8080/api/swagger-ui.html
- **H2数据库控制台**（开发模式）: http://localhost:8080/api/h2-console
  - JDBC URL: `jdbc:h2:mem:testdb`
  - 用户名: `sa`
  - 密码: （留空）

## 常见问题

### 1. 端口被占用
如果8080或3000端口被占用，可以修改配置：
- 后端端口：修改`application.properties`中的`server.port`
- 前端端口：在启动时使用`PORT=3001 npm start`

### 2. Maven编译错误
确保使用提供的`settings.xml`文件：
```bash
mvn clean compile -s settings.xml
```

### 3. 前端依赖安装失败
使用淘宝镜像：
```bash
npm config set registry https://registry.npmmirror.com
npm install
```

### 4. 数据库连接失败
- 检查PostgreSQL服务是否启动
- 确认数据库名称、用户名、密码正确
- 或使用开发配置（H2数据库）

## 开发环境推荐配置

1. **Java**: 17+ (LTS版本)
2. **Node.js**: 14+
3. **Maven**: 3.6+
4. **IDE**: IntelliJ IDEA 或 VS Code
5. **数据库工具**: DBeaver 或 pgAdmin（用于PostgreSQL）

## 项目结构说明

```
backend/
├── src/main/java/com/costproject/
│   ├── controller/     # REST API控制器
│   ├── entity/         # JPA实体类
│   ├── repository/     # 数据访问层
│   ├── service/        # 业务逻辑层
│   └── util/          # 工具类
├── src/main/resources/
│   ├── application.properties      # 主配置文件
│   └── application-dev.properties  # 开发环境配置
└── pom.xml

frontend/
├── src/
│   ├── components/    # React组件
│   ├── pages/        # 页面组件
│   ├── services/     # API服务
│   └── utils/        # 工具函数
└── package.json
```

## 下一步

1. 访问Swagger文档了解API接口
2. 使用前端界面进行功能测试
3. 查看`docs/项目分析总结.md`了解详细功能
4. 参考`README.md`了解项目整体架构 