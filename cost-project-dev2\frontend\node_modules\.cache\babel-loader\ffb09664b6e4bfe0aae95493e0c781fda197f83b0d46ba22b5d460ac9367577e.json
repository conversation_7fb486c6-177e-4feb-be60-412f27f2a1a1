{"ast": null, "code": "export default function requestAnimationFrame(fn) {\n  var method = window.requestAnimationFrame ||\n  // @ts-ignore\n  window.webkitRequestAnimationFrame ||\n  // @ts-ignore\n  window.mozRequestAnimationFrame ||\n  // @ts-ignore\n  window.msRequestAnimationFrame || function (f) {\n    return setTimeout(f, 16);\n  };\n  return method(fn);\n}", "map": {"version": 3, "names": ["requestAnimationFrame", "fn", "method", "window", "webkitRequestAnimationFrame", "mozRequestAnimationFrame", "msRequestAnimationFrame", "f", "setTimeout"], "sources": ["lodash/request-animation-frame.ts"], "sourcesContent": [null], "mappings": "AAAA,eAAc,SAAUA,qBAAqBA,CAACC,EAAwB;EACpE,IAAMC,MAAM,GACVC,MAAM,CAACH,qBAAqB;EAC5B;EACAG,MAAM,CAACC,2BAA2B;EAClC;EACAD,MAAM,CAACE,wBAAwB;EAC/B;EACAF,MAAM,CAACG,uBAAuB,IAC9B,UAAUC,CAAC;IACT,OAAOC,UAAU,CAACD,CAAC,EAAE,EAAE,CAAC;EAC1B,CAAC;EAEH,OAAOL,MAAM,CAACD,EAAE,CAAC;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}