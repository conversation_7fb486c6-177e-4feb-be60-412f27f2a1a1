import React, { useState, useEffect } from 'react';
import { Calendar as AntCalendar, Badge, Card, Select, Button, Modal } from 'antd';
import moment from 'moment';
import 'moment/locale/zh-cn';

const Calendar = () => {
  const [selectedDate, setSelectedDate] = useState(moment());
  const [workingDays, setWorkingDays] = useState([]);
  const [holidays, setHolidays] = useState([]);
  const [showSettingsModal, setShowSettingsModal] = useState(false);

  // 获取日历数据
  const fetchCalendarData = async (date) => {
    try {
      // TODO: 调用API获取指定月份的工作日历数据
      // const response = await getCalendarData(date.format('YYYY-MM'));
      // setWorkingDays(response.workingDays);
      // setHolidays(response.holidays);
    } catch (error) {
      console.error('获取日历数据失败:', error);
    }
  };

  // 日期单元格渲染
  const cellRender = (date, info) => {
    // 只处理日期类型的单元格
    if (info.type !== 'date') {
      return info.originNode;
    }

    const isHoliday = holidays.find(h => moment(h.date).isSame(date, 'day'));
    const isWorkingDay = workingDays.includes(date.format('YYYY-MM-DD'));

    const items = [];

    if (isHoliday) {
      items.push({
        type: 'error',
        content: isHoliday.name || '节假日'
      });
    }

    if (isWorkingDay) {
      items.push({
        type: 'success',
        content: '工作日'
      });
    }

    return (
      <ul className="events">
        {items.map((item, index) => (
          <li key={index}>
            <Badge status={item.type} text={item.content} />
          </li>
        ))}
      </ul>
    );
  };

  // 月份变化处理
  const onPanelChange = (date) => {
    setSelectedDate(date);
    fetchCalendarData(date);
  };

  // 日期选择处理
  const onSelect = (date) => {
    setSelectedDate(date);
  };

  // 打开设置模态框
  const openSettings = () => {
    setShowSettingsModal(true);
  };

  // 初始加载
  useEffect(() => {
    fetchCalendarData(selectedDate);
  }, []);

  return (
    <Card
      title="工作日历"
      extra={
        <Button type="primary" onClick={openSettings}>
          日历设置
        </Button>
      }
      style={{ margin: 20 }}
    >
      <AntCalendar
        cellRender={cellRender}
        onPanelChange={onPanelChange}
        onSelect={onSelect}
        mode="month"
      />

      <Modal
        title="工作日历设置"
        visible={showSettingsModal}
        onCancel={() => setShowSettingsModal(false)}
        footer={null}
        width={800}
      >
        {/* TODO: 添加工作日历设置组件 */}
      </Modal>

      <div style={{ marginTop: 16 }}>
        <h4>图例说明：</h4>
        <div style={{ display: 'flex', gap: 16 }}>
          <Badge status="success" text="工作日" />
          <Badge status="error" text="节假日" />
          <Badge status="default" text="非工作日" />
        </div>
      </div>
    </Card>
  );
};

export default Calendar;
