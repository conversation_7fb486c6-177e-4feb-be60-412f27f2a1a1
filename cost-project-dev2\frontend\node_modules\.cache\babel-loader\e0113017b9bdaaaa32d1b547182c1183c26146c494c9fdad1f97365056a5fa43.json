{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport RedditOutlinedSvg from \"@ant-design/icons-svg/es/asn/RedditOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar RedditOutlined = function RedditOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: RedditOutlinedSvg\n  }));\n};\n\n/**![reddit](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4OCA1NjhhNTYgNTYgMCAxMDExMiAwIDU2IDU2IDAgMTAtMTEyIDB6bTMzOC43IDExOS43Yy0yMy4xIDE4LjItNjguOSAzNy44LTExNC43IDM3LjhzLTkxLjYtMTkuNi0xMTQuNy0zNy44Yy0xNC40LTExLjMtMzUuMy04LjktNDYuNyA1LjVzLTguOSAzNS4zIDUuNSA0Ni43QzM5Ni4zIDc3MS42IDQ1Ny41IDc5MiA1MTIgNzkyczExNS43LTIwLjQgMTU1LjktNTIuMWEzMy4yNSAzMy4yNSAwIDEwLTQxLjItNTIuMnpNOTYwIDQ1NmMwLTYxLjktNTAuMS0xMTItMTEyLTExMi00Mi4xIDAtNzguNyAyMy4yLTk3LjkgNTcuNi01Ny42LTMxLjUtMTI3LjctNTEuOC0yMDQuMS01Ni41TDYxMi45IDE5NWwxMjcuOSAzNi45YzExLjUgMzIuNiA0Mi42IDU2LjEgNzkuMiA1Ni4xIDQ2LjQgMCA4NC0zNy42IDg0LTg0cy0zNy42LTg0LTg0LTg0Yy0zMiAwLTU5LjggMTcuOS03NCA0NC4yTDYwMy41IDEyM2EzMy4yIDMzLjIgMCAwMC0zOS42IDE4LjRsLTkwLjggMjAzLjljLTc0LjUgNS4yLTE0Mi45IDI1LjQtMTk5LjIgNTYuMkExMTEuOTQgMTExLjk0IDAgMDAxNzYgMzQ0Yy02MS45IDAtMTEyIDUwLjEtMTEyIDExMiAwIDQ1LjggMjcuNSA4NS4xIDY2LjggMTAyLjUtNy4xIDIxLTEwLjggNDMtMTAuOCA2NS41IDAgMTU0LjYgMTc1LjUgMjgwIDM5MiAyODBzMzkyLTEyNS40IDM5Mi0yODBjMC0yMi42LTMuOC00NC41LTEwLjgtNjUuNUM5MzIuNSA1NDEuMSA5NjAgNTAxLjggOTYwIDQ1NnpNODIwIDE3Mi41YTMxLjUgMzEuNSAwIDExMCA2MyAzMS41IDMxLjUgMCAwMTAtNjN6TTEyMCA0NTZjMC0zMC45IDI1LjEtNTYgNTYtNTZhNTYgNTYgMCAwMTUwLjYgMzIuMWMtMjkuMyAyMi4yLTUzLjUgNDcuOC03MS41IDc1LjlhNTYuMjMgNTYuMjMgMCAwMS0zNS4xLTUyem0zOTIgMzgxLjVjLTE3OS44IDAtMzI1LjUtOTUuNi0zMjUuNS0yMTMuNVMzMzIuMiA0MTAuNSA1MTIgNDEwLjUgODM3LjUgNTA2LjEgODM3LjUgNjI0IDY5MS44IDgzNy41IDUxMiA4MzcuNXpNODY4LjggNTA4Yy0xNy45LTI4LjEtNDIuMi01My43LTcxLjUtNzUuOSA5LTE4LjkgMjguMy0zMi4xIDUwLjYtMzIuMSAzMC45IDAgNTYgMjUuMSA1NiA1NiAuMSAyMy41LTE0LjUgNDMuNy0zNS4xIDUyek02MjQgNTY4YTU2IDU2IDAgMTAxMTIgMCA1NiA1NiAwIDEwLTExMiAweiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(RedditOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'RedditOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "RedditOutlinedSvg", "AntdIcon", "RedditOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/RedditOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport RedditOutlinedSvg from \"@ant-design/icons-svg/es/asn/RedditOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar RedditOutlined = function RedditOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: RedditOutlinedSvg\n  }));\n};\n\n/**![reddit](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4OCA1NjhhNTYgNTYgMCAxMDExMiAwIDU2IDU2IDAgMTAtMTEyIDB6bTMzOC43IDExOS43Yy0yMy4xIDE4LjItNjguOSAzNy44LTExNC43IDM3LjhzLTkxLjYtMTkuNi0xMTQuNy0zNy44Yy0xNC40LTExLjMtMzUuMy04LjktNDYuNyA1LjVzLTguOSAzNS4zIDUuNSA0Ni43QzM5Ni4zIDc3MS42IDQ1Ny41IDc5MiA1MTIgNzkyczExNS43LTIwLjQgMTU1LjktNTIuMWEzMy4yNSAzMy4yNSAwIDEwLTQxLjItNTIuMnpNOTYwIDQ1NmMwLTYxLjktNTAuMS0xMTItMTEyLTExMi00Mi4xIDAtNzguNyAyMy4yLTk3LjkgNTcuNi01Ny42LTMxLjUtMTI3LjctNTEuOC0yMDQuMS01Ni41TDYxMi45IDE5NWwxMjcuOSAzNi45YzExLjUgMzIuNiA0Mi42IDU2LjEgNzkuMiA1Ni4xIDQ2LjQgMCA4NC0zNy42IDg0LTg0cy0zNy42LTg0LTg0LTg0Yy0zMiAwLTU5LjggMTcuOS03NCA0NC4yTDYwMy41IDEyM2EzMy4yIDMzLjIgMCAwMC0zOS42IDE4LjRsLTkwLjggMjAzLjljLTc0LjUgNS4yLTE0Mi45IDI1LjQtMTk5LjIgNTYuMkExMTEuOTQgMTExLjk0IDAgMDAxNzYgMzQ0Yy02MS45IDAtMTEyIDUwLjEtMTEyIDExMiAwIDQ1LjggMjcuNSA4NS4xIDY2LjggMTAyLjUtNy4xIDIxLTEwLjggNDMtMTAuOCA2NS41IDAgMTU0LjYgMTc1LjUgMjgwIDM5MiAyODBzMzkyLTEyNS40IDM5Mi0yODBjMC0yMi42LTMuOC00NC41LTEwLjgtNjUuNUM5MzIuNSA1NDEuMSA5NjAgNTAxLjggOTYwIDQ1NnpNODIwIDE3Mi41YTMxLjUgMzEuNSAwIDExMCA2MyAzMS41IDMxLjUgMCAwMTAtNjN6TTEyMCA0NTZjMC0zMC45IDI1LjEtNTYgNTYtNTZhNTYgNTYgMCAwMTUwLjYgMzIuMWMtMjkuMyAyMi4yLTUzLjUgNDcuOC03MS41IDc1LjlhNTYuMjMgNTYuMjMgMCAwMS0zNS4xLTUyem0zOTIgMzgxLjVjLTE3OS44IDAtMzI1LjUtOTUuNi0zMjUuNS0yMTMuNVMzMzIuMiA0MTAuNSA1MTIgNDEwLjUgODM3LjUgNTA2LjEgODM3LjUgNjI0IDY5MS44IDgzNy41IDUxMiA4MzcuNXpNODY4LjggNTA4Yy0xNy45LTI4LjEtNDIuMi01My43LTcxLjUtNzUuOSA5LTE4LjkgMjguMy0zMi4xIDUwLjYtMzIuMSAzMC45IDAgNTYgMjUuMSA1NiA1NiAuMSAyMy41LTE0LjUgNDMuNy0zNS4xIDUyek02MjQgNTY4YTU2IDU2IDAgMTAxMTIgMCA1NiA1NiAwIDEwLTExMiAweiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(RedditOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'RedditOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,cAAc,CAAC;AAC3D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,gBAAgB;AACxC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}