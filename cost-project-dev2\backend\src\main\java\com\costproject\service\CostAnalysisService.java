package com.costproject.service;

import com.costproject.entity.*;
import com.costproject.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CostAnalysisService {

    private static final Logger log = LoggerFactory.getLogger(CostAnalysisService.class);

    private final TaskRepository taskRepository;
    private final BillItemRepository billItemRepository;
    private final QuotaItemRepository quotaItemRepository;
    private final ResourceRepository resourceRepository;
    
    public CostAnalysisService(TaskRepository taskRepository,
                              BillItemRepository billItemRepository,
                              QuotaItemRepository quotaItemRepository,
                              ResourceRepository resourceRepository) {
        this.taskRepository = taskRepository;
        this.billItemRepository = billItemRepository;
        this.quotaItemRepository = quotaItemRepository;
        this.resourceRepository = resourceRepository;
    }

    /**
     * 生成项目成本汇总报告
     */
    public Map<String, Object> generateProjectCostSummary(Long projectId) {
        Map<String, Object> summary = new HashMap<>();
        
        // 获取项目所有任务
        List<Task> tasks = taskRepository.findByProjectId(projectId);
        
        // 计算总成本
        BigDecimal totalCost = calculateTotalCost(tasks);
        summary.put("totalCost", totalCost);
        
        // 成本分类统计
        Map<String, BigDecimal> costByType = calculateCostByType(tasks);
        summary.put("costByType", costByType);
        
        // 成本构成分析
        Map<String, Object> costComposition = analyzeCostComposition(tasks);
        summary.put("costComposition", costComposition);
        
        // 计算关键指标
        Map<String, Object> keyMetrics = calculateKeyMetrics(tasks, totalCost);
        summary.put("keyMetrics", keyMetrics);
        
        return summary;
    }

    /**
     * 生成成本趋势分析
     */
    public Map<String, Object> analyzeCostTrend(Long projectId, String interval) {
        List<Task> tasks = taskRepository.findByProjectId(projectId);
        Map<String, Object> trend = new HashMap<>();
        
        // 按时间间隔统计成本
        Map<String, BigDecimal> costByPeriod = new HashMap<>();
        for (Task task : tasks) {
            String period = getPeriod(task.getStartDate(), interval);
            BigDecimal cost = calculateTaskCost(task);
            costByPeriod.merge(period, cost, BigDecimal::add);
        }
        trend.put("costByPeriod", costByPeriod);
        
        // 计算累计成本
        Map<String, BigDecimal> accumulatedCost = calculateAccumulatedCost(costByPeriod);
        trend.put("accumulatedCost", accumulatedCost);
        
        // 计算成本增长率
        Map<String, BigDecimal> growthRate = calculateGrowthRate(costByPeriod);
        trend.put("growthRate", growthRate);
        
        return trend;
    }

    /**
     * 生成资源成本分析
     */
    public Map<String, Object> analyzeResourceCost(Long projectId) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 获取项目资源使用情况
        Map<Resource, BigDecimal> resourceUsage = getResourceUsage(projectId);
        
        // 计算资源成本
        Map<String, Object> resourceCost = new HashMap<>();
        resourceUsage.forEach((resource, quantity) -> {
            Map<String, Object> costDetail = new HashMap<>();
            costDetail.put("quantity", quantity);
            costDetail.put("unitPrice", resource.getCurrentPrice());
            costDetail.put("totalCost", resource.getCurrentPrice().multiply(quantity));
            resourceCost.put(resource.getName(), costDetail);
        });
        analysis.put("resourceCost", resourceCost);
        
        // 资源成本占比
        Map<String, BigDecimal> costPercentage = calculateResourceCostPercentage(resourceCost);
        analysis.put("costPercentage", costPercentage);
        
        // 资源价格趋势
        Map<String, List<Map<String, Object>>> priceTrend = analyzeResourcePriceTrend(resourceUsage.keySet());
        analysis.put("priceTrend", priceTrend);
        
        return analysis;
    }

    /**
     * 生成成本预警报告
     */
    public List<Map<String, Object>> generateCostAlerts(Long projectId) {
        List<Map<String, Object>> alerts = new ArrayList<>();
        
        // 检查成本超支
        checkCostOverrun(projectId).ifPresent(alerts::add);
        
        // 检查资源价格异常
        alerts.addAll(checkResourcePriceAbnormal(projectId));
        
        // 检查成本增长异常
        checkCostGrowthAbnormal(projectId).ifPresent(alerts::add);
        
        // 检查预算执行异常
        checkBudgetExecution(projectId).ifPresent(alerts::add);
        
        return alerts;
    }

    /**
     * 生成成本优化建议
     */
    public List<Map<String, Object>> generateCostOptimizationSuggestions(Long projectId) {
        List<Map<String, Object>> suggestions = new ArrayList<>();
        
        // 分析资源利用效率
        analyzeResourceEfficiency(projectId).forEach(suggestions::add);
        
        // 分析成本结构优化空间
        analyzeCostStructure(projectId).forEach(suggestions::add);
        
        // 分析供应商选择
        analyzeSupplierOptions(projectId).forEach(suggestions::add);
        
        return suggestions;
    }

    // 私有辅助方法

    private BigDecimal calculateTotalCost(List<Task> tasks) {
        return tasks.stream()
            .map(this::calculateTaskCost)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal calculateTaskCost(Task task) {
        return task.getBillItems().stream()
            .map(BillItem::getTotalPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private Map<String, BigDecimal> calculateCostByType(List<Task> tasks) {
        Map<String, BigDecimal> costByType = new HashMap<>();
        
        tasks.forEach(task -> {
            task.getBillItems().forEach(billItem -> {
                billItem.getQuotaItems().forEach(quotaItem -> {
                    costByType.merge("labor", quotaItem.getLaborCost(), BigDecimal::add);
                    costByType.merge("material", quotaItem.getMaterialCost(), BigDecimal::add);
                    costByType.merge("machine", quotaItem.getMachineCost(), BigDecimal::add);
                });
            });
        });
        
        return costByType;
    }

    private Map<String, Object> analyzeCostComposition(List<Task> tasks) {
        Map<String, Object> composition = new HashMap<>();
        BigDecimal totalCost = calculateTotalCost(tasks);
        
        Map<String, BigDecimal> costByType = calculateCostByType(tasks);
        Map<String, BigDecimal> percentages = new HashMap<>();
        
        costByType.forEach((type, cost) -> {
            percentages.put(type, cost.divide(totalCost, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal(100)));
        });
        
        composition.put("amounts", costByType);
        composition.put("percentages", percentages);
        
        return composition;
    }

    private Map<String, Object> calculateKeyMetrics(List<Task> tasks, BigDecimal totalCost) {
        Map<String, Object> metrics = new HashMap<>();
        
        // 计算单位工程成本
        BigDecimal totalQuantity = tasks.stream()
            .map(Task::getQuantity)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        metrics.put("unitCost", totalCost.divide(totalQuantity, 2, BigDecimal.ROUND_HALF_UP));
        
        // 计算成本效率指标
        metrics.put("costEfficiencyIndex", calculateCostEfficiencyIndex(tasks));
        
        // 计算成本偏差
        metrics.put("costVariance", calculateCostVariance(tasks));
        
        return metrics;
    }

    private String getPeriod(LocalDateTime date, String interval) {
        // TODO: 根据间隔类型（日、周、月、季）返回对应的时间段标识
        return "";
    }

    private Map<String, BigDecimal> calculateAccumulatedCost(Map<String, BigDecimal> costByPeriod) {
        Map<String, BigDecimal> accumulated = new LinkedHashMap<>();
        BigDecimal total = BigDecimal.ZERO;
        
        for (Map.Entry<String, BigDecimal> entry : costByPeriod.entrySet()) {
            total = total.add(entry.getValue());
            accumulated.put(entry.getKey(), total);
        }
        
        return accumulated;
    }

    private Map<String, BigDecimal> calculateGrowthRate(Map<String, BigDecimal> costByPeriod) {
        Map<String, BigDecimal> growthRate = new LinkedHashMap<>();
        BigDecimal previousCost = null;
        
        for (Map.Entry<String, BigDecimal> entry : costByPeriod.entrySet()) {
            if (previousCost != null && previousCost.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growth = entry.getValue().subtract(previousCost)
                    .divide(previousCost, 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal(100));
                growthRate.put(entry.getKey(), growth);
            }
            previousCost = entry.getValue();
        }
        
        return growthRate;
    }

    private Map<Resource, BigDecimal> getResourceUsage(Long projectId) {
        // TODO: 统计项目资源使用情况
        return new HashMap<>();
    }

    private Map<String, BigDecimal> calculateResourceCostPercentage(Map<String, Object> resourceCost) {
        // TODO: 计算各资源成本占比
        return new HashMap<>();
    }

    private Map<String, List<Map<String, Object>>> analyzeResourcePriceTrend(Set<Resource> resources) {
        // TODO: 分析资源价格趋势
        return new HashMap<>();
    }

    private Optional<Map<String, Object>> checkCostOverrun(Long projectId) {
        // TODO: 检查成本超支情况
        return Optional.empty();
    }

    private List<Map<String, Object>> checkResourcePriceAbnormal(Long projectId) {
        // TODO: 检查资源价格异常
        return new ArrayList<>();
    }

    private Optional<Map<String, Object>> checkCostGrowthAbnormal(Long projectId) {
        // TODO: 检查成本增长异常
        return Optional.empty();
    }

    private Optional<Map<String, Object>> checkBudgetExecution(Long projectId) {
        // TODO: 检查预算执行情况
        return Optional.empty();
    }

    private List<Map<String, Object>> analyzeResourceEfficiency(Long projectId) {
        // TODO: 分析资源使用效率
        return new ArrayList<>();
    }

    private List<Map<String, Object>> analyzeCostStructure(Long projectId) {
        // TODO: 分析成本结构优化空间
        return new ArrayList<>();
    }

    private List<Map<String, Object>> analyzeSupplierOptions(Long projectId) {
        // TODO: 分析供应商选择优化建议
        return new ArrayList<>();
    }

    private BigDecimal calculateCostEfficiencyIndex(List<Task> tasks) {
        // TODO: 计算成本效率指标
        return BigDecimal.ONE;
    }

    private BigDecimal calculateCostVariance(List<Task> tasks) {
        // TODO: 计算成本偏差
        return BigDecimal.ZERO;
    }
}
