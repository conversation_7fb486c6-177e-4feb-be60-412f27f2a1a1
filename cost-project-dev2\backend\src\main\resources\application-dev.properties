# Server Configuration
server.port=8080
# server.servlet.context-path=/api

# H2 Database Configuration (for development)
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# H2 Console
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Logging Configuration
logging.level.root=INFO
logging.level.com.costproject=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Swagger Configuration
springfox.documentation.swagger-ui.path=/swagger-ui.html
springfox.documentation.swagger.v2.path=/v2/api-docs

# Spring MVC Configuration (fix for Swagger compatibility)
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Jackson Configuration
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=Asia/Shanghai

# Custom Application Properties
app.jwt.secret=your-secret-key
app.jwt.expiration=86400000 