{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"ece7f2a6bddb2b8cbe\", \"f1eef6bdc9e174a9cf0570b0\", \"f1eef6bdc9e174a9cf2b8cbe045a8d\", \"f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d\", \"f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b\", \"fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b\", \"fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/d3-scale-chromatic/src/sequential-multi/PuBu.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"ece7f2a6bddb2b8cbe\",\n  \"f1eef6bdc9e174a9cf0570b0\",\n  \"f1eef6bdc9e174a9cf2b8cbe045a8d\",\n  \"f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d\",\n  \"f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b\",\n  \"fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b\",\n  \"fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}