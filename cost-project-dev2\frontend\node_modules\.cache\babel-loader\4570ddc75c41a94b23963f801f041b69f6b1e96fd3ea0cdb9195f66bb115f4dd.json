{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ShoppingTwoToneSvg from \"@ant-design/icons-svg/es/asn/ShoppingTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ShoppingTwoTone = function ShoppingTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ShoppingTwoToneSvg\n  }));\n};\n\n/**![shopping](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5NiA0NzJjMCA0LjQtMy42IDgtOCA4aC01NmMtNC40IDAtOC0zLjYtOC04di04OEg0MDB2ODhjMCA0LjQtMy42IDgtOCA4aC01NmMtNC40IDAtOC0zLjYtOC04di04OGgtOTZ2NDU2aDU2MFYzODRoLTk2djg4eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODMyIDMxMkg2OTZ2LTE2YzAtMTAxLjYtODIuNC0xODQtMTg0LTE4NHMtMTg0IDgyLjQtMTg0IDE4NHYxNkgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjUzNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDMyLTE2YzAtNjEuOSA1MC4xLTExMiAxMTItMTEyczExMiA1MC4xIDExMiAxMTJ2MTZINDAwdi0xNnptMzkyIDU0NEgyMzJWMzg0aDk2djg4YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTg4aDIyNHY4OGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di04OGg5NnY0NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ShoppingTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ShoppingTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "ShoppingTwoToneSvg", "AntdIcon", "ShoppingTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/ShoppingTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ShoppingTwoToneSvg from \"@ant-design/icons-svg/es/asn/ShoppingTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ShoppingTwoTone = function ShoppingTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ShoppingTwoToneSvg\n  }));\n};\n\n/**![shopping](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5NiA0NzJjMCA0LjQtMy42IDgtOCA4aC01NmMtNC40IDAtOC0zLjYtOC04di04OEg0MDB2ODhjMCA0LjQtMy42IDgtOCA4aC01NmMtNC40IDAtOC0zLjYtOC04di04OGgtOTZ2NDU2aDU2MFYzODRoLTk2djg4eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODMyIDMxMkg2OTZ2LTE2YzAtMTAxLjYtODIuNC0xODQtMTg0LTE4NHMtMTg0IDgyLjQtMTg0IDE4NHYxNkgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjUzNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDMyLTE2YzAtNjEuOSA1MC4xLTExMiAxMTItMTEyczExMiA1MC4xIDExMiAxMTJ2MTZINDAwdi0xNnptMzkyIDU0NEgyMzJWMzg0aDk2djg4YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTg4aDIyNHY4OGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di04OGg5NnY0NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ShoppingTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ShoppingTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,eAAe,CAAC;AAC5D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,iBAAiB;AACzC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}