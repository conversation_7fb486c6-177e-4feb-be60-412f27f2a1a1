{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { genFocusStyle, resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genSwitchSmallStyle = token => {\n  const {\n    componentCls,\n    trackHeightSM,\n    trackPadding,\n    trackMinWidthSM,\n    innerMinMarginSM,\n    innerMaxMarginSM,\n    handleSizeSM,\n    calc\n  } = token;\n  const switchInnerCls = `${componentCls}-inner`;\n  const trackPaddingCalc = unit(calc(handleSizeSM).add(calc(trackPadding).mul(2)).equal());\n  const innerMaxMarginCalc = unit(calc(innerMaxMarginSM).mul(2).equal());\n  return {\n    [componentCls]: {\n      [`&${componentCls}-small`]: {\n        minWidth: trackMinWidthSM,\n        height: trackHeightSM,\n        lineHeight: unit(trackHeightSM),\n        [`${componentCls}-inner`]: {\n          paddingInlineStart: innerMaxMarginSM,\n          paddingInlineEnd: innerMinMarginSM,\n          [`${switchInnerCls}-checked, ${switchInnerCls}-unchecked`]: {\n            minHeight: trackHeightSM\n          },\n          [`${switchInnerCls}-checked`]: {\n            marginInlineStart: `calc(-100% + ${trackPaddingCalc} - ${innerMaxMarginCalc})`,\n            marginInlineEnd: `calc(100% - ${trackPaddingCalc} + ${innerMaxMarginCalc})`\n          },\n          [`${switchInnerCls}-unchecked`]: {\n            marginTop: calc(trackHeightSM).mul(-1).equal(),\n            marginInlineStart: 0,\n            marginInlineEnd: 0\n          }\n        },\n        [`${componentCls}-handle`]: {\n          width: handleSizeSM,\n          height: handleSizeSM\n        },\n        [`${componentCls}-loading-icon`]: {\n          top: calc(calc(handleSizeSM).sub(token.switchLoadingIconSize)).div(2).equal(),\n          fontSize: token.switchLoadingIconSize\n        },\n        [`&${componentCls}-checked`]: {\n          [`${componentCls}-inner`]: {\n            paddingInlineStart: innerMinMarginSM,\n            paddingInlineEnd: innerMaxMarginSM,\n            [`${switchInnerCls}-checked`]: {\n              marginInlineStart: 0,\n              marginInlineEnd: 0\n            },\n            [`${switchInnerCls}-unchecked`]: {\n              marginInlineStart: `calc(100% - ${trackPaddingCalc} + ${innerMaxMarginCalc})`,\n              marginInlineEnd: `calc(-100% + ${trackPaddingCalc} - ${innerMaxMarginCalc})`\n            }\n          },\n          [`${componentCls}-handle`]: {\n            insetInlineStart: `calc(100% - ${unit(calc(handleSizeSM).add(trackPadding).equal())})`\n          }\n        },\n        [`&:not(${componentCls}-disabled):active`]: {\n          [`&:not(${componentCls}-checked) ${switchInnerCls}`]: {\n            [`${switchInnerCls}-unchecked`]: {\n              marginInlineStart: calc(token.marginXXS).div(2).equal(),\n              marginInlineEnd: calc(token.marginXXS).mul(-1).div(2).equal()\n            }\n          },\n          [`&${componentCls}-checked ${switchInnerCls}`]: {\n            [`${switchInnerCls}-checked`]: {\n              marginInlineStart: calc(token.marginXXS).mul(-1).div(2).equal(),\n              marginInlineEnd: calc(token.marginXXS).div(2).equal()\n            }\n          }\n        }\n      }\n    }\n  };\n};\nconst genSwitchLoadingStyle = token => {\n  const {\n    componentCls,\n    handleSize,\n    calc\n  } = token;\n  return {\n    [componentCls]: {\n      [`${componentCls}-loading-icon${token.iconCls}`]: {\n        position: 'relative',\n        top: calc(calc(handleSize).sub(token.fontSize)).div(2).equal(),\n        color: token.switchLoadingIconColor,\n        verticalAlign: 'top'\n      },\n      [`&${componentCls}-checked ${componentCls}-loading-icon`]: {\n        color: token.switchColor\n      }\n    }\n  };\n};\nconst genSwitchHandleStyle = token => {\n  const {\n    componentCls,\n    trackPadding,\n    handleBg,\n    handleShadow,\n    handleSize,\n    calc\n  } = token;\n  const switchHandleCls = `${componentCls}-handle`;\n  return {\n    [componentCls]: {\n      [switchHandleCls]: {\n        position: 'absolute',\n        top: trackPadding,\n        insetInlineStart: trackPadding,\n        width: handleSize,\n        height: handleSize,\n        transition: `all ${token.switchDuration} ease-in-out`,\n        '&::before': {\n          position: 'absolute',\n          top: 0,\n          insetInlineEnd: 0,\n          bottom: 0,\n          insetInlineStart: 0,\n          backgroundColor: handleBg,\n          borderRadius: calc(handleSize).div(2).equal(),\n          boxShadow: handleShadow,\n          transition: `all ${token.switchDuration} ease-in-out`,\n          content: '\"\"'\n        }\n      },\n      [`&${componentCls}-checked ${switchHandleCls}`]: {\n        insetInlineStart: `calc(100% - ${unit(calc(handleSize).add(trackPadding).equal())})`\n      },\n      [`&:not(${componentCls}-disabled):active`]: {\n        [`${switchHandleCls}::before`]: {\n          insetInlineEnd: token.switchHandleActiveInset,\n          insetInlineStart: 0\n        },\n        [`&${componentCls}-checked ${switchHandleCls}::before`]: {\n          insetInlineEnd: 0,\n          insetInlineStart: token.switchHandleActiveInset\n        }\n      }\n    }\n  };\n};\nconst genSwitchInnerStyle = token => {\n  const {\n    componentCls,\n    trackHeight,\n    trackPadding,\n    innerMinMargin,\n    innerMaxMargin,\n    handleSize,\n    calc\n  } = token;\n  const switchInnerCls = `${componentCls}-inner`;\n  const trackPaddingCalc = unit(calc(handleSize).add(calc(trackPadding).mul(2)).equal());\n  const innerMaxMarginCalc = unit(calc(innerMaxMargin).mul(2).equal());\n  return {\n    [componentCls]: {\n      [switchInnerCls]: {\n        display: 'block',\n        overflow: 'hidden',\n        borderRadius: 100,\n        height: '100%',\n        paddingInlineStart: innerMaxMargin,\n        paddingInlineEnd: innerMinMargin,\n        transition: `padding-inline-start ${token.switchDuration} ease-in-out, padding-inline-end ${token.switchDuration} ease-in-out`,\n        [`${switchInnerCls}-checked, ${switchInnerCls}-unchecked`]: {\n          display: 'block',\n          color: token.colorTextLightSolid,\n          fontSize: token.fontSizeSM,\n          transition: `margin-inline-start ${token.switchDuration} ease-in-out, margin-inline-end ${token.switchDuration} ease-in-out`,\n          pointerEvents: 'none',\n          minHeight: trackHeight\n        },\n        [`${switchInnerCls}-checked`]: {\n          marginInlineStart: `calc(-100% + ${trackPaddingCalc} - ${innerMaxMarginCalc})`,\n          marginInlineEnd: `calc(100% - ${trackPaddingCalc} + ${innerMaxMarginCalc})`\n        },\n        [`${switchInnerCls}-unchecked`]: {\n          marginTop: calc(trackHeight).mul(-1).equal(),\n          marginInlineStart: 0,\n          marginInlineEnd: 0\n        }\n      },\n      [`&${componentCls}-checked ${switchInnerCls}`]: {\n        paddingInlineStart: innerMinMargin,\n        paddingInlineEnd: innerMaxMargin,\n        [`${switchInnerCls}-checked`]: {\n          marginInlineStart: 0,\n          marginInlineEnd: 0\n        },\n        [`${switchInnerCls}-unchecked`]: {\n          marginInlineStart: `calc(100% - ${trackPaddingCalc} + ${innerMaxMarginCalc})`,\n          marginInlineEnd: `calc(-100% + ${trackPaddingCalc} - ${innerMaxMarginCalc})`\n        }\n      },\n      [`&:not(${componentCls}-disabled):active`]: {\n        [`&:not(${componentCls}-checked) ${switchInnerCls}`]: {\n          [`${switchInnerCls}-unchecked`]: {\n            marginInlineStart: calc(trackPadding).mul(2).equal(),\n            marginInlineEnd: calc(trackPadding).mul(-1).mul(2).equal()\n          }\n        },\n        [`&${componentCls}-checked ${switchInnerCls}`]: {\n          [`${switchInnerCls}-checked`]: {\n            marginInlineStart: calc(trackPadding).mul(-1).mul(2).equal(),\n            marginInlineEnd: calc(trackPadding).mul(2).equal()\n          }\n        }\n      }\n    }\n  };\n};\nconst genSwitchStyle = token => {\n  const {\n    componentCls,\n    trackHeight,\n    trackMinWidth\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'inline-block',\n      boxSizing: 'border-box',\n      minWidth: trackMinWidth,\n      height: trackHeight,\n      lineHeight: unit(trackHeight),\n      verticalAlign: 'middle',\n      background: token.colorTextQuaternary,\n      border: '0',\n      borderRadius: 100,\n      cursor: 'pointer',\n      transition: `all ${token.motionDurationMid}`,\n      userSelect: 'none',\n      [`&:hover:not(${componentCls}-disabled)`]: {\n        background: token.colorTextTertiary\n      }\n    }), genFocusStyle(token)), {\n      [`&${componentCls}-checked`]: {\n        background: token.switchColor,\n        [`&:hover:not(${componentCls}-disabled)`]: {\n          background: token.colorPrimaryHover\n        }\n      },\n      [`&${componentCls}-loading, &${componentCls}-disabled`]: {\n        cursor: 'not-allowed',\n        opacity: token.switchDisabledOpacity,\n        '*': {\n          boxShadow: 'none',\n          cursor: 'not-allowed'\n        }\n      },\n      // rtl style\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    fontSize,\n    lineHeight,\n    controlHeight,\n    colorWhite\n  } = token;\n  const height = fontSize * lineHeight;\n  const heightSM = controlHeight / 2;\n  const padding = 2; // Fixed value\n  const handleSize = height - padding * 2;\n  const handleSizeSM = heightSM - padding * 2;\n  return {\n    trackHeight: height,\n    trackHeightSM: heightSM,\n    trackMinWidth: handleSize * 2 + padding * 4,\n    trackMinWidthSM: handleSizeSM * 2 + padding * 2,\n    trackPadding: padding,\n    // Fixed value\n    handleBg: colorWhite,\n    handleSize,\n    handleSizeSM,\n    handleShadow: `0 2px 4px 0 ${new FastColor('#00230b').setA(0.2).toRgbString()}`,\n    innerMinMargin: handleSize / 2,\n    innerMaxMargin: handleSize + padding + padding * 2,\n    innerMinMarginSM: handleSizeSM / 2,\n    innerMaxMarginSM: handleSizeSM + padding + padding * 2\n  };\n};\nexport default genStyleHooks('Switch', token => {\n  const switchToken = mergeToken(token, {\n    switchDuration: token.motionDurationMid,\n    switchColor: token.colorPrimary,\n    switchDisabledOpacity: token.opacityLoading,\n    switchLoadingIconSize: token.calc(token.fontSizeIcon).mul(0.75).equal(),\n    switchLoadingIconColor: `rgba(0, 0, 0, ${token.opacityLoading})`,\n    switchHandleActiveInset: '-30%'\n  });\n  return [genSwitchStyle(switchToken),\n  // inner style\n  genSwitchInnerStyle(switchToken),\n  // handle style\n  genSwitchHandleStyle(switchToken),\n  // loading style\n  genSwitchLoadingStyle(switchToken),\n  // small style\n  genSwitchSmallStyle(switchToken)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["unit", "FastColor", "genFocusStyle", "resetComponent", "genStyleHooks", "mergeToken", "genSwitchSmallStyle", "token", "componentCls", "trackHeightSM", "trackPadding", "trackMinWidthSM", "innerMinMarginSM", "innerMaxMarginSM", "handleSizeSM", "calc", "switchInnerCls", "trackPaddingCalc", "add", "mul", "equal", "innerMaxMarginCalc", "min<PERSON><PERSON><PERSON>", "height", "lineHeight", "paddingInlineStart", "paddingInlineEnd", "minHeight", "marginInlineStart", "marginInlineEnd", "marginTop", "width", "top", "sub", "switchLoadingIconSize", "div", "fontSize", "insetInlineStart", "marginXXS", "genSwitchLoadingStyle", "handleSize", "iconCls", "position", "color", "switchLoadingIconColor", "verticalAlign", "switchColor", "genSwitchHandleStyle", "handleBg", "handleShadow", "switchHandleCls", "transition", "switchDuration", "insetInlineEnd", "bottom", "backgroundColor", "borderRadius", "boxShadow", "content", "switchHandleActiveInset", "genSwitchInnerStyle", "trackHeight", "inner<PERSON>in<PERSON>argin", "innerMaxMargin", "display", "overflow", "colorTextLightSolid", "fontSizeSM", "pointerEvents", "genSwitchStyle", "trackMinWidth", "Object", "assign", "boxSizing", "background", "colorTextQuaternary", "border", "cursor", "motionDurationMid", "userSelect", "colorTextTertiary", "colorPrimaryHover", "opacity", "switchDisabledOpacity", "direction", "prepareComponentToken", "controlHeight", "colorWhite", "heightSM", "padding", "setA", "toRgbString", "switchToken", "colorPrimary", "opacityLoading", "fontSizeIcon"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/antd/es/switch/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { genFocusStyle, resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genSwitchSmallStyle = token => {\n  const {\n    componentCls,\n    trackHeightSM,\n    trackPadding,\n    trackMinWidthSM,\n    innerMinMarginSM,\n    innerMaxMarginSM,\n    handleSizeSM,\n    calc\n  } = token;\n  const switchInnerCls = `${componentCls}-inner`;\n  const trackPaddingCalc = unit(calc(handleSizeSM).add(calc(trackPadding).mul(2)).equal());\n  const innerMaxMarginCalc = unit(calc(innerMaxMarginSM).mul(2).equal());\n  return {\n    [componentCls]: {\n      [`&${componentCls}-small`]: {\n        minWidth: trackMinWidthSM,\n        height: trackHeightSM,\n        lineHeight: unit(trackHeightSM),\n        [`${componentCls}-inner`]: {\n          paddingInlineStart: innerMaxMarginSM,\n          paddingInlineEnd: innerMinMarginSM,\n          [`${switchInnerCls}-checked, ${switchInnerCls}-unchecked`]: {\n            minHeight: trackHeightSM\n          },\n          [`${switchInnerCls}-checked`]: {\n            marginInlineStart: `calc(-100% + ${trackPaddingCalc} - ${innerMaxMarginCalc})`,\n            marginInlineEnd: `calc(100% - ${trackPaddingCalc} + ${innerMaxMarginCalc})`\n          },\n          [`${switchInnerCls}-unchecked`]: {\n            marginTop: calc(trackHeightSM).mul(-1).equal(),\n            marginInlineStart: 0,\n            marginInlineEnd: 0\n          }\n        },\n        [`${componentCls}-handle`]: {\n          width: handleSizeSM,\n          height: handleSizeSM\n        },\n        [`${componentCls}-loading-icon`]: {\n          top: calc(calc(handleSizeSM).sub(token.switchLoadingIconSize)).div(2).equal(),\n          fontSize: token.switchLoadingIconSize\n        },\n        [`&${componentCls}-checked`]: {\n          [`${componentCls}-inner`]: {\n            paddingInlineStart: innerMinMarginSM,\n            paddingInlineEnd: innerMaxMarginSM,\n            [`${switchInnerCls}-checked`]: {\n              marginInlineStart: 0,\n              marginInlineEnd: 0\n            },\n            [`${switchInnerCls}-unchecked`]: {\n              marginInlineStart: `calc(100% - ${trackPaddingCalc} + ${innerMaxMarginCalc})`,\n              marginInlineEnd: `calc(-100% + ${trackPaddingCalc} - ${innerMaxMarginCalc})`\n            }\n          },\n          [`${componentCls}-handle`]: {\n            insetInlineStart: `calc(100% - ${unit(calc(handleSizeSM).add(trackPadding).equal())})`\n          }\n        },\n        [`&:not(${componentCls}-disabled):active`]: {\n          [`&:not(${componentCls}-checked) ${switchInnerCls}`]: {\n            [`${switchInnerCls}-unchecked`]: {\n              marginInlineStart: calc(token.marginXXS).div(2).equal(),\n              marginInlineEnd: calc(token.marginXXS).mul(-1).div(2).equal()\n            }\n          },\n          [`&${componentCls}-checked ${switchInnerCls}`]: {\n            [`${switchInnerCls}-checked`]: {\n              marginInlineStart: calc(token.marginXXS).mul(-1).div(2).equal(),\n              marginInlineEnd: calc(token.marginXXS).div(2).equal()\n            }\n          }\n        }\n      }\n    }\n  };\n};\nconst genSwitchLoadingStyle = token => {\n  const {\n    componentCls,\n    handleSize,\n    calc\n  } = token;\n  return {\n    [componentCls]: {\n      [`${componentCls}-loading-icon${token.iconCls}`]: {\n        position: 'relative',\n        top: calc(calc(handleSize).sub(token.fontSize)).div(2).equal(),\n        color: token.switchLoadingIconColor,\n        verticalAlign: 'top'\n      },\n      [`&${componentCls}-checked ${componentCls}-loading-icon`]: {\n        color: token.switchColor\n      }\n    }\n  };\n};\nconst genSwitchHandleStyle = token => {\n  const {\n    componentCls,\n    trackPadding,\n    handleBg,\n    handleShadow,\n    handleSize,\n    calc\n  } = token;\n  const switchHandleCls = `${componentCls}-handle`;\n  return {\n    [componentCls]: {\n      [switchHandleCls]: {\n        position: 'absolute',\n        top: trackPadding,\n        insetInlineStart: trackPadding,\n        width: handleSize,\n        height: handleSize,\n        transition: `all ${token.switchDuration} ease-in-out`,\n        '&::before': {\n          position: 'absolute',\n          top: 0,\n          insetInlineEnd: 0,\n          bottom: 0,\n          insetInlineStart: 0,\n          backgroundColor: handleBg,\n          borderRadius: calc(handleSize).div(2).equal(),\n          boxShadow: handleShadow,\n          transition: `all ${token.switchDuration} ease-in-out`,\n          content: '\"\"'\n        }\n      },\n      [`&${componentCls}-checked ${switchHandleCls}`]: {\n        insetInlineStart: `calc(100% - ${unit(calc(handleSize).add(trackPadding).equal())})`\n      },\n      [`&:not(${componentCls}-disabled):active`]: {\n        [`${switchHandleCls}::before`]: {\n          insetInlineEnd: token.switchHandleActiveInset,\n          insetInlineStart: 0\n        },\n        [`&${componentCls}-checked ${switchHandleCls}::before`]: {\n          insetInlineEnd: 0,\n          insetInlineStart: token.switchHandleActiveInset\n        }\n      }\n    }\n  };\n};\nconst genSwitchInnerStyle = token => {\n  const {\n    componentCls,\n    trackHeight,\n    trackPadding,\n    innerMinMargin,\n    innerMaxMargin,\n    handleSize,\n    calc\n  } = token;\n  const switchInnerCls = `${componentCls}-inner`;\n  const trackPaddingCalc = unit(calc(handleSize).add(calc(trackPadding).mul(2)).equal());\n  const innerMaxMarginCalc = unit(calc(innerMaxMargin).mul(2).equal());\n  return {\n    [componentCls]: {\n      [switchInnerCls]: {\n        display: 'block',\n        overflow: 'hidden',\n        borderRadius: 100,\n        height: '100%',\n        paddingInlineStart: innerMaxMargin,\n        paddingInlineEnd: innerMinMargin,\n        transition: `padding-inline-start ${token.switchDuration} ease-in-out, padding-inline-end ${token.switchDuration} ease-in-out`,\n        [`${switchInnerCls}-checked, ${switchInnerCls}-unchecked`]: {\n          display: 'block',\n          color: token.colorTextLightSolid,\n          fontSize: token.fontSizeSM,\n          transition: `margin-inline-start ${token.switchDuration} ease-in-out, margin-inline-end ${token.switchDuration} ease-in-out`,\n          pointerEvents: 'none',\n          minHeight: trackHeight\n        },\n        [`${switchInnerCls}-checked`]: {\n          marginInlineStart: `calc(-100% + ${trackPaddingCalc} - ${innerMaxMarginCalc})`,\n          marginInlineEnd: `calc(100% - ${trackPaddingCalc} + ${innerMaxMarginCalc})`\n        },\n        [`${switchInnerCls}-unchecked`]: {\n          marginTop: calc(trackHeight).mul(-1).equal(),\n          marginInlineStart: 0,\n          marginInlineEnd: 0\n        }\n      },\n      [`&${componentCls}-checked ${switchInnerCls}`]: {\n        paddingInlineStart: innerMinMargin,\n        paddingInlineEnd: innerMaxMargin,\n        [`${switchInnerCls}-checked`]: {\n          marginInlineStart: 0,\n          marginInlineEnd: 0\n        },\n        [`${switchInnerCls}-unchecked`]: {\n          marginInlineStart: `calc(100% - ${trackPaddingCalc} + ${innerMaxMarginCalc})`,\n          marginInlineEnd: `calc(-100% + ${trackPaddingCalc} - ${innerMaxMarginCalc})`\n        }\n      },\n      [`&:not(${componentCls}-disabled):active`]: {\n        [`&:not(${componentCls}-checked) ${switchInnerCls}`]: {\n          [`${switchInnerCls}-unchecked`]: {\n            marginInlineStart: calc(trackPadding).mul(2).equal(),\n            marginInlineEnd: calc(trackPadding).mul(-1).mul(2).equal()\n          }\n        },\n        [`&${componentCls}-checked ${switchInnerCls}`]: {\n          [`${switchInnerCls}-checked`]: {\n            marginInlineStart: calc(trackPadding).mul(-1).mul(2).equal(),\n            marginInlineEnd: calc(trackPadding).mul(2).equal()\n          }\n        }\n      }\n    }\n  };\n};\nconst genSwitchStyle = token => {\n  const {\n    componentCls,\n    trackHeight,\n    trackMinWidth\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'inline-block',\n      boxSizing: 'border-box',\n      minWidth: trackMinWidth,\n      height: trackHeight,\n      lineHeight: unit(trackHeight),\n      verticalAlign: 'middle',\n      background: token.colorTextQuaternary,\n      border: '0',\n      borderRadius: 100,\n      cursor: 'pointer',\n      transition: `all ${token.motionDurationMid}`,\n      userSelect: 'none',\n      [`&:hover:not(${componentCls}-disabled)`]: {\n        background: token.colorTextTertiary\n      }\n    }), genFocusStyle(token)), {\n      [`&${componentCls}-checked`]: {\n        background: token.switchColor,\n        [`&:hover:not(${componentCls}-disabled)`]: {\n          background: token.colorPrimaryHover\n        }\n      },\n      [`&${componentCls}-loading, &${componentCls}-disabled`]: {\n        cursor: 'not-allowed',\n        opacity: token.switchDisabledOpacity,\n        '*': {\n          boxShadow: 'none',\n          cursor: 'not-allowed'\n        }\n      },\n      // rtl style\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    fontSize,\n    lineHeight,\n    controlHeight,\n    colorWhite\n  } = token;\n  const height = fontSize * lineHeight;\n  const heightSM = controlHeight / 2;\n  const padding = 2; // Fixed value\n  const handleSize = height - padding * 2;\n  const handleSizeSM = heightSM - padding * 2;\n  return {\n    trackHeight: height,\n    trackHeightSM: heightSM,\n    trackMinWidth: handleSize * 2 + padding * 4,\n    trackMinWidthSM: handleSizeSM * 2 + padding * 2,\n    trackPadding: padding,\n    // Fixed value\n    handleBg: colorWhite,\n    handleSize,\n    handleSizeSM,\n    handleShadow: `0 2px 4px 0 ${new FastColor('#00230b').setA(0.2).toRgbString()}`,\n    innerMinMargin: handleSize / 2,\n    innerMaxMargin: handleSize + padding + padding * 2,\n    innerMinMarginSM: handleSizeSM / 2,\n    innerMaxMarginSM: handleSizeSM + padding + padding * 2\n  };\n};\nexport default genStyleHooks('Switch', token => {\n  const switchToken = mergeToken(token, {\n    switchDuration: token.motionDurationMid,\n    switchColor: token.colorPrimary,\n    switchDisabledOpacity: token.opacityLoading,\n    switchLoadingIconSize: token.calc(token.fontSizeIcon).mul(0.75).equal(),\n    switchLoadingIconColor: `rgba(0, 0, 0, ${token.opacityLoading})`,\n    switchHandleActiveInset: '-30%'\n  });\n  return [genSwitchStyle(switchToken),\n  // inner style\n  genSwitchInnerStyle(switchToken),\n  // handle style\n  genSwitchHandleStyle(switchToken),\n  // loading style\n  genSwitchLoadingStyle(switchToken),\n  // small style\n  genSwitchSmallStyle(switchToken)];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,aAAa,EAAEC,cAAc,QAAQ,aAAa;AAC3D,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,MAAMC,mBAAmB,GAAGC,KAAK,IAAI;EACnC,MAAM;IACJC,YAAY;IACZC,aAAa;IACbC,YAAY;IACZC,eAAe;IACfC,gBAAgB;IAChBC,gBAAgB;IAChBC,YAAY;IACZC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMS,cAAc,GAAG,GAAGR,YAAY,QAAQ;EAC9C,MAAMS,gBAAgB,GAAGjB,IAAI,CAACe,IAAI,CAACD,YAAY,CAAC,CAACI,GAAG,CAACH,IAAI,CAACL,YAAY,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;EACxF,MAAMC,kBAAkB,GAAGrB,IAAI,CAACe,IAAI,CAACF,gBAAgB,CAAC,CAACM,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;EACtE,OAAO;IACL,CAACZ,YAAY,GAAG;MACd,CAAC,IAAIA,YAAY,QAAQ,GAAG;QAC1Bc,QAAQ,EAAEX,eAAe;QACzBY,MAAM,EAAEd,aAAa;QACrBe,UAAU,EAAExB,IAAI,CAACS,aAAa,CAAC;QAC/B,CAAC,GAAGD,YAAY,QAAQ,GAAG;UACzBiB,kBAAkB,EAAEZ,gBAAgB;UACpCa,gBAAgB,EAAEd,gBAAgB;UAClC,CAAC,GAAGI,cAAc,aAAaA,cAAc,YAAY,GAAG;YAC1DW,SAAS,EAAElB;UACb,CAAC;UACD,CAAC,GAAGO,cAAc,UAAU,GAAG;YAC7BY,iBAAiB,EAAE,gBAAgBX,gBAAgB,MAAMI,kBAAkB,GAAG;YAC9EQ,eAAe,EAAE,eAAeZ,gBAAgB,MAAMI,kBAAkB;UAC1E,CAAC;UACD,CAAC,GAAGL,cAAc,YAAY,GAAG;YAC/Bc,SAAS,EAAEf,IAAI,CAACN,aAAa,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YAC9CQ,iBAAiB,EAAE,CAAC;YACpBC,eAAe,EAAE;UACnB;QACF,CAAC;QACD,CAAC,GAAGrB,YAAY,SAAS,GAAG;UAC1BuB,KAAK,EAAEjB,YAAY;UACnBS,MAAM,EAAET;QACV,CAAC;QACD,CAAC,GAAGN,YAAY,eAAe,GAAG;UAChCwB,GAAG,EAAEjB,IAAI,CAACA,IAAI,CAACD,YAAY,CAAC,CAACmB,GAAG,CAAC1B,KAAK,CAAC2B,qBAAqB,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAACf,KAAK,CAAC,CAAC;UAC7EgB,QAAQ,EAAE7B,KAAK,CAAC2B;QAClB,CAAC;QACD,CAAC,IAAI1B,YAAY,UAAU,GAAG;UAC5B,CAAC,GAAGA,YAAY,QAAQ,GAAG;YACzBiB,kBAAkB,EAAEb,gBAAgB;YACpCc,gBAAgB,EAAEb,gBAAgB;YAClC,CAAC,GAAGG,cAAc,UAAU,GAAG;cAC7BY,iBAAiB,EAAE,CAAC;cACpBC,eAAe,EAAE;YACnB,CAAC;YACD,CAAC,GAAGb,cAAc,YAAY,GAAG;cAC/BY,iBAAiB,EAAE,eAAeX,gBAAgB,MAAMI,kBAAkB,GAAG;cAC7EQ,eAAe,EAAE,gBAAgBZ,gBAAgB,MAAMI,kBAAkB;YAC3E;UACF,CAAC;UACD,CAAC,GAAGb,YAAY,SAAS,GAAG;YAC1B6B,gBAAgB,EAAE,eAAerC,IAAI,CAACe,IAAI,CAACD,YAAY,CAAC,CAACI,GAAG,CAACR,YAAY,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC;UACrF;QACF,CAAC;QACD,CAAC,SAASZ,YAAY,mBAAmB,GAAG;UAC1C,CAAC,SAASA,YAAY,aAAaQ,cAAc,EAAE,GAAG;YACpD,CAAC,GAAGA,cAAc,YAAY,GAAG;cAC/BY,iBAAiB,EAAEb,IAAI,CAACR,KAAK,CAAC+B,SAAS,CAAC,CAACH,GAAG,CAAC,CAAC,CAAC,CAACf,KAAK,CAAC,CAAC;cACvDS,eAAe,EAAEd,IAAI,CAACR,KAAK,CAAC+B,SAAS,CAAC,CAACnB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACgB,GAAG,CAAC,CAAC,CAAC,CAACf,KAAK,CAAC;YAC9D;UACF,CAAC;UACD,CAAC,IAAIZ,YAAY,YAAYQ,cAAc,EAAE,GAAG;YAC9C,CAAC,GAAGA,cAAc,UAAU,GAAG;cAC7BY,iBAAiB,EAAEb,IAAI,CAACR,KAAK,CAAC+B,SAAS,CAAC,CAACnB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACgB,GAAG,CAAC,CAAC,CAAC,CAACf,KAAK,CAAC,CAAC;cAC/DS,eAAe,EAAEd,IAAI,CAACR,KAAK,CAAC+B,SAAS,CAAC,CAACH,GAAG,CAAC,CAAC,CAAC,CAACf,KAAK,CAAC;YACtD;UACF;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMmB,qBAAqB,GAAGhC,KAAK,IAAI;EACrC,MAAM;IACJC,YAAY;IACZgC,UAAU;IACVzB;EACF,CAAC,GAAGR,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACd,CAAC,GAAGA,YAAY,gBAAgBD,KAAK,CAACkC,OAAO,EAAE,GAAG;QAChDC,QAAQ,EAAE,UAAU;QACpBV,GAAG,EAAEjB,IAAI,CAACA,IAAI,CAACyB,UAAU,CAAC,CAACP,GAAG,CAAC1B,KAAK,CAAC6B,QAAQ,CAAC,CAAC,CAACD,GAAG,CAAC,CAAC,CAAC,CAACf,KAAK,CAAC,CAAC;QAC9DuB,KAAK,EAAEpC,KAAK,CAACqC,sBAAsB;QACnCC,aAAa,EAAE;MACjB,CAAC;MACD,CAAC,IAAIrC,YAAY,YAAYA,YAAY,eAAe,GAAG;QACzDmC,KAAK,EAAEpC,KAAK,CAACuC;MACf;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMC,oBAAoB,GAAGxC,KAAK,IAAI;EACpC,MAAM;IACJC,YAAY;IACZE,YAAY;IACZsC,QAAQ;IACRC,YAAY;IACZT,UAAU;IACVzB;EACF,CAAC,GAAGR,KAAK;EACT,MAAM2C,eAAe,GAAG,GAAG1C,YAAY,SAAS;EAChD,OAAO;IACL,CAACA,YAAY,GAAG;MACd,CAAC0C,eAAe,GAAG;QACjBR,QAAQ,EAAE,UAAU;QACpBV,GAAG,EAAEtB,YAAY;QACjB2B,gBAAgB,EAAE3B,YAAY;QAC9BqB,KAAK,EAAES,UAAU;QACjBjB,MAAM,EAAEiB,UAAU;QAClBW,UAAU,EAAE,OAAO5C,KAAK,CAAC6C,cAAc,cAAc;QACrD,WAAW,EAAE;UACXV,QAAQ,EAAE,UAAU;UACpBV,GAAG,EAAE,CAAC;UACNqB,cAAc,EAAE,CAAC;UACjBC,MAAM,EAAE,CAAC;UACTjB,gBAAgB,EAAE,CAAC;UACnBkB,eAAe,EAAEP,QAAQ;UACzBQ,YAAY,EAAEzC,IAAI,CAACyB,UAAU,CAAC,CAACL,GAAG,CAAC,CAAC,CAAC,CAACf,KAAK,CAAC,CAAC;UAC7CqC,SAAS,EAAER,YAAY;UACvBE,UAAU,EAAE,OAAO5C,KAAK,CAAC6C,cAAc,cAAc;UACrDM,OAAO,EAAE;QACX;MACF,CAAC;MACD,CAAC,IAAIlD,YAAY,YAAY0C,eAAe,EAAE,GAAG;QAC/Cb,gBAAgB,EAAE,eAAerC,IAAI,CAACe,IAAI,CAACyB,UAAU,CAAC,CAACtB,GAAG,CAACR,YAAY,CAAC,CAACU,KAAK,CAAC,CAAC,CAAC;MACnF,CAAC;MACD,CAAC,SAASZ,YAAY,mBAAmB,GAAG;QAC1C,CAAC,GAAG0C,eAAe,UAAU,GAAG;UAC9BG,cAAc,EAAE9C,KAAK,CAACoD,uBAAuB;UAC7CtB,gBAAgB,EAAE;QACpB,CAAC;QACD,CAAC,IAAI7B,YAAY,YAAY0C,eAAe,UAAU,GAAG;UACvDG,cAAc,EAAE,CAAC;UACjBhB,gBAAgB,EAAE9B,KAAK,CAACoD;QAC1B;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMC,mBAAmB,GAAGrD,KAAK,IAAI;EACnC,MAAM;IACJC,YAAY;IACZqD,WAAW;IACXnD,YAAY;IACZoD,cAAc;IACdC,cAAc;IACdvB,UAAU;IACVzB;EACF,CAAC,GAAGR,KAAK;EACT,MAAMS,cAAc,GAAG,GAAGR,YAAY,QAAQ;EAC9C,MAAMS,gBAAgB,GAAGjB,IAAI,CAACe,IAAI,CAACyB,UAAU,CAAC,CAACtB,GAAG,CAACH,IAAI,CAACL,YAAY,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;EACtF,MAAMC,kBAAkB,GAAGrB,IAAI,CAACe,IAAI,CAACgD,cAAc,CAAC,CAAC5C,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;EACpE,OAAO;IACL,CAACZ,YAAY,GAAG;MACd,CAACQ,cAAc,GAAG;QAChBgD,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE,QAAQ;QAClBT,YAAY,EAAE,GAAG;QACjBjC,MAAM,EAAE,MAAM;QACdE,kBAAkB,EAAEsC,cAAc;QAClCrC,gBAAgB,EAAEoC,cAAc;QAChCX,UAAU,EAAE,wBAAwB5C,KAAK,CAAC6C,cAAc,oCAAoC7C,KAAK,CAAC6C,cAAc,cAAc;QAC9H,CAAC,GAAGpC,cAAc,aAAaA,cAAc,YAAY,GAAG;UAC1DgD,OAAO,EAAE,OAAO;UAChBrB,KAAK,EAAEpC,KAAK,CAAC2D,mBAAmB;UAChC9B,QAAQ,EAAE7B,KAAK,CAAC4D,UAAU;UAC1BhB,UAAU,EAAE,uBAAuB5C,KAAK,CAAC6C,cAAc,mCAAmC7C,KAAK,CAAC6C,cAAc,cAAc;UAC5HgB,aAAa,EAAE,MAAM;UACrBzC,SAAS,EAAEkC;QACb,CAAC;QACD,CAAC,GAAG7C,cAAc,UAAU,GAAG;UAC7BY,iBAAiB,EAAE,gBAAgBX,gBAAgB,MAAMI,kBAAkB,GAAG;UAC9EQ,eAAe,EAAE,eAAeZ,gBAAgB,MAAMI,kBAAkB;QAC1E,CAAC;QACD,CAAC,GAAGL,cAAc,YAAY,GAAG;UAC/Bc,SAAS,EAAEf,IAAI,CAAC8C,WAAW,CAAC,CAAC1C,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;UAC5CQ,iBAAiB,EAAE,CAAC;UACpBC,eAAe,EAAE;QACnB;MACF,CAAC;MACD,CAAC,IAAIrB,YAAY,YAAYQ,cAAc,EAAE,GAAG;QAC9CS,kBAAkB,EAAEqC,cAAc;QAClCpC,gBAAgB,EAAEqC,cAAc;QAChC,CAAC,GAAG/C,cAAc,UAAU,GAAG;UAC7BY,iBAAiB,EAAE,CAAC;UACpBC,eAAe,EAAE;QACnB,CAAC;QACD,CAAC,GAAGb,cAAc,YAAY,GAAG;UAC/BY,iBAAiB,EAAE,eAAeX,gBAAgB,MAAMI,kBAAkB,GAAG;UAC7EQ,eAAe,EAAE,gBAAgBZ,gBAAgB,MAAMI,kBAAkB;QAC3E;MACF,CAAC;MACD,CAAC,SAASb,YAAY,mBAAmB,GAAG;QAC1C,CAAC,SAASA,YAAY,aAAaQ,cAAc,EAAE,GAAG;UACpD,CAAC,GAAGA,cAAc,YAAY,GAAG;YAC/BY,iBAAiB,EAAEb,IAAI,CAACL,YAAY,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YACpDS,eAAe,EAAEd,IAAI,CAACL,YAAY,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;UAC3D;QACF,CAAC;QACD,CAAC,IAAIZ,YAAY,YAAYQ,cAAc,EAAE,GAAG;UAC9C,CAAC,GAAGA,cAAc,UAAU,GAAG;YAC7BY,iBAAiB,EAAEb,IAAI,CAACL,YAAY,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YAC5DS,eAAe,EAAEd,IAAI,CAACL,YAAY,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;UACnD;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMiD,cAAc,GAAG9D,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZqD,WAAW;IACXS;EACF,CAAC,GAAG/D,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG+D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErE,cAAc,CAACI,KAAK,CAAC,CAAC,EAAE;MAClGmC,QAAQ,EAAE,UAAU;MACpBsB,OAAO,EAAE,cAAc;MACvBS,SAAS,EAAE,YAAY;MACvBnD,QAAQ,EAAEgD,aAAa;MACvB/C,MAAM,EAAEsC,WAAW;MACnBrC,UAAU,EAAExB,IAAI,CAAC6D,WAAW,CAAC;MAC7BhB,aAAa,EAAE,QAAQ;MACvB6B,UAAU,EAAEnE,KAAK,CAACoE,mBAAmB;MACrCC,MAAM,EAAE,GAAG;MACXpB,YAAY,EAAE,GAAG;MACjBqB,MAAM,EAAE,SAAS;MACjB1B,UAAU,EAAE,OAAO5C,KAAK,CAACuE,iBAAiB,EAAE;MAC5CC,UAAU,EAAE,MAAM;MAClB,CAAC,eAAevE,YAAY,YAAY,GAAG;QACzCkE,UAAU,EAAEnE,KAAK,CAACyE;MACpB;IACF,CAAC,CAAC,EAAE9E,aAAa,CAACK,KAAK,CAAC,CAAC,EAAE;MACzB,CAAC,IAAIC,YAAY,UAAU,GAAG;QAC5BkE,UAAU,EAAEnE,KAAK,CAACuC,WAAW;QAC7B,CAAC,eAAetC,YAAY,YAAY,GAAG;UACzCkE,UAAU,EAAEnE,KAAK,CAAC0E;QACpB;MACF,CAAC;MACD,CAAC,IAAIzE,YAAY,cAAcA,YAAY,WAAW,GAAG;QACvDqE,MAAM,EAAE,aAAa;QACrBK,OAAO,EAAE3E,KAAK,CAAC4E,qBAAqB;QACpC,GAAG,EAAE;UACH1B,SAAS,EAAE,MAAM;UACjBoB,MAAM,EAAE;QACV;MACF,CAAC;MACD;MACA,CAAC,IAAIrE,YAAY,MAAM,GAAG;QACxB4E,SAAS,EAAE;MACb;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD;AACA,OAAO,MAAMC,qBAAqB,GAAG9E,KAAK,IAAI;EAC5C,MAAM;IACJ6B,QAAQ;IACRZ,UAAU;IACV8D,aAAa;IACbC;EACF,CAAC,GAAGhF,KAAK;EACT,MAAMgB,MAAM,GAAGa,QAAQ,GAAGZ,UAAU;EACpC,MAAMgE,QAAQ,GAAGF,aAAa,GAAG,CAAC;EAClC,MAAMG,OAAO,GAAG,CAAC,CAAC,CAAC;EACnB,MAAMjD,UAAU,GAAGjB,MAAM,GAAGkE,OAAO,GAAG,CAAC;EACvC,MAAM3E,YAAY,GAAG0E,QAAQ,GAAGC,OAAO,GAAG,CAAC;EAC3C,OAAO;IACL5B,WAAW,EAAEtC,MAAM;IACnBd,aAAa,EAAE+E,QAAQ;IACvBlB,aAAa,EAAE9B,UAAU,GAAG,CAAC,GAAGiD,OAAO,GAAG,CAAC;IAC3C9E,eAAe,EAAEG,YAAY,GAAG,CAAC,GAAG2E,OAAO,GAAG,CAAC;IAC/C/E,YAAY,EAAE+E,OAAO;IACrB;IACAzC,QAAQ,EAAEuC,UAAU;IACpB/C,UAAU;IACV1B,YAAY;IACZmC,YAAY,EAAE,eAAe,IAAIhD,SAAS,CAAC,SAAS,CAAC,CAACyF,IAAI,CAAC,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;IAC/E7B,cAAc,EAAEtB,UAAU,GAAG,CAAC;IAC9BuB,cAAc,EAAEvB,UAAU,GAAGiD,OAAO,GAAGA,OAAO,GAAG,CAAC;IAClD7E,gBAAgB,EAAEE,YAAY,GAAG,CAAC;IAClCD,gBAAgB,EAAEC,YAAY,GAAG2E,OAAO,GAAGA,OAAO,GAAG;EACvD,CAAC;AACH,CAAC;AACD,eAAerF,aAAa,CAAC,QAAQ,EAAEG,KAAK,IAAI;EAC9C,MAAMqF,WAAW,GAAGvF,UAAU,CAACE,KAAK,EAAE;IACpC6C,cAAc,EAAE7C,KAAK,CAACuE,iBAAiB;IACvChC,WAAW,EAAEvC,KAAK,CAACsF,YAAY;IAC/BV,qBAAqB,EAAE5E,KAAK,CAACuF,cAAc;IAC3C5D,qBAAqB,EAAE3B,KAAK,CAACQ,IAAI,CAACR,KAAK,CAACwF,YAAY,CAAC,CAAC5E,GAAG,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC;IACvEwB,sBAAsB,EAAE,iBAAiBrC,KAAK,CAACuF,cAAc,GAAG;IAChEnC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;EACF,OAAO,CAACU,cAAc,CAACuB,WAAW,CAAC;EACnC;EACAhC,mBAAmB,CAACgC,WAAW,CAAC;EAChC;EACA7C,oBAAoB,CAAC6C,WAAW,CAAC;EACjC;EACArD,qBAAqB,CAACqD,WAAW,CAAC;EAClC;EACAtF,mBAAmB,CAACsF,WAAW,CAAC,CAAC;AACnC,CAAC,EAAEP,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}