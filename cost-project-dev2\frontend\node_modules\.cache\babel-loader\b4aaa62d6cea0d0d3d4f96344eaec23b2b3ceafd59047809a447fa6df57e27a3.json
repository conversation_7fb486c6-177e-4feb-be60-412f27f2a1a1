{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport BlockOutlinedSvg from \"@ant-design/icons-svg/es/asn/BlockOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BlockOutlined = function BlockOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BlockOutlinedSvg\n  }));\n};\n\n/**![block](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NiAzNzZINjQ4VjE2OGMwLTguOC03LjItMTYtMTYtMTZIMTY4Yy04LjggMC0xNiA3LjItMTYgMTZ2NDY0YzAgOC44IDcuMiAxNiAxNiAxNmgyMDh2MjA4YzAgOC44IDcuMiAxNiAxNiAxNmg0NjRjOC44IDAgMTYtNy4yIDE2LTE2VjM5MmMwLTguOC03LjItMTYtMTYtMTZ6bS00ODAgMTZ2MTg4SDIyMFYyMjBoMzYwdjE1NkgzOTJjLTguOCAwLTE2IDcuMi0xNiAxNnptMjA0IDUydjEzNkg0NDRWNDQ0aDEzNnptMjI0IDM2MEg0NDRWNjQ4aDE4OGM4LjggMCAxNi03LjIgMTYtMTZWNDQ0aDE1NnYzNjB6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BlockOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BlockOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "BlockOutlinedSvg", "AntdIcon", "BlockOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/BlockOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport BlockOutlinedSvg from \"@ant-design/icons-svg/es/asn/BlockOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BlockOutlined = function BlockOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BlockOutlinedSvg\n  }));\n};\n\n/**![block](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NiAzNzZINjQ4VjE2OGMwLTguOC03LjItMTYtMTYtMTZIMTY4Yy04LjggMC0xNiA3LjItMTYgMTZ2NDY0YzAgOC44IDcuMiAxNiAxNiAxNmgyMDh2MjA4YzAgOC44IDcuMiAxNiAxNiAxNmg0NjRjOC44IDAgMTYtNy4yIDE2LTE2VjM5MmMwLTguOC03LjItMTYtMTYtMTZ6bS00ODAgMTZ2MTg4SDIyMFYyMjBoMzYwdjE1NkgzOTJjLTguOCAwLTE2IDcuMi0xNiAxNnptMjA0IDUydjEzNkg0NDRWNDQ0aDEzNnptMjI0IDM2MEg0NDRWNjQ4aDE4OGM4LjggMCAxNi03LjIgMTYtMTZWNDQ0aDE1NnYzNjB6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BlockOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BlockOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,4CAA4C;AACzE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,aAAa,CAAC;AAC1D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,eAAe;AACvC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}