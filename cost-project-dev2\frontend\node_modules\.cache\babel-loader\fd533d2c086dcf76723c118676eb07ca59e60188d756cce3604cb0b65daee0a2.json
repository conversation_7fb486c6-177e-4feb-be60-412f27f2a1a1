{"ast": null, "code": "import { Threshold as ThresholdScale } from '@antv/scale';\nexport const Threshold = options => {\n  return new ThresholdScale(options);\n};\nThreshold.props = {};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "ThresholdScale", "options", "props"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\g2\\src\\scale\\threshold.ts"], "sourcesContent": ["import { Threshold as ThresholdScale } from '@antv/scale';\nimport { ScaleComponent as SC } from '../runtime';\nimport { ThresholdScale as ThresholdScaleSpec } from '../spec';\n\nexport type ThresholdOptions = Omit<ThresholdScaleSpec, 'type'>;\n\nexport const Threshold: SC<ThresholdOptions> = (options) => {\n  return new ThresholdScale(options);\n};\n\nThreshold.props = {};\n"], "mappings": "AAAA,SAASA,SAAS,IAAIC,cAAc,QAAQ,aAAa;AAMzD,OAAO,MAAMD,SAAS,GAA0BE,OAAO,IAAI;EACzD,OAAO,IAAID,cAAc,CAACC,OAAO,CAAC;AACpC,CAAC;AAEDF,SAAS,CAACG,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}