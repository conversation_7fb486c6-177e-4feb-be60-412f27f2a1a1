# 成本管控模块文档

## 功能概述

成本管控模块提供全面的成本管理功能，包括：

### 1. 清单管理
- 清单的创建、编辑、删除
- 清单导入导出
- 定额关联
- 成本计算
- 数据可视化

### 2. 定额管理
- 定额库维护
- 定额导入导出
- 资源消耗管理
- 成本计算
- 数据分析

### 3. 资源管理
- 资源信息维护
- 价格管理
- 供应商管理
- 库存管理
- 预警系统

### 4. 成本分析
- 成本总览
- 趋势分析
- 构成分析
- 预警信息
- 优化建议

## 组件说明

### BillManagement（清单管理）
```jsx
import BillManagement from '../pages/cost/BillManagement';

// 使用示例
<BillManagement />
```

主要功能：
- 清单列表展示
- 清单创建和编辑
- 清单导入导出
- 定额关联
- 成本计算

### QuotaManagement（定额管理）
```jsx
import QuotaManagement from '../pages/cost/QuotaManagement';

// 使用示例
<QuotaManagement />
```

主要功能：
- 定额列表展示
- 定额创建和编辑
- 定额导入导出
- 资源关联
- 成本分析

### ResourceManagement（资源管理）
```jsx
import ResourceManagement from '../pages/cost/ResourceManagement';

// 使用示例
<ResourceManagement />
```

主要功能：
- 资源列表展示
- 资源创建和编辑
- 价格管理
- 供应商管理
- 库存预警

### CostAnalysis（成本分析）
```jsx
import CostAnalysis from '../pages/cost/CostAnalysis';

// 使用示例
<CostAnalysis />
```

主要功能：
- 成本总览
- 趋势图表
- 构成分析
- 预警信息
- 优化建议

## API 接口

### 清单管理接口

#### 获取清单列表
```http
GET /api/bills
```

参数：
- `keyword`: 搜索关键词
- `page`: 页码
- `pageSize`: 每页数量

响应：
```json
{
  "total": 100,
  "items": [
    {
      "id": 1,
      "code": "123456789012",
      "name": "清单1",
      "chapter": "第一章",
      "description": "描述",
      "unit": "个",
      "quantity": 100,
      "unitPrice": 50,
      "totalPrice": 5000
    }
  ]
}
```

#### 创建清单
```http
POST /api/bills
```

请求体：
```json
{
  "code": "123456789012",
  "name": "清单1",
  "chapter": "第一章",
  "description": "描述",
  "unit": "个",
  "quantity": 100,
  "unitPrice": 50
}
```

### 定额管理接口

#### 获取定额列表
```http
GET /api/quotas
```

参数：
- `keyword`: 搜索关键词
- `page`: 页码
- `pageSize`: 每页数量

#### 创建定额
```http
POST /api/quotas
```

请求体：
```json
{
  "code": "Q001",
  "name": "定额1",
  "unit": "工日",
  "laborCost": 300,
  "materialCost": 500,
  "machineCost": 200
}
```

### 资源管理接口

#### 获取资源列表
```http
GET /api/resources
```

#### 创建资源
```http
POST /api/resources
```

#### 更新价格
```http
PUT /api/resources/{id}/price
```

### 成本分析接口

#### 获取成本总览
```http
GET /api/cost-analysis/summary
```

#### 获取成本趋势
```http
GET /api/cost-analysis/trend
```

## 开发指南

### 项目设置

1. 安装依赖
```bash
npm install
```

2. 启动开发服务器
```bash
npm start
```

3. 运行测试
```bash
npm test
```

4. 构建生产版本
```bash
npm run build
```

### 开发规范

1. 代码风格
- 使用ESLint进行代码检查
- 遵循项目的.eslintrc配置
- 使用Prettier进行代码格式化

2. 组件开发
- 使用函数组件和Hooks
- 遵循组件分层原则
- 实现必要的错误处理
- 添加适当的加载状态
- 实现数据验证

3. 状态管理
- 使用Context进行状态管理
- 遵循单向数据流
- 实现必要的缓存机制

4. 错误处理
- 使用错误边界捕获渲染错误
- 实现全局错误处理
- 添加错误日志记录
- 提供用户友好的错误提示

5. 测试
- 编写单元测试
- 实现集成测试
- 保持测试覆盖率
- 模拟API请求

### 部署说明

1. 环境配置
- 配置环境变量
- 设置API地址
- 配置错误监控

2. 构建部署
- 执行生产构建
- 压缩静态资源
- 配置CDN
- 设置缓存策略

3. 监控维护
- 配置错误监控
- 设置性能监控
- 实现日志收集
- 配置告警机制

## 常见问题

1. 数据加载失败
- 检查网络连接
- 验证API地址
- 查看错误日志
- 检查权限设置

2. 性能问题
- 优化数据加载
- 实现分页加载
- 添加必要缓存
- 优化组件渲染

3. 兼容性问题
- 检查浏览器支持
- 添加polyfill
- 测试不同设备
- 处理特殊场景

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础功能
- 添加数据可视化
- 完善错误处理

### v1.1.0 (计划中)
- 优化性能
- 添加新功能
- 改进用户体验
- 增强数据分析
