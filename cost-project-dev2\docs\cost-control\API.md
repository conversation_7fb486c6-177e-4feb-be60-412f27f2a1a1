# 成本管控模块 API 文档

## 接口规范

### 基础信息
- 基础URL: `/api`
- 请求方式: REST
- 数据格式: JSON
- 字符编码: UTF-8

### 认证方式
所有接口需要在请求头中携带认证信息：
```http
Authorization: Bearer <token>
```

### 通用响应格式
```json
{
  "code": 200,          // 状态码
  "message": "success", // 提示信息
  "data": {            // 响应数据
    // 具体数据
  }
}
```

### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 无权限 |
| 404 | 资源不存在 |
| 500 | 服务器错误 |

## 详细接口文档

### 清单管理

#### 1. 获取清单列表
```http
GET /api/bills
```

请求参数：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | string | 否 | 搜索关键词 |
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |
| chapter | string | 否 | 章节筛选 |

响应示例：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "items": [
      {
        "id": 1,
        "code": "123456789012",
        "name": "清单1",
        "chapter": "第一章",
        "description": "描述",
        "unit": "个",
        "quantity": 100,
        "unitPrice": 50,
        "totalPrice": 5000,
        "quotaItems": []
      }
    ]
  }
}
```

#### 2. 创建清单
```http
POST /api/bills
```

请求体：
```json
{
  "code": "123456789012",
  "name": "清单1",
  "chapter": "第一章",
  "description": "描述",
  "unit": "个",
  "quantity": 100,
  "unitPrice": 50
}
```

#### 3. 更新清单
```http
PUT /api/bills/{id}
```

#### 4. 删除清单
```http
DELETE /api/bills/{id}
```

#### 5. 导入清单
```http
POST /api/bills/import/excel
```

请求体：
- Content-Type: multipart/form-data
- 参数名：file

#### 6. 导出清单
```http
GET /api/bills/export
```

### 定额管理

#### 1. 获取定额列表
```http
GET /api/quotas
```

请求参数：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | string | 否 | 搜索关键词 |
| page | number | 否 | 页码 |
| pageSize | number | 否 | 每页数量 |

#### 2. 创建定额
```http
POST /api/quotas
```

请求体：
```json
{
  "code": "Q001",
  "name": "定额1",
  "unit": "工日",
  "laborCost": 300,
  "materialCost": 500,
  "machineCost": 200,
  "description": "描述"
}
```

#### 3. 更新定额
```http
PUT /api/quotas/{id}
```

#### 4. 删除定额
```http
DELETE /api/quotas/{id}
```

### 资源管理

#### 1. 获取资源列表
```http
GET /api/resources
```

请求参数：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | string | 否 | 搜索关键词 |
| type | string | 否 | 资源类型 |
| page | number | 否 | 页码 |
| pageSize | number | 否 | 每页数量 |

响应示例：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "items": [
      {
        "id": 1,
        "code": "R001",
        "name": "资源1",
        "type": "LABOR",
        "specification": "规格",
        "unit": "工日",
        "currentPrice": 300,
        "remainingQuantity": 1000,
        "totalQuantity": 2000,
        "usedQuantity": 1000,
        "suppliers": []
      }
    ]
  }
}
```

#### 2. 创建资源
```http
POST /api/resources
```

#### 3. 更新资源
```http
PUT /api/resources/{id}
```

#### 4. 删除资源
```http
DELETE /api/resources/{id}
```

#### 5. 更新价格
```http
PUT /api/resources/{id}/price
```

请求体：
```json
{
  "price": 350,
  "effectiveDate": "2024-01-01",
  "reason": "市场价格调整"
}
```

#### 6. 管理供应商
```http
POST /api/resources/{id}/suppliers
```

### 成本分析

#### 1. 获取成本总览
```http
GET /api/cost-analysis/summary
```

响应示例：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalCost": 1000000,
    "laborCost": 300000,
    "materialCost": 500000,
    "machineCost": 200000,
    "costTrend": -5,
    "laborCostPercentage": 30,
    "materialCostPercentage": 50,
    "machineCostPercentage": 20
  }
}
```

#### 2. 获取成本趋势
```http
GET /api/cost-analysis/trend
```

请求参数：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| interval | string | 否 | 时间间隔(day/week/month) |
| startDate | string | 是 | 开始日期 |
| endDate | string | 是 | 结束日期 |

#### 3. 获取资源消耗
```http
GET /api/cost-analysis/resource-consumption
```

#### 4. 获取成本预警
```http
GET /api/cost-analysis/alerts
```

#### 5. 获取优化建议
```http
GET /api/cost-analysis/optimization
```

## 数据结构

### 清单(Bill)
```typescript
interface Bill {
  id: number;
  code: string;        // 12位编码
  name: string;        // 名称
  chapter: string;     // 章节
  description: string; // 描述
  unit: string;       // 单位
  quantity: number;    // 工程量
  unitPrice: number;   // 单价
  totalPrice: number;  // 合价
  quotaItems: Quota[]; // 关联定额
}
```

### 定额(Quota)
```typescript
interface Quota {
  id: number;
  code: string;        // 编号
  name: string;        // 名称
  unit: string;        // 单位
  laborCost: number;   // 人工费
  materialCost: number;// 材料费
  machineCost: number; // 机械费
  unitPrice: number;   // 综合单价
  description: string; // 描述
}
```

### 资源(Resource)
```typescript
interface Resource {
  id: number;
  code: string;           // 编号
  name: string;           // 名称
  type: ResourceType;     // 类型
  specification: string;  // 规格
  unit: string;          // 单位
  currentPrice: number;   // 当前单价
  remainingQuantity: number; // 剩余数量
  totalQuantity: number;    // 总数量
  usedQuantity: number;     // 已用数量
  suppliers: Supplier[];    // 供应商
}

enum ResourceType {
  LABOR = 'LABOR',       // 人工
  MATERIAL = 'MATERIAL', // 材料
  MACHINE = 'MACHINE'    // 机械
}
```

## 错误处理

### 错误响应格式
```json
{
  "code": 400,
  "message": "错误信息",
  "details": {
    "field": "错误字段",
    "reason": "具体原因"
  }
}
```

### 业务错误码
| 错误码 | 说明 |
|--------|------|
| 1001 | 清单编码重复 |
| 1002 | 定额编号重复 |
| 1003 | 资源编号重复 |
| 1004 | 库存不足 |
| 1005 | 价格无效 |

## 注意事项

1. 接口访问限制
- 限流：每个IP每分钟最多100次请求
- 超时：30秒

2. 数据验证
- 清单编码必须为12位数字
- 金额必须大于等于0
- 数量必须大于0

3. 文件上传
- 支持格式：xlsx, xls
- 大小限制：10MB

4. 安全性
- 所有接口必须通过认证
- 敏感数据加密传输
- 定期更新token

## 更新记录

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础接口
- 添加数据验证
- 完善错误处理

### v1.1.0 (计划中)
- 优化接口性能
- 添加新功能
- 增强安全性
- 改进数据结构
