{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport IdcardFilledSvg from \"@ant-design/icons-svg/es/asn/IdcardFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar IdcardFilled = function IdcardFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: IdcardFilledSvg\n  }));\n};\n\n/**![idcard](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM3MyA0MTFjLTI4LjUgMC01MS43IDIzLjMtNTEuNyA1MnMyMy4yIDUyIDUxLjcgNTIgNTEuNy0yMy4zIDUxLjctNTItMjMuMi01Mi01MS43LTUyem01NTUtMjUxSDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NDBjMCAxNy43IDE0LjMgMzIgMzIgMzJoODMyYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE5MmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjA4IDQyMGMwLTQuNCAxLTggMi4zLThoMTIzLjRjMS4zIDAgMi4zIDMuNiAyLjMgOHY0OGMwIDQuNC0xIDgtMi4zIDhINjEwLjNjLTEuMyAwLTIuMy0zLjYtMi4zLTh2LTQ4em0tODYgMjUzaC00My45Yy00LjIgMC03LjYtMy4zLTcuOS03LjUtMy44LTUwLjUtNDYtOTAuNS05Ny4yLTkwLjVzLTkzLjQgNDAtOTcuMiA5MC41Yy0uMyA0LjItMy43IDcuNS03LjkgNy41SDIyNGE4IDggMCAwMS04LTguNGMyLjgtNTMuMyAzMi05OS43IDc0LjYtMTI2LjFhMTExLjggMTExLjggMCAwMS0yOS4xLTc1LjVjMC02MS45IDQ5LjktMTEyIDExMS40LTExMnMxMTEuNCA1MC4xIDExMS40IDExMmMwIDI5LjEtMTEgNTUuNS0yOS4xIDc1LjUgNDIuNyAyNi41IDcxLjggNzIuOCA3NC42IDEyNi4xLjQgNC42LTMuMiA4LjQtNy44IDguNHptMjc4LjktNTNINjE1LjFjLTMuOSAwLTcuMS0zLjYtNy4xLTh2LTQ4YzAtNC40IDMuMi04IDcuMS04aDE4NS43YzMuOSAwIDcuMSAzLjYgNy4xIDh2NDhoLjFjMCA0LjQtMy4yIDgtNy4xIDh6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(IdcardFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'IdcardFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "IdcardFilledSvg", "AntdIcon", "IdcardFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/IdcardFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport IdcardFilledSvg from \"@ant-design/icons-svg/es/asn/IdcardFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar IdcardFilled = function IdcardFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: IdcardFilledSvg\n  }));\n};\n\n/**![idcard](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM3MyA0MTFjLTI4LjUgMC01MS43IDIzLjMtNTEuNyA1MnMyMy4yIDUyIDUxLjcgNTIgNTEuNy0yMy4zIDUxLjctNTItMjMuMi01Mi01MS43LTUyem01NTUtMjUxSDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NDBjMCAxNy43IDE0LjMgMzIgMzIgMzJoODMyYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE5MmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjA4IDQyMGMwLTQuNCAxLTggMi4zLThoMTIzLjRjMS4zIDAgMi4zIDMuNiAyLjMgOHY0OGMwIDQuNC0xIDgtMi4zIDhINjEwLjNjLTEuMyAwLTIuMy0zLjYtMi4zLTh2LTQ4em0tODYgMjUzaC00My45Yy00LjIgMC03LjYtMy4zLTcuOS03LjUtMy44LTUwLjUtNDYtOTAuNS05Ny4yLTkwLjVzLTkzLjQgNDAtOTcuMiA5MC41Yy0uMyA0LjItMy43IDcuNS03LjkgNy41SDIyNGE4IDggMCAwMS04LTguNGMyLjgtNTMuMyAzMi05OS43IDc0LjYtMTI2LjFhMTExLjggMTExLjggMCAwMS0yOS4xLTc1LjVjMC02MS45IDQ5LjktMTEyIDExMS40LTExMnMxMTEuNCA1MC4xIDExMS40IDExMmMwIDI5LjEtMTEgNTUuNS0yOS4xIDc1LjUgNDIuNyAyNi41IDcxLjggNzIuOCA3NC42IDEyNi4xLjQgNC42LTMuMiA4LjQtNy44IDguNHptMjc4LjktNTNINjE1LjFjLTMuOSAwLTcuMS0zLjYtNy4xLTh2LTQ4YzAtNC40IDMuMi04IDcuMS04aDE4NS43YzMuOSAwIDcuMSAzLjYgNy4xIDh2NDhoLjFjMCA0LjQtMy4yIDgtNy4xIDh6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(IdcardFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'IdcardFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,YAAY,CAAC;AACzD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,cAAc;AACtC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}