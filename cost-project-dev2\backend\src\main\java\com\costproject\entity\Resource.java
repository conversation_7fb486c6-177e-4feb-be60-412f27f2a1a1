package com.costproject.entity;

import com.costproject.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Table(name = "resources")
@EqualsAndHashCode(callSuper = true)
public class Resource extends BaseEntity {

    @Column(length = 50, nullable = false, unique = true)
    private String code; // 资源编号

    @Column(nullable = false)
    private String name; // 资源名称

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ResourceType type; // 资源类型（人工/材料/机械）

    @Column(length = 20)
    private String unit; // 计量单位

    @Column(precision = 18, scale = 4)
    private BigDecimal currentPrice; // 当前单价

    @Column(columnDefinition = "TEXT")
    private String specification; // 规格型号

    // 成本拆分（JSON格式存储）
    @Column(columnDefinition = "TEXT")
    private String costBreakdown;

    // 价格历史记录
    @OneToMany(mappedBy = "resource", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ResourcePrice> priceHistory = new ArrayList<>();

    // 供应商信息
    @ManyToMany
    @JoinTable(
        name = "resource_suppliers",
        joinColumns = @JoinColumn(name = "resource_id"),
        inverseJoinColumns = @JoinColumn(name = "supplier_id")
    )
    private List<Supplier> suppliers = new ArrayList<>();

    // 使用统计
    @Column(precision = 18, scale = 4)
    private BigDecimal totalQuantity; // 总需求量

    @Column(precision = 18, scale = 4)
    private BigDecimal usedQuantity; // 已使用量

    @Column(precision = 18, scale = 4)
    private BigDecimal remainingQuantity; // 剩余量

    // 资源类型枚举
    public enum ResourceType {
        LABOR("人工"),
        MATERIAL("材料"),
        MACHINE("机械");

        private final String description;

        ResourceType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 业务方法

    /**
     * 添加价格记录
     */
    public void addPriceRecord(BigDecimal price, LocalDateTime effectiveDate) {
        ResourcePrice priceRecord = new ResourcePrice();
        priceRecord.setResource(this);
        priceRecord.setPrice(price);
        priceRecord.setEffectiveDate(effectiveDate);
        this.priceHistory.add(priceRecord);
        this.currentPrice = price; // 更新当前价格
    }
    
    // Getter methods that were missing
    public List<ResourcePrice> getPriceHistory() {
        return priceHistory;
    }

    /**
     * 更新成本拆分
     */
    public void updateCostBreakdown(CostBreakdown breakdown) {
        // TODO: 将成本拆分对象转换为JSON存储
    }

    /**
     * 添加供应商
     */
    public void addSupplier(Supplier supplier) {
        if (!suppliers.contains(supplier)) {
            suppliers.add(supplier);
        }
    }

    /**
     * 更新使用量
     */
    public void updateUsage(BigDecimal quantity) {
        this.usedQuantity = this.usedQuantity.add(quantity);
        this.remainingQuantity = this.totalQuantity.subtract(this.usedQuantity);
    }

    /**
     * 检查库存是否充足
     */
    public boolean hasEnoughStock(BigDecimal requiredQuantity) {
        return this.remainingQuantity.compareTo(requiredQuantity) >= 0;
    }

    // 内部类：成本拆分结构
    @Data
    public static class CostBreakdown {
        // 材料成本拆分
        private BigDecimal originalPrice; // 原价
        private BigDecimal transportFee; // 运输费
        private BigDecimal loadingFee; // 装卸费
        private BigDecimal storageFee; // 仓储费
        private BigDecimal lossFee; // 损耗费
        private BigDecimal testingFee; // 检验费

        // 人工成本拆分
        private BigDecimal basicSalary; // 基本工资
        private BigDecimal allowance; // 补贴
        private BigDecimal welfare; // 福利
        private BigDecimal insurance; // 保险
        private BigDecimal trainingFee; // 培训费

        // 机械成本拆分
        private BigDecimal rentFee; // 租赁费
        private BigDecimal operatorFee; // 操作工费
        private BigDecimal fuelFee; // 燃料费
        private BigDecimal maintenanceFee; // 维护费
        private BigDecimal insuranceFee; // 保险费
        
        // Getters and Setters
        public BigDecimal getOriginalPrice() {
            return originalPrice;
        }

        public void setOriginalPrice(BigDecimal originalPrice) {
            this.originalPrice = originalPrice;
        }

        public BigDecimal getTransportFee() {
            return transportFee;
        }

        public void setTransportFee(BigDecimal transportFee) {
            this.transportFee = transportFee;
        }

        public BigDecimal getLoadingFee() {
            return loadingFee;
        }

        public void setLoadingFee(BigDecimal loadingFee) {
            this.loadingFee = loadingFee;
        }

        public BigDecimal getStorageFee() {
            return storageFee;
        }

        public void setStorageFee(BigDecimal storageFee) {
            this.storageFee = storageFee;
        }

        public BigDecimal getLossFee() {
            return lossFee;
        }

        public void setLossFee(BigDecimal lossFee) {
            this.lossFee = lossFee;
        }

        public BigDecimal getTestingFee() {
            return testingFee;
        }

        public void setTestingFee(BigDecimal testingFee) {
            this.testingFee = testingFee;
        }

        public BigDecimal getBasicSalary() {
            return basicSalary;
        }

        public void setBasicSalary(BigDecimal basicSalary) {
            this.basicSalary = basicSalary;
        }

        public BigDecimal getAllowance() {
            return allowance;
        }

        public void setAllowance(BigDecimal allowance) {
            this.allowance = allowance;
        }

        public BigDecimal getWelfare() {
            return welfare;
        }

        public void setWelfare(BigDecimal welfare) {
            this.welfare = welfare;
        }

        public BigDecimal getInsurance() {
            return insurance;
        }

        public void setInsurance(BigDecimal insurance) {
            this.insurance = insurance;
        }

        public BigDecimal getTrainingFee() {
            return trainingFee;
        }

        public void setTrainingFee(BigDecimal trainingFee) {
            this.trainingFee = trainingFee;
        }

        public BigDecimal getRentFee() {
            return rentFee;
        }

        public void setRentFee(BigDecimal rentFee) {
            this.rentFee = rentFee;
        }

        public BigDecimal getOperatorFee() {
            return operatorFee;
        }

        public void setOperatorFee(BigDecimal operatorFee) {
            this.operatorFee = operatorFee;
        }

        public BigDecimal getFuelFee() {
            return fuelFee;
        }

        public void setFuelFee(BigDecimal fuelFee) {
            this.fuelFee = fuelFee;
        }

        public BigDecimal getMaintenanceFee() {
            return maintenanceFee;
        }

        public void setMaintenanceFee(BigDecimal maintenanceFee) {
            this.maintenanceFee = maintenanceFee;
        }

        public BigDecimal getInsuranceFee() {
            return insuranceFee;
        }

        public void setInsuranceFee(BigDecimal insuranceFee) {
            this.insuranceFee = insuranceFee;
        }
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ResourceType getType() {
        return type;
    }

    public void setType(ResourceType type) {
        this.type = type;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getCurrentPrice() {
        return currentPrice;
    }

    public void setCurrentPrice(BigDecimal currentPrice) {
        this.currentPrice = currentPrice;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getCostBreakdown() {
        return costBreakdown;
    }

    public void setCostBreakdown(String costBreakdown) {
        this.costBreakdown = costBreakdown;
    }

    public void setPriceHistory(List<ResourcePrice> priceHistory) {
        this.priceHistory = priceHistory;
    }

    public List<Supplier> getSuppliers() {
        return suppliers;
    }

    public void setSuppliers(List<Supplier> suppliers) {
        this.suppliers = suppliers;
    }

    public BigDecimal getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(BigDecimal totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public BigDecimal getUsedQuantity() {
        return usedQuantity;
    }

    public void setUsedQuantity(BigDecimal usedQuantity) {
        this.usedQuantity = usedQuantity;
    }

    public BigDecimal getRemainingQuantity() {
        return remainingQuantity;
    }

    public void setRemainingQuantity(BigDecimal remainingQuantity) {
        this.remainingQuantity = remainingQuantity;
    }
}
