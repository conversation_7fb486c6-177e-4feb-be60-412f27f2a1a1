{"ast": null, "code": "/**\n * Copyright 2016, <PERSON>\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *\n * * Redistributions of source code must retain the above copyright notice, this\n *   list of conditions and the following disclaimer.\n *\n * * Redistributions in binary form must reproduce the above copyright notice,\n *   this list of conditions and the following disclaimer in the documentation\n *   and/or other materials provided with the distribution.\n *\n * * Neither the name of the author nor the names of contributors may be used to\n *   endorse or promote products derived from this software without specific prior\n *   written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR\n * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON><PERSON>QUENTIAL DAMAGES\n * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\n * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\nexport { bisect } from './bisect';\nexport { nelderMead } from './nelderMead';\nexport { conjugateGradient, conjugateGradientSolve } from './conjugateGradient';\nexport { gradientDescent, gradientDescentLineSearch } from './gradientDescent';\nexport { zeros, zerosM, norm2, weightedSum, scale } from './blas1';", "map": {"version": 3, "names": ["bisect", "nelderMead", "conjugateGradient", "conjugateGradientSolve", "gradientDescent", "gradientDescentLineSearch", "zeros", "zerosM", "norm2", "weightedSum", "scale"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\g2\\src\\data\\utils\\venn\\fmin\\index.ts"], "sourcesContent": ["/**\n * Copyright 2016, <PERSON>\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *\n * * Redistributions of source code must retain the above copyright notice, this\n *   list of conditions and the following disclaimer.\n *\n * * Redistributions in binary form must reproduce the above copyright notice,\n *   this list of conditions and the following disclaimer in the documentation\n *   and/or other materials provided with the distribution.\n *\n * * Neither the name of the author nor the names of contributors may be used to\n *   endorse or promote products derived from this software without specific prior\n *   written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR\n * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON><PERSON>QUENTIAL DAMAGES\n * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\n * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\nexport { bisect } from './bisect';\nexport { nelderMead } from './nelderMead';\nexport { conjugateGradient, conjugateGradientSolve } from './conjugateGradient';\nexport { gradientDescent, gradientDescentLineSearch } from './gradientDescent';\nexport { zeros, zerosM, norm2, weightedSum, scale } from './blas1';\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,SAASA,MAAM,QAAQ,UAAU;AACjC,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,iBAAiB,EAAEC,sBAAsB,QAAQ,qBAAqB;AAC/E,SAASC,eAAe,EAAEC,yBAAyB,QAAQ,mBAAmB;AAC9E,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,KAAK,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}