{"ast": null, "code": "import isArray from './is-array';\n/**\n * Flattens `array` a single level deep.\n *\n * @param {Array} arr The array to flatten.\n * @return {Array} Returns the new flattened array.\n * @example\n *\n * flatten([1, [2, [3, [4]], 5]]);  // => [1, 2, [3, [4]], 5]\n */\nvar flatten = function (arr) {\n  if (!isArray(arr)) {\n    return [];\n  }\n  var rst = [];\n  for (var i = 0; i < arr.length; i++) {\n    rst = rst.concat(arr[i]);\n  }\n  return rst;\n};\nexport default flatten;", "map": {"version": 3, "names": ["isArray", "flatten", "arr", "rst", "i", "length", "concat"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\algorithm\\node_modules\\@antv\\util\\src\\flatten.ts"], "sourcesContent": ["import isArray from './is-array';\n\n/**\n * Flattens `array` a single level deep.\n *\n * @param {Array} arr The array to flatten.\n * @return {Array} Returns the new flattened array.\n * @example\n *\n * flatten([1, [2, [3, [4]], 5]]);  // => [1, 2, [3, [4]], 5]\n */\nconst flatten = function <T>(arr: T[]): T[] {\n  if (!isArray(arr)) {\n    return [];\n  }\n  let rst: T[] = [];\n  for (let i = 0; i < arr.length; i++) {\n    rst = rst.concat(arr[i]);\n  }\n  return rst;\n};\n\nexport default flatten;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,YAAY;AAEhC;;;;;;;;;AASA,IAAMC,OAAO,GAAG,SAAAA,CAAaC,GAAQ;EACnC,IAAI,CAACF,OAAO,CAACE,GAAG,CAAC,EAAE;IACjB,OAAO,EAAE;;EAEX,IAAIC,GAAG,GAAQ,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCD,GAAG,GAAGA,GAAG,CAACG,MAAM,CAACJ,GAAG,CAACE,CAAC,CAAC,CAAC;;EAE1B,OAAOD,GAAG;AACZ,CAAC;AAED,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}