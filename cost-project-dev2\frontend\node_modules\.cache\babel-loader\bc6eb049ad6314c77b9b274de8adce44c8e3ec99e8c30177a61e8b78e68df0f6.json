{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FolderOpenTwoToneSvg from \"@ant-design/icons-svg/es/asn/FolderOpenTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FolderOpenTwoTone = function FolderOpenTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FolderOpenTwoToneSvg\n  }));\n};\n\n/**![folder-open](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE1OSA3NjhoNjEyLjNsMTAzLjQtMjU2SDI2Mi4zeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNOTI4IDQ0NEg4MjBWMzMwLjRjMC0xNy43LTE0LjMtMzItMzItMzJINDczTDM1NS43IDE4Ni4yYTguMTUgOC4xNSAwIDAwLTUuNS0yLjJIOTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjU5MmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2OThjMTMgMCAyNC44LTcuOSAyOS43LTIwbDEzNC0zMzJjMS41LTMuOCAyLjMtNy45IDIuMy0xMiAwLTE3LjctMTQuMy0zMi0zMi0zMnpNMTM2IDI1NmgxODguNWwxMTkuNiAxMTQuNEg3NDhWNDQ0SDIzOGMtMTMgMC0yNC44IDcuOS0yOS43IDIwTDEzNiA2NDMuMlYyNTZ6bTYzNS4zIDUxMkgxNTlsMTAzLjMtMjU2aDYxMi40TDc3MS4zIDc2OHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FolderOpenTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FolderOpenTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "FolderOpenTwoToneSvg", "AntdIcon", "FolderOpenTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/FolderOpenTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FolderOpenTwoToneSvg from \"@ant-design/icons-svg/es/asn/FolderOpenTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FolderOpenTwoTone = function FolderOpenTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FolderOpenTwoToneSvg\n  }));\n};\n\n/**![folder-open](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE1OSA3NjhoNjEyLjNsMTAzLjQtMjU2SDI2Mi4zeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNOTI4IDQ0NEg4MjBWMzMwLjRjMC0xNy43LTE0LjMtMzItMzItMzJINDczTDM1NS43IDE4Ni4yYTguMTUgOC4xNSAwIDAwLTUuNS0yLjJIOTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjU5MmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2OThjMTMgMCAyNC44LTcuOSAyOS43LTIwbDEzNC0zMzJjMS41LTMuOCAyLjMtNy45IDIuMy0xMiAwLTE3LjctMTQuMy0zMi0zMi0zMnpNMTM2IDI1NmgxODguNWwxMTkuNiAxMTQuNEg3NDhWNDQ0SDIzOGMtMTMgMC0yNC44IDcuOS0yOS43IDIwTDEzNiA2NDMuMlYyNTZ6bTYzNS4zIDUxMkgxNTlsMTAzLjMtMjU2aDYxMi40TDc3MS4zIDc2OHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FolderOpenTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FolderOpenTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,iBAAiB,CAAC;AAC9D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,mBAAmB;AAC3C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}