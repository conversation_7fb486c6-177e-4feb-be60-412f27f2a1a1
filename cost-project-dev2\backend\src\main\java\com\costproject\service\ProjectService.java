package com.costproject.service;

import com.costproject.entity.Project;
import com.costproject.repository.ProjectRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Service
public class ProjectService {

    private static final Logger log = LoggerFactory.getLogger(ProjectService.class);

    private final ProjectRepository projectRepository;

    public ProjectService(ProjectRepository projectRepository) {
        this.projectRepository = projectRepository;
    }

    /**
     * 创建项目
     */
    @Transactional
    public Project createProject(Project project) {
        validateProject(project);
        return projectRepository.save(project);
    }

    /**
     * 更新项目
     */
    @Transactional
    public Project updateProject(Long id, Project project) {
        Project existing = getProject(id);
        updateProjectFields(existing, project);
        return projectRepository.save(existing);
    }

    /**
     * 删除项目
     */
    @Transactional
    public void deleteProject(Long id) {
        Project project = getProject(id);
        projectRepository.delete(project);
    }

    /**
     * 获取项目详情
     */
    public Project getProject(Long id) {
        return projectRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("项目不存在: " + id));
    }

    /**
     * 获取项目列表（分页）
     */
    public Page<Project> getProjects(Pageable pageable, String keyword) {
        if (keyword != null && !keyword.trim().isEmpty()) {
            return projectRepository.findByNameContainingOrDescriptionContaining(
                keyword, keyword, pageable);
        }
        return projectRepository.findAll(pageable);
    }

    /**
     * 获取项目统计信息
     */
    public Map<String, Object> getProjectStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        long totalProjects = projectRepository.count();
        statistics.put("total", totalProjects);
        
        // 按状态统计
        statistics.put("planning", projectRepository.countByStatus("PLANNING"));
        statistics.put("inProgress", projectRepository.countByStatus("IN_PROGRESS"));
        statistics.put("completed", projectRepository.countByStatus("COMPLETED"));
        statistics.put("onHold", projectRepository.countByStatus("ON_HOLD"));
        statistics.put("cancelled", projectRepository.countByStatus("CANCELLED"));
        
        return statistics;
    }

    // 私有辅助方法

    private void validateProject(Project project) {
        if (project.getName() == null || project.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("项目名称不能为空");
        }
        if (project.getStartDate() == null) {
            throw new IllegalArgumentException("项目开始日期不能为空");
        }
        // TODO: 添加更多验证规则
    }

    private void updateProjectFields(Project existing, Project updated) {
        if (updated.getName() != null) {
            existing.setName(updated.getName());
        }
        if (updated.getDescription() != null) {
            existing.setDescription(updated.getDescription());
        }
        if (updated.getStartDate() != null) {
            existing.setStartDate(updated.getStartDate());
        }
        if (updated.getEndDate() != null) {
            existing.setEndDate(updated.getEndDate());
        }
        if (updated.getLocation() != null) {
            existing.setLocation(updated.getLocation());
        }
        if (updated.getStatus() != null) {
            existing.setStatus(updated.getStatus());
        }
        if (updated.getDailyWorkHours() != null) {
            existing.setDailyWorkHours(updated.getDailyWorkHours());
        }
        if (updated.getTimeZone() != null) {
            existing.setTimeZone(updated.getTimeZone());
        }
    }
} 