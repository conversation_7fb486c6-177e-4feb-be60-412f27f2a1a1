{"ast": null, "code": "import reduce from './reduce';\nexport default (function (obj, keys) {\n  return reduce(obj, function (r, curr, key) {\n    if (!keys.includes(key)) {\n      r[key] = curr;\n    }\n    return r;\n  }, {});\n});", "map": {"version": 3, "names": ["reduce", "obj", "keys", "r", "curr", "key", "includes"], "sources": ["C:\\Users\\<USER>\\Desktop\\dev\\cost-project-dev2\\frontend\\node_modules\\@antv\\algorithm\\node_modules\\@antv\\util\\src\\omit.ts"], "sourcesContent": ["import reduce from './reduce';\nimport { ObjectType } from './types';\n\nexport default <T>(obj: ObjectType<T>, keys: string[]): ObjectType<T> => {\n  return reduce(\n    obj,\n    (r: ObjectType<T>, curr: T, key: string) => {\n      if (!keys.includes(key)) {\n        r[key] = curr;\n      }\n      return r;\n    },\n    {}\n  );\n}\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,UAAU;AAG7B,gBAAe,UAAIC,GAAkB,EAAEC,IAAc;EACnD,OAAOF,MAAM,CACXC,GAAG,EACH,UAACE,CAAgB,EAAEC,IAAO,EAAEC,GAAW;IACrC,IAAI,CAACH,IAAI,CAACI,QAAQ,CAACD,GAAG,CAAC,EAAE;MACvBF,CAAC,CAACE,GAAG,CAAC,GAAGD,IAAI;;IAEf,OAAOD,CAAC;EACV,CAAC,EACD,EAAE,CACH;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}