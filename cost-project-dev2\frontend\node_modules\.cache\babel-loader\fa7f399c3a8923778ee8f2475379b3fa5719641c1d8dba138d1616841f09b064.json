{"ast": null, "code": "import useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport { devUseWarning } from '../../_util/warning';\nimport { defaultConfig } from '../../theme/internal';\nimport useThemeKey from './useThemeKey';\nexport default function useTheme(theme, parentTheme, config) {\n  var _a, _b;\n  const warning = devUseWarning('ConfigProvider');\n  const themeConfig = theme || {};\n  const parentThemeConfig = themeConfig.inherit === false || !parentTheme ? Object.assign(Object.assign({}, defaultConfig), {\n    hashed: (_a = parentTheme === null || parentTheme === void 0 ? void 0 : parentTheme.hashed) !== null && _a !== void 0 ? _a : defaultConfig.hashed,\n    cssVar: parentTheme === null || parentTheme === void 0 ? void 0 : parentTheme.cssVar\n  }) : parentTheme;\n  const themeKey = useThemeKey();\n  if (process.env.NODE_ENV !== 'production') {\n    const cssVarEnabled = themeConfig.cssVar || parentThemeConfig.cssVar;\n    const validKey = !!(typeof themeConfig.cssVar === 'object' && ((_b = themeConfig.cssVar) === null || _b === void 0 ? void 0 : _b.key) || themeKey);\n    process.env.NODE_ENV !== \"production\" ? warning(!cssVarEnabled || validKey, 'breaking', 'Missing key in `cssVar` config. Please upgrade to React 18 or set `cssVar.key` manually in each ConfigProvider inside `cssVar` enabled ConfigProvider.') : void 0;\n  }\n  return useMemo(() => {\n    var _a, _b;\n    if (!theme) {\n      return parentTheme;\n    }\n    // Override\n    const mergedComponents = Object.assign({}, parentThemeConfig.components);\n    Object.keys(theme.components || {}).forEach(componentName => {\n      mergedComponents[componentName] = Object.assign(Object.assign({}, mergedComponents[componentName]), theme.components[componentName]);\n    });\n    const cssVarKey = `css-var-${themeKey.replace(/:/g, '')}`;\n    const mergedCssVar = ((_a = themeConfig.cssVar) !== null && _a !== void 0 ? _a : parentThemeConfig.cssVar) && Object.assign(Object.assign(Object.assign({\n      prefix: config === null || config === void 0 ? void 0 : config.prefixCls\n    }, typeof parentThemeConfig.cssVar === 'object' ? parentThemeConfig.cssVar : {}), typeof themeConfig.cssVar === 'object' ? themeConfig.cssVar : {}), {\n      key: typeof themeConfig.cssVar === 'object' && ((_b = themeConfig.cssVar) === null || _b === void 0 ? void 0 : _b.key) || cssVarKey\n    });\n    // Base token\n    return Object.assign(Object.assign(Object.assign({}, parentThemeConfig), themeConfig), {\n      token: Object.assign(Object.assign({}, parentThemeConfig.token), themeConfig.token),\n      components: mergedComponents,\n      cssVar: mergedCssVar\n    });\n  }, [themeConfig, parentThemeConfig], (prev, next) => prev.some((prevTheme, index) => {\n    const nextTheme = next[index];\n    return !isEqual(prevTheme, nextTheme, true);\n  }));\n}", "map": {"version": 3, "names": ["useMemo", "isEqual", "devUseW<PERSON>ning", "defaultConfig", "useThemeKey", "useTheme", "theme", "parentTheme", "config", "_a", "_b", "warning", "themeConfig", "parentThemeConfig", "inherit", "Object", "assign", "hashed", "cssVar", "<PERSON><PERSON><PERSON>", "process", "env", "NODE_ENV", "cssVarEnabled", "<PERSON><PERSON><PERSON>", "key", "mergedComponents", "components", "keys", "for<PERSON>ach", "componentName", "cssVarKey", "replace", "mergedCssVar", "prefix", "prefixCls", "token", "prev", "next", "some", "prevTheme", "index", "nextTheme"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/antd/es/config-provider/hooks/useTheme.js"], "sourcesContent": ["import useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport { devUseWarning } from '../../_util/warning';\nimport { defaultConfig } from '../../theme/internal';\nimport useThemeKey from './useThemeKey';\nexport default function useTheme(theme, parentTheme, config) {\n  var _a, _b;\n  const warning = devUseWarning('ConfigProvider');\n  const themeConfig = theme || {};\n  const parentThemeConfig = themeConfig.inherit === false || !parentTheme ? Object.assign(Object.assign({}, defaultConfig), {\n    hashed: (_a = parentTheme === null || parentTheme === void 0 ? void 0 : parentTheme.hashed) !== null && _a !== void 0 ? _a : defaultConfig.hashed,\n    cssVar: parentTheme === null || parentTheme === void 0 ? void 0 : parentTheme.cssVar\n  }) : parentTheme;\n  const themeKey = useThemeKey();\n  if (process.env.NODE_ENV !== 'production') {\n    const cssVarEnabled = themeConfig.cssVar || parentThemeConfig.cssVar;\n    const validKey = !!(typeof themeConfig.cssVar === 'object' && ((_b = themeConfig.cssVar) === null || _b === void 0 ? void 0 : _b.key) || themeKey);\n    process.env.NODE_ENV !== \"production\" ? warning(!cssVarEnabled || validKey, 'breaking', 'Missing key in `cssVar` config. Please upgrade to React 18 or set `cssVar.key` manually in each ConfigProvider inside `cssVar` enabled ConfigProvider.') : void 0;\n  }\n  return useMemo(() => {\n    var _a, _b;\n    if (!theme) {\n      return parentTheme;\n    }\n    // Override\n    const mergedComponents = Object.assign({}, parentThemeConfig.components);\n    Object.keys(theme.components || {}).forEach(componentName => {\n      mergedComponents[componentName] = Object.assign(Object.assign({}, mergedComponents[componentName]), theme.components[componentName]);\n    });\n    const cssVarKey = `css-var-${themeKey.replace(/:/g, '')}`;\n    const mergedCssVar = ((_a = themeConfig.cssVar) !== null && _a !== void 0 ? _a : parentThemeConfig.cssVar) && Object.assign(Object.assign(Object.assign({\n      prefix: config === null || config === void 0 ? void 0 : config.prefixCls\n    }, typeof parentThemeConfig.cssVar === 'object' ? parentThemeConfig.cssVar : {}), typeof themeConfig.cssVar === 'object' ? themeConfig.cssVar : {}), {\n      key: typeof themeConfig.cssVar === 'object' && ((_b = themeConfig.cssVar) === null || _b === void 0 ? void 0 : _b.key) || cssVarKey\n    });\n    // Base token\n    return Object.assign(Object.assign(Object.assign({}, parentThemeConfig), themeConfig), {\n      token: Object.assign(Object.assign({}, parentThemeConfig.token), themeConfig.token),\n      components: mergedComponents,\n      cssVar: mergedCssVar\n    });\n  }, [themeConfig, parentThemeConfig], (prev, next) => prev.some((prevTheme, index) => {\n    const nextTheme = next[index];\n    return !isEqual(prevTheme, nextTheme, true);\n  }));\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,WAAW,MAAM,eAAe;AACvC,eAAe,SAASC,QAAQA,CAACC,KAAK,EAAEC,WAAW,EAAEC,MAAM,EAAE;EAC3D,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAMC,OAAO,GAAGT,aAAa,CAAC,gBAAgB,CAAC;EAC/C,MAAMU,WAAW,GAAGN,KAAK,IAAI,CAAC,CAAC;EAC/B,MAAMO,iBAAiB,GAAGD,WAAW,CAACE,OAAO,KAAK,KAAK,IAAI,CAACP,WAAW,GAAGQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,aAAa,CAAC,EAAE;IACxHc,MAAM,EAAE,CAACR,EAAE,GAAGF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACU,MAAM,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGN,aAAa,CAACc,MAAM;IACjJC,MAAM,EAAEX,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACW;EAChF,CAAC,CAAC,GAAGX,WAAW;EAChB,MAAMY,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,IAAIgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,aAAa,GAAGX,WAAW,CAACM,MAAM,IAAIL,iBAAiB,CAACK,MAAM;IACpE,MAAMM,QAAQ,GAAG,CAAC,EAAE,OAAOZ,WAAW,CAACM,MAAM,KAAK,QAAQ,KAAK,CAACR,EAAE,GAAGE,WAAW,CAACM,MAAM,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,GAAG,CAAC,IAAIN,QAAQ,CAAC;IAClJC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,OAAO,CAAC,CAACY,aAAa,IAAIC,QAAQ,EAAE,UAAU,EAAE,wJAAwJ,CAAC,GAAG,KAAK,CAAC;EAC5P;EACA,OAAOxB,OAAO,CAAC,MAAM;IACnB,IAAIS,EAAE,EAAEC,EAAE;IACV,IAAI,CAACJ,KAAK,EAAE;MACV,OAAOC,WAAW;IACpB;IACA;IACA,MAAMmB,gBAAgB,GAAGX,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,iBAAiB,CAACc,UAAU,CAAC;IACxEZ,MAAM,CAACa,IAAI,CAACtB,KAAK,CAACqB,UAAU,IAAI,CAAC,CAAC,CAAC,CAACE,OAAO,CAACC,aAAa,IAAI;MAC3DJ,gBAAgB,CAACI,aAAa,CAAC,GAAGf,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEU,gBAAgB,CAACI,aAAa,CAAC,CAAC,EAAExB,KAAK,CAACqB,UAAU,CAACG,aAAa,CAAC,CAAC;IACtI,CAAC,CAAC;IACF,MAAMC,SAAS,GAAG,WAAWZ,QAAQ,CAACa,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE;IACzD,MAAMC,YAAY,GAAG,CAAC,CAACxB,EAAE,GAAGG,WAAW,CAACM,MAAM,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGI,iBAAiB,CAACK,MAAM,KAAKH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;MACtJkB,MAAM,EAAE1B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC2B;IACjE,CAAC,EAAE,OAAOtB,iBAAiB,CAACK,MAAM,KAAK,QAAQ,GAAGL,iBAAiB,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,OAAON,WAAW,CAACM,MAAM,KAAK,QAAQ,GAAGN,WAAW,CAACM,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;MACnJO,GAAG,EAAE,OAAOb,WAAW,CAACM,MAAM,KAAK,QAAQ,KAAK,CAACR,EAAE,GAAGE,WAAW,CAACM,MAAM,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,GAAG,CAAC,IAAIM;IAC5H,CAAC,CAAC;IACF;IACA,OAAOhB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,iBAAiB,CAAC,EAAED,WAAW,CAAC,EAAE;MACrFwB,KAAK,EAAErB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,iBAAiB,CAACuB,KAAK,CAAC,EAAExB,WAAW,CAACwB,KAAK,CAAC;MACnFT,UAAU,EAAED,gBAAgB;MAC5BR,MAAM,EAAEe;IACV,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrB,WAAW,EAAEC,iBAAiB,CAAC,EAAE,CAACwB,IAAI,EAAEC,IAAI,KAAKD,IAAI,CAACE,IAAI,CAAC,CAACC,SAAS,EAAEC,KAAK,KAAK;IACnF,MAAMC,SAAS,GAAGJ,IAAI,CAACG,KAAK,CAAC;IAC7B,OAAO,CAACxC,OAAO,CAACuC,SAAS,EAAEE,SAAS,EAAE,IAAI,CAAC;EAC7C,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}