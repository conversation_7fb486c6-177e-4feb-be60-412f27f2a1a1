{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport StopTwoToneSvg from \"@ant-design/icons-svg/es/asn/StopTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar StopTwoTone = function StopTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: StopTwoToneSvg\n  }));\n};\n\n/**![stop](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0yODguNSA2ODIuOEwyNzcuNyAyMjRDMjU4IDI0MCAyNDAgMjU4IDIyNCAyNzcuN2w1MjIuOCA1MjIuOEM2ODIuOCA4NTIuNyA2MDEgODg0IDUxMiA4ODRjLTIwNS40IDAtMzcyLTE2Ni42LTM3Mi0zNzJzMTY2LjYtMzcyIDM3Mi0zNzIgMzcyIDE2Ni42IDM3MiAzNzJjMCA4OS0zMS4zIDE3MC44LTgzLjUgMjM0Ljh6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik01MTIgMTQwYy0yMDUuNCAwLTM3MiAxNjYuNi0zNzIgMzcyczE2Ni42IDM3MiAzNzIgMzcyYzg5IDAgMTcwLjgtMzEuMyAyMzQuOC04My41TDIyNCAyNzcuN2MxNi0xOS43IDM0LTM3LjcgNTMuNy01My43bDUyMi44IDUyMi44Qzg1Mi43IDY4Mi44IDg4NCA2MDEgODg0IDUxMmMwLTIwNS40LTE2Ni42LTM3Mi0zNzItMzcyeiIgZmlsbD0iI2U2ZjRmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(StopTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'StopTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "StopTwoToneSvg", "AntdIcon", "StopTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/StopTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport StopTwoToneSvg from \"@ant-design/icons-svg/es/asn/StopTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar StopTwoTone = function StopTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: StopTwoToneSvg\n  }));\n};\n\n/**![stop](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0yODguNSA2ODIuOEwyNzcuNyAyMjRDMjU4IDI0MCAyNDAgMjU4IDIyNCAyNzcuN2w1MjIuOCA1MjIuOEM2ODIuOCA4NTIuNyA2MDEgODg0IDUxMiA4ODRjLTIwNS40IDAtMzcyLTE2Ni42LTM3Mi0zNzJzMTY2LjYtMzcyIDM3Mi0zNzIgMzcyIDE2Ni42IDM3MiAzNzJjMCA4OS0zMS4zIDE3MC44LTgzLjUgMjM0Ljh6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik01MTIgMTQwYy0yMDUuNCAwLTM3MiAxNjYuNi0zNzIgMzcyczE2Ni42IDM3MiAzNzIgMzcyYzg5IDAgMTcwLjgtMzEuMyAyMzQuOC04My41TDIyNCAyNzcuN2MxNi0xOS43IDM0LTM3LjcgNTMuNy01My43bDUyMi44IDUyMi44Qzg1Mi43IDY4Mi44IDg4NCA2MDEgODg0IDUxMmMwLTIwNS40LTE2Ni42LTM3Mi0zNzItMzcyeiIgZmlsbD0iI2U2ZjRmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(StopTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'StopTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}