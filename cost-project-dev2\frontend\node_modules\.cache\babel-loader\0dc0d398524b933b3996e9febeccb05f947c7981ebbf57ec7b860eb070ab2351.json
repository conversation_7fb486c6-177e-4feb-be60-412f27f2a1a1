{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ProjectTwoToneSvg from \"@ant-design/icons-svg/es/asn/ProjectTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ProjectTwoTone = function ProjectTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ProjectTwoToneSvg\n  }));\n};\n\n/**![project](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptNDcyLTU2MGMwLTQuNCAzLjYtOCA4LThoODBjNC40IDAgOCAzLjYgOCA4djI1NmMwIDQuNC0zLjYgOC04IDhoLTgwYy00LjQgMC04LTMuNi04LThWMjgwem0tMTkyIDBjMC00LjQgMy42LTggOC04aDgwYzQuNCAwIDggMy42IDggOHYxODRjMCA0LjQtMy42IDgtOCA4aC04MGMtNC40IDAtOC0zLjYtOC04VjI4MHptLTE5MiAwYzAtNC40IDMuNi04IDgtOGg4MGM0LjQgMCA4IDMuNiA4IDh2NDY0YzAgNC40LTMuNiA4LTggOGgtODBjLTQuNCAwLTgtMy42LTgtOFYyODB6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik0yODAgNzUyaDgwYzQuNCAwIDgtMy42IDgtOFYyODBjMC00LjQtMy42LTgtOC04aC04MGMtNC40IDAtOCAzLjYtOCA4djQ2NGMwIDQuNCAzLjYgOCA4IDh6bTE5Mi0yODBoODBjNC40IDAgOC0zLjYgOC04VjI4MGMwLTQuNC0zLjYtOC04LThoLTgwYy00LjQgMC04IDMuNi04IDh2MTg0YzAgNC40IDMuNiA4IDggOHptMTkyIDcyaDgwYzQuNCAwIDgtMy42IDgtOFYyODBjMC00LjQtMy42LTgtOC04aC04MGMtNC40IDAtOCAzLjYtOCA4djI1NmMwIDQuNCAzLjYgOCA4IDh6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ProjectTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ProjectTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "ProjectTwoToneSvg", "AntdIcon", "ProjectTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/dev/cost-project-dev2/frontend/node_modules/@ant-design/icons/es/icons/ProjectTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ProjectTwoToneSvg from \"@ant-design/icons-svg/es/asn/ProjectTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ProjectTwoTone = function ProjectTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ProjectTwoToneSvg\n  }));\n};\n\n/**![project](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptNDcyLTU2MGMwLTQuNCAzLjYtOCA4LThoODBjNC40IDAgOCAzLjYgOCA4djI1NmMwIDQuNC0zLjYgOC04IDhoLTgwYy00LjQgMC04LTMuNi04LThWMjgwem0tMTkyIDBjMC00LjQgMy42LTggOC04aDgwYzQuNCAwIDggMy42IDggOHYxODRjMCA0LjQtMy42IDgtOCA4aC04MGMtNC40IDAtOC0zLjYtOC04VjI4MHptLTE5MiAwYzAtNC40IDMuNi04IDgtOGg4MGM0LjQgMCA4IDMuNiA4IDh2NDY0YzAgNC40LTMuNiA4LTggOGgtODBjLTQuNCAwLTgtMy42LTgtOFYyODB6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik0yODAgNzUyaDgwYzQuNCAwIDgtMy42IDgtOFYyODBjMC00LjQtMy42LTgtOC04aC04MGMtNC40IDAtOCAzLjYtOCA4djQ2NGMwIDQuNCAzLjYgOCA4IDh6bTE5Mi0yODBoODBjNC40IDAgOC0zLjYgOC04VjI4MGMwLTQuNC0zLjYtOC04LThoLTgwYy00LjQgMC04IDMuNi04IDh2MTg0YzAgNC40IDMuNiA4IDggOHptMTkyIDcyaDgwYzQuNCAwIDgtMy42IDgtOFYyODBjMC00LjQtMy42LTgtOC04aC04MGMtNC40IDAtOCAzLjYtOCA4djI1NmMwIDQuNCAzLjYgOCA4IDh6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ProjectTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ProjectTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,cAAc,CAAC;AAC3D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,gBAAgB;AACxC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}